/**
 * 地图缩放控制功能使用示例
 */
import { useMap } from '@/hooks/web/useMap';

export function zoomControlExample() {
  // 初始化地图
  const { 
    initializeMap, 
    setZoom,
    getMapInstance,
    provider
  } = useMap({
    center: [116.397428, 39.90923],
    zoom: 13,
  });

  async function demo() {
    // 初始化地图
    await initializeMap('map-container', {
      center: [116.397428, 39.90923],
      zoom: 13,
    });

    console.log('当前地图提供商:', provider.value);

    // 基本缩放操作示例
    setTimeout(() => {
      console.log('放大到 16 级');
      setZoom(16);
    }, 2000);

    setTimeout(() => {
      console.log('缩小到 10 级');
      setZoom(10);
    }, 4000);

    // 高德地图特有的动画缩放（Google Maps 会忽略这些参数）
    setTimeout(() => {
      console.log('使用动画缩放到 15 级（仅高德地图支持动画参数）');
      setZoom(15, false, 1000); // 1秒动画时长
    }, 6000);

    // 立即缩放（无动画）
    setTimeout(() => {
      console.log('立即缩放到 12 级');
      setZoom(12, true); // 立即缩放
    }, 8000);

    // 边界测试
    setTimeout(() => {
      console.log('测试缩放边界');
      
      // 高德地图支持的范围 [2, 30]
      // Google Maps 支持的范围 [1, 20]
      if (provider.value === 'amap') {
        console.log('高德地图 - 缩放到最大级别 30');
        setZoom(30);
      } else {
        console.log('Google Maps - 缩放到最大级别 20');
        setZoom(20);
      }
    }, 10000);

    setTimeout(() => {
      if (provider.value === 'amap') {
        console.log('高德地图 - 缩放到最小级别 2');
        setZoom(2);
      } else {
        console.log('Google Maps - 缩放到最小级别 1');
        setZoom(1);
      }
    }, 12000);

    // 错误处理示例
    setTimeout(() => {
      console.log('测试无效缩放级别');
      const result = setZoom(100); // 超出范围
      console.log('缩放结果:', result); // 应该返回 false
    }, 14000);
  }

  /**
   * 创建缩放控制按钮的示例
   */
  function createZoomControls() {
    return {
      zoomIn: () => {
        // 获取当前缩放级别并放大
        const mapInstance = getMapInstance();
        if (mapInstance) {
          if (provider.value === 'amap') {
            const currentZoom = (mapInstance as AMap.Map).getZoom();
            setZoom(Math.min(currentZoom + 1, 30));
          } else {
            const currentZoom = (mapInstance as google.maps.Map).getZoom() || 13;
            setZoom(Math.min(currentZoom + 1, 20));
          }
        }
      },
      
      zoomOut: () => {
        // 获取当前缩放级别并缩小
        const mapInstance = getMapInstance();
        if (mapInstance) {
          if (provider.value === 'amap') {
            const currentZoom = (mapInstance as AMap.Map).getZoom();
            setZoom(Math.max(currentZoom - 1, 2));
          } else {
            const currentZoom = (mapInstance as google.maps.Map).getZoom() || 13;
            setZoom(Math.max(currentZoom - 1, 1));
          }
        }
      },
      
      resetZoom: () => {
        // 重置到默认缩放级别
        setZoom(13);
      },
      
      setCustomZoom: (level: number) => {
        // 设置自定义缩放级别
        return setZoom(level);
      }
    };
  }

  return {
    demo,
    createZoomControls,
    setZoom,
  };
}

/**
 * 使用方式：
 * 
 * 1. 在 Vue 组件中使用：
 * ```vue
 * <template>
 *   <div>
 *     <div id="map-container" style="width: 100%; height: 400px;"></div>
 *     <div class="zoom-controls">
 *       <button @click="zoomIn">放大</button>
 *       <button @click="zoomOut">缩小</button>
 *       <button @click="resetZoom">重置</button>
 *       <input 
 *         type="range" 
 *         :min="minZoom" 
 *         :max="maxZoom" 
 *         v-model="currentZoom" 
 *         @input="handleZoomChange"
 *       />
 *     </div>
 *   </div>
 * </template>
 * 
 * <script setup>
 * import { ref, computed } from 'vue';
 * import { zoomControlExample } from '@/examples/zoom-control-example';
 * import { useMap } from '@/hooks/web/useMap';
 * 
 * const { demo, createZoomControls } = zoomControlExample();
 * const { provider } = useMap({});
 * 
 * const currentZoom = ref(13);
 * const { zoomIn, zoomOut, resetZoom, setCustomZoom } = createZoomControls();
 * 
 * // 根据地图提供商设置缩放范围
 * const minZoom = computed(() => provider.value === 'amap' ? 2 : 1);
 * const maxZoom = computed(() => provider.value === 'amap' ? 30 : 20);
 * 
 * const handleZoomChange = () => {
 *   setCustomZoom(currentZoom.value);
 * };
 * 
 * const runDemo = () => {
 *   demo();
 * };
 * </script>
 * ```
 * 
 * 2. setZoom 方法参数说明：
 * - zoom: 缩放级别
 *   - 高德地图：[2, 30]
 *   - Google Maps：[1, 20]
 * - immediately: 是否立即缩放（仅高德地图支持）
 * - duration: 动画时长，单位 ms（仅高德地图支持）
 * 
 * 3. 返回值：
 * - boolean: 缩放操作是否成功
 * 
 * 4. 特性：
 * - 🎯 统一的缩放接口，自动适配不同地图提供商
 * - 🔄 自动参数验证和边界检查
 * - ⚡ 高德地图支持动画缩放，Google Maps 自动忽略不支持的参数
 * - 🛡️ 错误处理和日志记录
 * - 📱 适用于各种缩放控制场景
 */
