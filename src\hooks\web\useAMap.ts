import { ref, unref, onMounted, onUnmounted } from 'vue';
import Car from '../../assets/images/equip/car.png';

export type AMapPoint = [number, number] | AMap.LngLat;

// 地图边界信息接口
export type MapBoundary = {
  maxLat: number;
  maxLng: number;
  minLat: number;
  minLng: number;
};

// 地理编码结果接口
export interface GeocodeResult {
  status: string;
  regeocode?: {
    formattedAddress: string;
  };
}

// 控件类型枚举
export enum AMapControlType {
  TOOLBAR = 'ToolBar',
  CONTROLBAR = 'ControlBar',
  HAWKEYE = 'HawkEye',
  MAPTYPE = 'MapType',
  SCALE = 'Scale',
  OVERVIEW = 'OverView',
}

// 控件配置接口
export interface AMapControlConfig {
  type: AMapControlType;
  options?: any;
  position?: {
    top?: string;
    bottom?: string;
    left?: string;
    right?: string;
  };
}

// 预定义的控件配置
export const DEFAULT_CONTROL_CONFIGS: Record<string, AMapControlConfig> = {
  toolbar: {
    type: AMapControlType.TOOLBAR,
    options: {},
    position: {
      top: '160px',
      right: '40px',
    },
  },
  controlbar: {
    type: AMapControlType.CONTROLBAR,
    options: {},
    position: {
      top: '20px',
      right: '150px',
    },
  },
  hawkeye: {
    type: AMapControlType.HAWKEYE,
    options: {},
  },
  maptype: {
    type: AMapControlType.MAPTYPE,
    options: {
      defaultType: 0, // 0代表默认，1代表卫星
    },
  },
  scale: {
    type: AMapControlType.SCALE,
    options: {},
  },
};

/**
 * @description 高德地图hook，便于管理地图加载、创建和销毁。
 * @param {AMap.MapOptions} options 高德地图实例化参数
 * @param {Function} callback 地图初始化完成后的回调函数
 */
export function useAMap(options: AMap.MapOptions, callback?: (map: AMap.Map) => void) {
  const map = ref<AMap.Map | undefined>();
  const mapRef = ref<HTMLDivElement | undefined>();
  const currentMarker = ref<AMap.Marker | null>(null);
  const geocoder = ref<any>();
  const isInitialized = ref(false);
  const controls = ref<Map<string, any>>(new Map());

  /**
   * 初始化地图实例的核心逻辑
   */
  function initializeMap(): boolean {
    const container = unref(mapRef);
    if (!container) {
      console.error('Map container ref is not available');
      return false;
    }

    if (isInitialized.value) {
      console.warn('Map is already initialized');
      return true;
    }

    try {
      // 销毁现有实例（如果存在）
      if (map.value) {
        map.value.destroy();
      }

      map.value = new AMap.Map(container, options);

      // 初始化地理编码器
      geocoder.value = new (window as any).AMap.Geocoder({
        city: '全国',
        radius: 1000,
      });

      isInitialized.value = true;

      if (typeof callback === 'function') {
        callback(map.value);
      }

      return true;
    } catch (error) {
      console.error('Failed to initialize AMap:', error);
      isInitialized.value = false;
      return false;
    }
  }

  // dom挂载完成后初始化map实例
  onMounted(() => {
    // 先加载动画插件
    AMap.plugin(['AMap.MoveAnimation'], () => {
      // 插件加载完成后初始化地图
      console.log('map ref : ', unref(mapRef));
      if (unref(mapRef)) {
        map.value = new AMap.Map(unref(mapRef)!, options);
        if (typeof callback === 'function') callback(unref(map)!);
      }
    });

    if (unref(mapRef)) {
      initializeMap();
    }
  });

  /**
   * 手动初始化地图
   */
  function init(): boolean {
    return initializeMap();
  }

  /**
   * 销毁地图实例和相关资源
   */
  function destroy(): void {
    try {
      if (map.value) {
        map.value.destroy();
        map.value = undefined;
      }
      geocoder.value = undefined;
      isInitialized.value = false;
    } catch (error) {
      console.error('Failed to destroy AMap:', error);
    }
  }

  // 组件卸载时销毁地图
  onUnmounted(() => {
    stopMoveAnimation();
    destroy();
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * @description 设置中心点
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenter(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.setCenter(lngLat);
      return true;
    } catch (error) {
      console.error('Failed to set map center:', error);
      return false;
    }
  }

  /**
   * @description 设置中心点并自适应视野
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenterFitView(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.setCenter(lngLat);
      mapInstance.setFitView();
      return true;
    } catch (error) {
      console.error('Failed to set map center and fit view:', error);
      return false;
    }
  }

  /**
   * @description 设置地图缩放级别
   * @param zoom 缩放级别，范围 [2, 30]
   * @param immediately 是否立即过渡到目标位置，默认 false
   * @param duration 动画过渡时长，单位 ms
   */
  function setZoom(zoom: number, immediately: boolean = false, duration?: number): boolean {
    if (typeof zoom !== 'number' || zoom < 2 || zoom > 30) {
      console.error('Invalid zoom level provided:', zoom, 'Expected range: [2, 30]');
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.setZoom(zoom, immediately, duration);
      return true;
    } catch (error) {
      console.error('Failed to set zoom level:', error);
      return false;
    }
  }

  /**
   * 验证位置参数是否有效
   */
  function validatePosition(position: AMapPoint): boolean {
    if (Array.isArray(position)) {
      return validateCoordinates(position);
    }
    // 如果是AMap.LngLat对象，检查其属性
    if (position && typeof position === 'object' && 'lng' in position && 'lat' in position) {
      return validateCoordinates([position.lng, position.lat]);
    }
    return false;
  }

  /**
   * 添加标记点
   * @param position 位置坐标
   * @param options 标记选项
   */
  function addMarker(position: AMapPoint, options: AMap.MarkerOptions = {}): AMap.Marker | null {
    if (!validatePosition(position)) {
      console.error('Invalid position provided:', position);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const marker = new AMap.Marker({
        position,
        ...options,
      });
      mapInstance.add(marker);
      return marker;
    } catch (error) {
      console.error('Failed to add marker:', error);
      return null;
    }
  }

  /**
   * 添加折线
   * @param points 路径点数组
   * @param options 折线选项
   */
  function addPolyline(
    points: AMapPoint[],
    options: Partial<AMap.PolylineOptions> = {},
  ): AMap.Polyline | null {
    if (!Array.isArray(points) || points.length < 2) {
      console.error('Invalid points array provided, at least 2 points required');
      return null;
    }

    // 验证所有点的有效性
    const invalidPoints = points.filter((point) => !validatePosition(point));
    if (invalidPoints.length > 0) {
      console.error('Invalid points found:', invalidPoints);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const polyline = new AMap.Polyline({
        path: points,
        borderWeight: 10,
        strokeColor: '#4d78bc',
        ...options,
      });
      mapInstance.add(polyline);
      return polyline;
    } catch (error) {
      console.error('Failed to add polyline:', error);
      return null;
    }
  }

  /**
   * 获取地图实例
   */
  function getMapInstance(): AMap.Map | undefined {
    return unref(map);
  }

  /**
   * 清空地图上的所有覆盖物
   */
  function clearMap(): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.clearMap();
      return true;
    } catch (error) {
      console.error('Failed to clear map:', error);
      return false;
    }
  }

  /**
   * 获取地图边界信息
   */
  function getMapBoundary(): MapBoundary | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const bounds = mapInstance.getBounds();
      if (!bounds || !bounds.northEast || !bounds.southWest) {
        console.error('Invalid bounds returned from map');
        return null;
      }

      return {
        maxLat: bounds.northEast.lat,
        maxLng: bounds.northEast.lng,
        minLat: bounds.southWest.lat,
        minLng: bounds.southWest.lng,
      };
    } catch (error) {
      console.error('Failed to get map boundary:', error);
      return null;
    }
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: AMapPoint): Promise<string> {
    if (!validatePosition(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise((resolve, reject) => {
      try {
        const coordinates = Array.isArray(lngLat) ? lngLat : [lngLat.lng, lngLat.lat];
        geocoderInstance.getAddress(
          new (window as any).AMap.LngLat(coordinates[0], coordinates[1]),
          (status: string, result: GeocodeResult) => {
            if (status === 'complete' && result.regeocode) {
              resolve(result.regeocode.formattedAddress || '');
            } else if (status === 'no_data') {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 手动触发地图对象的事件
   * @param target 目标对象（地图、marker、polyline 等）
   * @param eventType 事件类型
   * @param eventData 事件数据（可选）
   */
  function triggerEvent(
    target: AMap.Map | AMap.Marker | AMap.Polyline | AMap.Polygon | AMap.Circle | any,
    eventType: string,
    eventData?: any,
  ): boolean {
    if (!target) {
      console.error('Target object is not available');
      return false;
    }

    try {
      // 使用 AMap.Event.trigger 手动触发事件
      AMap.Event.trigger(target, eventType, eventData);
      return true;
    } catch (error) {
      console.error('Failed to trigger event:', error);
      return false;
    }
  }

  /**
   * 为地图对象添加事件监听器（通用方法）
   * @param target 目标对象（地图、marker、polyline、polygon、circle 等）
   * @param eventType 事件类型
   * @param handler 事件处理函数
   */
  function addEventListener(
    target: AMap.Map | AMap.Marker | AMap.Polyline | AMap.Polygon | AMap.Circle | any,
    eventType: string,
    handler: (event: any) => void,
  ): void {
    if (!target) {
      console.error('Target object is not available');
      return;
    }

    try {
      target.on(eventType, handler);
    } catch (error) {
      console.error('Failed to add event listener:', error);
    }
  }

  /**
   * 移除地图对象的事件监听器（通用方法）
   * @param target 目标对象（地图、marker、polyline、polygon、circle 等）
   * @param eventType 事件类型
   * @param handler 事件处理函数（可选，不传则移除该事件类型的所有监听器）
   */
  function removeEventListener(
    target: AMap.Map | AMap.Marker | AMap.Polyline | AMap.Polygon | AMap.Circle | any,
    eventType: string,
    handler?: (event: any) => void,
  ): void {
    if (!target) {
      console.error('Target object is not available');
      return;
    }

    try {
      if (handler) {
        target.off(eventType, handler);
      } else {
        // 移除所有该类型的事件监听器
        target.off(eventType, () => {});
      }
    } catch (error) {
      console.error('Failed to remove event listener:', error);
    }
  }

  /**
   * 创建信息窗口
   * @param options 信息窗口选项
   */
  function createInfoWindow(options: any = {}): AMap.InfoWindow | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const infoWindow = new AMap.InfoWindow({
        anchor: 'bottom-center',
        map: mapInstance,
        ...options,
      });
      return infoWindow;
    } catch (error) {
      console.error('Failed to create info window:', error);
      return null;
    }
  }

  /**
   * 添加地图控件
   * @param controlId 控件唯一标识
   * @param config 控件配置
   */
  function addControl(controlId: string, config: AMapControlConfig): Promise<any> {
    return new Promise((resolve, reject) => {
      const mapInstance = unref(map);
      if (!mapInstance) {
        reject(new Error('Map instance is not available'));
        return;
      }

      // 如果控件已存在，先移除
      if (controls.value.has(controlId)) {
        removeControl(controlId);
      }

      const pluginName = `AMap.${config.type}`;

      (window as any).AMap.plugin([pluginName], () => {
        try {
          const ControlClass = (window as any).AMap[config.type];
          if (!ControlClass) {
            reject(new Error(`Control class ${config.type} not found`));
            return;
          }

          const controlOptions = { ...config.options };
          if (config.position) {
            controlOptions.position = config.position;
          }

          const control = new ControlClass(controlOptions);
          mapInstance.addControl(control);
          controls.value.set(controlId, control);

          console.log(`Control ${controlId} (${config.type}) added successfully`);
          resolve(control);
        } catch (error) {
          console.error(`Failed to add control ${controlId}:`, error);
          reject(error);
        }
      });
    });
  }

  /**
   * 移除地图控件
   * @param controlId 控件唯一标识
   */
  function removeControl(controlId: string): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    const control = controls.value.get(controlId);
    if (!control) {
      console.warn(`Control ${controlId} not found`);
      return false;
    }

    try {
      mapInstance.removeControl(control);
      controls.value.delete(controlId);
      console.log(`Control ${controlId} removed successfully`);
      return true;
    } catch (error) {
      console.error(`Failed to remove control ${controlId}:`, error);
      return false;
    }
  }

  /**
   * 添加预定义的控件
   * @param controlName 预定义控件名称
   * @param customOptions 自定义选项（可选）
   */
  function addPredefinedControl(
    controlName: keyof typeof DEFAULT_CONTROL_CONFIGS,
    customOptions?: Partial<AMapControlConfig>,
  ): Promise<any> {
    const defaultConfig = DEFAULT_CONTROL_CONFIGS[controlName];
    if (!defaultConfig) {
      return Promise.reject(new Error(`Predefined control ${controlName} not found`));
    }

    const config: AMapControlConfig = {
      ...defaultConfig,
      ...customOptions,
      options: {
        ...defaultConfig.options,
        ...customOptions?.options,
      },
    };

    return addControl(controlName, config);
  }

  /**
   * 批量添加控件
   * @param controlConfigs 控件配置数组
   */
  async function addMultipleControls(
    controlConfigs: Array<{ id: string; config: AMapControlConfig }>,
  ): Promise<Array<{ id: string; control?: any; error?: any; success: boolean }>> {
    const results: Array<{ id: string; control?: any; error?: any; success: boolean }> = [];
    for (const { id, config } of controlConfigs) {
      try {
        const control = await addControl(id, config);
        results.push({ id, control, success: true });
      } catch (error) {
        console.error(`Failed to add control ${id}:`, error);
        results.push({ id, error, success: false });
      }
    }
    return results;
  }

  /**
   * 清除所有控件
   */
  function clearAllControls(): void {
    const controlIds = Array.from(controls.value.keys());
    controlIds.forEach((id) => removeControl(id));
  }

  /**
   * 获取已添加的控件列表
   */
  function getControls(): Map<string, any> {
    return new Map(controls.value);
  }

  const CAR_ICON = new AMap.Icon({
    image: Car,
    imageSize: new AMap.Size(30, 30),
    size: new AMap.Size(30, 30),
    offset: new AMap.Pixel(-15, -15),
  });

  /**
   * @description 开始轨迹动画
   * @param points 轨迹点数组
   * @param speed 移动速度（千米/小时），默认 100
   * @param options 动画配置选项
   */
  function startMoveAnimation(
    points: AMapPoint[],
    options: {
      speed?: number;
      loop?: boolean;
      onStart?: () => void;
      onMoving?: (e: any) => void; // 新增一个移动中的回调
      onEnd?: () => void;
    } = {},
  ) {
    // 清除旧marker和动画
    stopMoveAnimation();

    // 创建移动marker
    currentMarker.value = new AMap.Marker({
      icon: CAR_ICON,
      position: points[0],
      offset: new AMap.Pixel(-15, -15),
      autoRotation: true,
    });
    unref(map)?.add(currentMarker.value);

    if (currentMarker.value) {
      currentMarker.value.on('moving', (e: any) => {
        options.onMoving?.(e);
      });
      currentMarker.value.on('stop', () => {
        console.log('动画结束');

        if (!options.loop) {
          options.onEnd?.();
        }
      });
    }

    // 执行移动动画
    if (currentMarker.value) {
      currentMarker.value.moveAlong(points, {
        duration: 200 * (100 / (options.speed || 100)),
        autoRotation: true,
        circulate: options.loop || false,
        speed: options.speed || 100,
      });

      // 立即调用 onStart 回调
      console.log('动画指令已发出，开始移动');
      options.onStart?.();
    }
  }

  /**
   * @description 停止轨迹动画
   */
  function stopMoveAnimation() {
    if (currentMarker.value) {
      // 使用 Marker 实例上的 stopMove 方法
      currentMarker.value.stopMove();
      unref(map)?.remove(currentMarker.value);
      currentMarker.value = null;
    }
  }

  return {
    mapRef,
    map,
    setMapCenter,
    setZoom,
    addMarker,
    addPolyline,
    getMapInstance,
    clearMap,
    getMapBoundary,
    geocoder,
    destroy,
    getAddress,
    createInfoWindow,
    init,
    setMapCenterFitView,
    isInitialized,
    validateCoordinates,
    validatePosition,
    // 事件管理方法
    addEventListener,
    removeEventListener,
    triggerEvent,
    // 控件管理方法
    addControl,
    removeControl,
    addPredefinedControl,
    addMultipleControls,
    clearAllControls,
    getControls,
    CAR_ICON,
    startMoveAnimation,
    stopMoveAnimation,
  };
}

/**
 * @description 获取高德地图geocoder
 */
export function useGeoCoder() {
  const geocoder = ref<any>();
  const isInitialized = ref(false);

  /**
   * 初始化地理编码器
   */
  function initGeocoder(): boolean {
    if (isInitialized.value) {
      return true;
    }

    try {
      // 使用any类型避免类型检查问题
      geocoder.value = new (window as any).AMap.Geocoder({
        city: '全国',
        radius: 1000,
      });
      isInitialized.value = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize geocoder:', error);
      isInitialized.value = false;
      return false;
    }
  }

  onMounted(() => {
    initGeocoder();
  });

  onUnmounted(() => {
    geocoder.value = undefined;
    isInitialized.value = false;
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: [number, number]): Promise<string> {
    if (!validateCoordinates(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise<string>((resolve, reject) => {
      try {
        geocoderInstance.getAddress(
          new (window as any).AMap.LngLat(lngLat[0], lngLat[1]),
          (status: string, result: GeocodeResult) => {
            if (status === 'complete' && result.regeocode) {
              resolve(result.regeocode.formattedAddress || '');
            } else if (status === 'no_data') {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  return {
    geocoder,
    getAddress,
    isInitialized,
    initGeocoder,
    validateCoordinates,
  };
}

export type SetMapCenter = (lngLat: [number, number]) => void;
