import { ref, unref, onMounted, onUnmounted } from 'vue';

export type AMapPoint = [number, number] | AMap.LngLat;

// 地图边界信息接口
export type MapBoundary = {
  maxLat: number;
  maxLng: number;
  minLat: number;
  minLng: number;
};

// 地理编码结果接口
export interface GeocodeResult {
  status: string;
  regeocode?: {
    formattedAddress: string;
  };
}

/**
 * @description 高德地图hook，便于管理地图加载、创建和销毁。
 * @param {AMap.MapOptions} options 高德地图实例化参数
 * @param {Function} callback 地图初始化完成后的回调函数
 */
export function useAMap(options: AMap.MapOptions, callback?: (map: AMap.Map) => void) {
  const map = ref<AMap.Map | undefined>();
  const mapRef = ref<HTMLDivElement | undefined>();
  const geocoder = ref<any>();
  const isInitialized = ref(false);

  /**
   * 初始化地图实例的核心逻辑
   */
  function initializeMap(): boolean {
    const container = unref(mapRef);
    if (!container) {
      console.error('Map container ref is not available');
      return false;
    }

    if (isInitialized.value) {
      console.warn('Map is already initialized');
      return true;
    }

    try {
      // 销毁现有实例（如果存在）
      if (map.value) {
        map.value.destroy();
      }

      map.value = new AMap.Map(container, options);

      // 初始化地理编码器
      geocoder.value = new (window as any).AMap.Geocoder({
        city: '全国',
        radius: 1000,
      });

      isInitialized.value = true;

      if (typeof callback === 'function') {
        callback(map.value);
      }

      return true;
    } catch (error) {
      console.error('Failed to initialize AMap:', error);
      isInitialized.value = false;
      return false;
    }
  }

  // dom挂载完成后初始化map实例
  onMounted(() => {
    if (unref(mapRef)) {
      initializeMap();
    }
  });

  /**
   * 手动初始化地图
   */
  function init(): boolean {
    return initializeMap();
  }

  /**
   * 销毁地图实例和相关资源
   */
  function destroy(): void {
    try {
      if (map.value) {
        map.value.destroy();
        map.value = undefined;
      }
      geocoder.value = undefined;
      isInitialized.value = false;
    } catch (error) {
      console.error('Failed to destroy AMap:', error);
    }
  }

  // 组件卸载时销毁地图
  onUnmounted(() => {
    destroy();
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * @description 设置中心点
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenter(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.setCenter(lngLat);
      return true;
    } catch (error) {
      console.error('Failed to set map center:', error);
      return false;
    }
  }

  /**
   * @description 设置中心点并自适应视野
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenterFitView(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.setCenter(lngLat);
      mapInstance.setFitView();
      return true;
    } catch (error) {
      console.error('Failed to set map center and fit view:', error);
      return false;
    }
  }

  /**
   * 验证位置参数是否有效
   */
  function validatePosition(position: AMapPoint): boolean {
    if (Array.isArray(position)) {
      return validateCoordinates(position);
    }
    // 如果是AMap.LngLat对象，检查其属性
    if (position && typeof position === 'object' && 'lng' in position && 'lat' in position) {
      return validateCoordinates([position.lng, position.lat]);
    }
    return false;
  }

  /**
   * 添加标记点
   * @param position 位置坐标
   * @param options 标记选项
   */
  function addMarker(position: AMapPoint, options: AMap.MarkerOptions = {}): AMap.Marker | null {
    if (!validatePosition(position)) {
      console.error('Invalid position provided:', position);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const marker = new AMap.Marker({
        position,
        ...options,
      });
      mapInstance.add(marker);
      return marker;
    } catch (error) {
      console.error('Failed to add marker:', error);
      return null;
    }
  }

  /**
   * 添加折线
   * @param points 路径点数组
   * @param options 折线选项
   */
  function addPolyline(
    points: AMapPoint[],
    options: Partial<AMap.PolylineOptions> = {},
  ): AMap.Polyline | null {
    if (!Array.isArray(points) || points.length < 2) {
      console.error('Invalid points array provided, at least 2 points required');
      return null;
    }

    // 验证所有点的有效性
    const invalidPoints = points.filter((point) => !validatePosition(point));
    if (invalidPoints.length > 0) {
      console.error('Invalid points found:', invalidPoints);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const polyline = new AMap.Polyline({
        path: points,
        borderWeight: 10,
        strokeColor: '#4d78bc',
        ...options,
      });
      mapInstance.add(polyline);
      return polyline;
    } catch (error) {
      console.error('Failed to add polyline:', error);
      return null;
    }
  }

  /**
   * 获取地图实例
   */
  function getMapInstance(): AMap.Map | undefined {
    return unref(map);
  }

  /**
   * 清空地图上的所有覆盖物
   */
  function clearMap(): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.clearMap();
      return true;
    } catch (error) {
      console.error('Failed to clear map:', error);
      return false;
    }
  }

  /**
   * 获取地图边界信息
   */
  function getMapBoundary(): MapBoundary | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const bounds = mapInstance.getBounds();
      if (!bounds || !bounds.northEast || !bounds.southWest) {
        console.error('Invalid bounds returned from map');
        return null;
      }

      return {
        maxLat: bounds.northEast.lat,
        maxLng: bounds.northEast.lng,
        minLat: bounds.southWest.lat,
        minLng: bounds.southWest.lng,
      };
    } catch (error) {
      console.error('Failed to get map boundary:', error);
      return null;
    }
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: AMapPoint): Promise<string> {
    if (!validatePosition(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise((resolve, reject) => {
      try {
        const coordinates = Array.isArray(lngLat) ? lngLat : [lngLat.lng, lngLat.lat];
        geocoderInstance.getAddress(
          new (window as any).AMap.LngLat(coordinates[0], coordinates[1]),
          (status: string, result: GeocodeResult) => {
            if (status === 'complete' && result.regeocode) {
              resolve(result.regeocode.formattedAddress || '');
            } else if (status === 'no_data') {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 创建信息窗口
   * @param options 信息窗口选项
   */
  function createInfoWindow(options: any = {}): AMap.InfoWindow | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const infoWindow = new AMap.InfoWindow({
        anchor: 'bottom-center',
        map: mapInstance,
        ...options,
      });
      return infoWindow;
    } catch (error) {
      console.error('Failed to create info window:', error);
      return null;
    }
  }

  return {
    mapRef,
    map,
    setMapCenter,
    addMarker,
    addPolyline,
    getMapInstance,
    clearMap,
    getMapBoundary,
    geocoder,
    destroy,
    getAddress,
    createInfoWindow,
    init,
    setMapCenterFitView,
    isInitialized,
    validateCoordinates,
    validatePosition,
  };
}

/**
 * @description 获取高德地图geocoder
 */
export function useGeoCoder() {
  const geocoder = ref<any>();
  const isInitialized = ref(false);

  /**
   * 初始化地理编码器
   */
  function initGeocoder(): boolean {
    if (isInitialized.value) {
      return true;
    }

    try {
      // 使用any类型避免类型检查问题
      geocoder.value = new (window as any).AMap.Geocoder({
        city: '全国',
        radius: 1000,
      });
      isInitialized.value = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize geocoder:', error);
      isInitialized.value = false;
      return false;
    }
  }

  onMounted(() => {
    initGeocoder();
  });

  onUnmounted(() => {
    geocoder.value = undefined;
    isInitialized.value = false;
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: [number, number]): Promise<string> {
    if (!validateCoordinates(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise<string>((resolve, reject) => {
      try {
        geocoderInstance.getAddress(
          new (window as any).AMap.LngLat(lngLat[0], lngLat[1]),
          (status: string, result: GeocodeResult) => {
            if (status === 'complete' && result.regeocode) {
              resolve(result.regeocode.formattedAddress || '');
            } else if (status === 'no_data') {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  return {
    geocoder,
    getAddress,
    isInitialized,
    initGeocoder,
    validateCoordinates,
  };
}

export type SetMapCenter = (lngLat: [number, number]) => void;
