import { ref, unref, onMounted, onUnmounted } from 'vue';

export type AMapPoint = [number, number] | AMap.LngLat;

/**
 * @description 高德地图hook，便于管理地图加载、创建和销毁。
 * @param {AMap.MapOptions} options 高德地图实例化参数
 */
export function useAMap(options: AMap.MapOptions, callback?: (map: AMap.Map) => void) {
  const map = ref<AMap.Map | undefined>();
  const mapRef = ref<HTMLDivElement | undefined>();
  const geocoder = ref<AMap.Geocoder | undefined>();

  // dom挂载完成后初始化map实例
  onMounted(() => {
    console.log('map ref : ', unref(mapRef));
    if (unref(mapRef)) {
      try {
        map.value = new AMap.Map(unref(mapRef)!, options);
        // 初始化地理编码器
        geocoder.value = new AMap.Geocoder({
          city: '全国',
          radius: 1000,
        });
        if (typeof callback === 'function') callback(unref(map)!);
      } catch (error) {
        console.error('Failed to initialize AMap:', error);
      }
    }
  });

  function init() {
    if (!unref(mapRef)) {
      console.error('Map container ref is not available');
      return;
    }
    try {
      map.value = new AMap.Map(unref(mapRef)!, options);
      // 初始化地理编码器
      geocoder.value = new AMap.Geocoder({
        city: '全国',
        radius: 1000,
      });
      if (typeof callback === 'function') callback(unref(map)!);
    } catch (error) {
      console.error('Failed to initialize AMap:', error);
    }
  }

  function destroy() {
    try {
      map.value?.destroy();
      geocoder.value = undefined;
      map.value = undefined;
    } catch (error) {
      console.error('Failed to destroy AMap:', error);
    }
  }

  // 组件卸载时销毁地图
  onUnmounted(() => {
    destroy();
  });

  /**
   * @description 设置中心点
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenter(lngLat: [number, number]) {
    const mapInstance = unref(map);
    if (mapInstance) {
      try {
        mapInstance.setCenter(lngLat);
      } catch (error) {
        console.error('Failed to set map center:', error);
      }
    }
  }

  /**
   * @description 设置中心点并自适应视野
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenterFitView(lngLat: [number, number]) {
    const mapInstance = unref(map);
    if (mapInstance) {
      try {
        mapInstance.setCenter(lngLat);
        mapInstance.setFitView();
      } catch (error) {
        console.error('Failed to set map center and fit view:', error);
      }
    }
  }

  function addMarker(position: AMapPoint, options: AMap.MarkerOptions = {}) {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const marker = new AMap.Marker({
        position,
        ...options,
      });
      mapInstance.add(marker);
      return marker;
    } catch (error) {
      console.error('Failed to add marker:', error);
      return null;
    }
  }

  function addPolyline(points: AMapPoint[]) {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const polyline = new AMap.Polyline({
        path: points,
        borderWeight: 10,
        strokeColor: '#4d78bc',
      });
      mapInstance.add(polyline);
      return polyline;
    } catch (error) {
      console.error('Failed to add polyline:', error);
      return null;
    }
  }

  function getMapInstance() {
    return unref(map);
  }

  function clearMap() {
    const mapInstance = unref(map);
    if (mapInstance) {
      try {
        mapInstance.clearMap();
      } catch (error) {
        console.error('Failed to clear map:', error);
      }
    }
  }

  function getMapBoundary() {
    const mapInstance = unref(map);
    if (!mapInstance) return {};

    try {
      const bounds = mapInstance.getBounds();
      return {
        maxLat: bounds.northEast.lat,
        maxLng: bounds.northEast.lng,
        minLat: bounds.southWest.lat,
        minLng: bounds.southWest.lng,
      };
    } catch (error) {
      console.error('Failed to get map boundary:', error);
      return {};
    }
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: AMapPoint): Promise<string> {
    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise((resolve, reject) => {
      try {
        geocoderInstance.getAddress(
          new AMap.LngLat(lngLat[0], lngLat[1]),
          (status: string, result: any) => {
            if (status === 'complete' && result.regeocode) {
              resolve(result.regeocode.formattedAddress);
            } else if (status === 'no_data') {
              resolve('');
            } else {
              reject({ status, result });
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  function createInfoWindow() {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const infoWindow = new AMap.InfoWindow({
        anchor: 'bottom-center',
        map: mapInstance,
      });
      return infoWindow;
    } catch (error) {
      console.error('Failed to create info window:', error);
      return null;
    }
  }

  return {
    mapRef,
    map,
    setMapCenter,
    addMarker,
    addPolyline,
    getMapInstance,
    clearMap,
    getMapBoundary,
    geocoder,
    destroy,
    getAddress,
    createInfoWindow,
    init,
    setMapCenterFitView,
  };
}

/**
 * @description 获取高德地图geocoder
 */
export function useGeoCoder() {
  const geocoder = ref<AMap.Geocoder | undefined>();

  onMounted(() => {
    try {
      geocoder.value = new AMap.Geocoder({
        city: '全国',
        radius: 1000,
      });
    } catch (error) {
      console.error('Failed to initialize geocoder:', error);
    }
  });

  onUnmounted(() => {
    geocoder.value = undefined;
  });

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: [number, number]) {
    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise<string>((resolve, reject) => {
      try {
        geocoderInstance.getAddress(
          new AMap.LngLat(lngLat[0], lngLat[1]),
          (status: string, result: any) => {
            if (status === 'complete' && result.regeocode) {
              resolve(result.regeocode.formattedAddress);
            } else if (status === 'no_data') {
              resolve('');
            } else {
              reject({ status, result });
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  return {
    geocoder,
    getAddress,
  };
}

export type SetMapCenter = (lngLat: [number, number]) => void;
