import { ref, unref, onMounted, onUnmounted } from 'vue';
import Car from '../../assets/images/equip/car.png';

export type AMapPoint = [number, number] | AMap.LngLat;

// 地图边界信息接口
export type MapBoundary = {
  maxLat: number;
  maxLng: number;
  minLat: number;
  minLng: number;
};

// 地理编码结果接口
export interface GeocodeResult {
  status: string;
  regeocode?: {
    formattedAddress: string;
  };
}

// 控件类型枚举
export enum AMapControlType {
  TOOLBAR = 'ToolBar',
  CONTROLBAR = 'ControlBar',
  HAWKEYE = 'HawkEye',
  MAPTYPE = 'MapType',
  SCALE = 'Scale',
  OVERVIEW = 'OverView',
}

// 控件配置接口
export interface AMapControlConfig {
  type: AMapControlType;
  options?: any;
  position?: {
    top?: string;
    bottom?: string;
    left?: string;
    right?: string;
  };
}

// 预定义的控件配置
export const DEFAULT_CONTROL_CONFIGS: Record<string, AMapControlConfig> = {
  toolbar: {
    type: AMapControlType.TOOLBAR,
    options: {},
    position: {
      top: '160px',
      right: '40px',
    },
  },
  controlbar: {
    type: AMapControlType.CONTROLBAR,
    options: {},
    position: {
      top: '20px',
      right: '150px',
    },
  },
  hawkeye: {
    type: AMapControlType.HAWKEYE,
    options: {},
  },
  maptype: {
    type: AMapControlType.MAPTYPE,
    options: {
      defaultType: 0, // 0代表默认，1代表卫星
    },
  },
  scale: {
    type: AMapControlType.SCALE,
    options: {},
  },
};

/**
 * @description 高德地图hook，便于管理地图加载、创建和销毁。
 * @param {AMap.MapOptions} options 高德地图实例化参数
 * @param {Function} callback 地图初始化完成后的回调函数
 */
export function useAMap(options: AMap.MapOptions, callback?: (map: AMap.Map) => void) {
  const map = ref<AMap.Map | undefined>();
  const mapRef = ref<HTMLDivElement | undefined>();
  const currentMarker = ref<AMap.Marker | null>(null);
  const geocoder = ref<any>();
  const isInitialized = ref(false);
  const controls = ref<Map<string, any>>(new Map());

  // 存储地图上的多边形和圆形
  const polygons = ref<Map<string, AMap.Polygon>>(new Map());
  const circles = ref<Map<string, AMap.Circle>>(new Map());

  // 绘制工具相关
  const mouseTool = ref<AMap.MouseTool | null>(null);
  const drawingOverlays = ref<(AMap.Polygon | AMap.Circle)[]>([]);

  /**
   * 初始化地图实例的核心逻辑
   */
  function initializeMap(): boolean {
    const container = unref(mapRef);
    if (!container) {
      console.error('Map container ref is not available');
      return false;
    }

    if (isInitialized.value) {
      console.warn('Map is already initialized');
      return true;
    }

    try {
      // 销毁现有实例（如果存在）
      if (map.value) {
        map.value.destroy();
      }

      map.value = new AMap.Map(container, options);

      // 初始化地理编码器
      geocoder.value = new (window as any).AMap.Geocoder({
        city: '全国',
        radius: 1000,
      });

      isInitialized.value = true;

      if (typeof callback === 'function') {
        callback(map.value);
      }

      return true;
    } catch (error) {
      console.error('Failed to initialize AMap:', error);
      isInitialized.value = false;
      return false;
    }
  }

  // dom挂载完成后初始化map实例
  onMounted(() => {
    // 加载所有需要的插件
    AMap.plugin(
      [
        'AMap.Geocoder',
        'AMap.MouseTool',
        'AMap.DistrictSearch',
        'AMap.Circle',
        'AMap.Polygon',
        'AMap.MoveAnimation',
      ],
      () => {
        // 插件加载完成后初始化地图
        console.log('map ref : ', unref(mapRef));
        if (unref(mapRef)) {
          map.value = new AMap.Map(unref(mapRef)!, options);
          // 初始化绘制工具
          initializeDrawingTool();
          if (typeof callback === 'function') callback(unref(map)!);
        }
      },
    );

    console.log('map ref : ', unref(mapRef));
    if (unref(mapRef)) {
      initializeMap();
    }
  });

  /**
   * 手动初始化地图
   */
  function init(containerRef: HTMLDivElement): boolean {
    mapRef.value = containerRef;
    return initializeMap();
  }

  /**
   * 销毁地图实例和相关资源
   */
  function destroy(): void {
    try {
      if (map.value) {
        map.value.destroy();
        map.value = undefined;
      }
      geocoder.value = undefined;
      isInitialized.value = false;
    } catch (error) {
      console.error('Failed to destroy AMap:', error);
    }
  }

  // 组件卸载时销毁地图
  onUnmounted(() => {
    stopMoveAnimation();
    destroy();
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * @description 设置中心点
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenter(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.setCenter(lngLat);
      return true;
    } catch (error) {
      console.error('Failed to set map center:', error);
      return false;
    }
  }

  /**
   * @description 设置中心点并自适应视野
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenterFitView(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.setCenter(lngLat);
      mapInstance.setFitView();
      return true;
    } catch (error) {
      console.error('Failed to set map center and fit view:', error);
      return false;
    }
  }

  /**
   * @description 统一的自适应视野方法
   * @param overlays 覆盖物数组，如果不传则自适应所有覆盖物
   * @param immediately 是否立即执行，默认 false（有动画）
   * @param avoid 四周边距 [上, 下, 左, 右]，默认 [60, 60, 60, 60]
   * @param maxZoom 最大缩放级别，默认 18
   */
  function setFitView(
    overlays?: any[],
    immediately: boolean = false,
    avoid: number[] = [60, 60, 60, 60],
    maxZoom: number = 18,
  ): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      if (overlays && overlays.length > 0) {
        // 传入了特定覆盖物，自适应这些覆盖物
        mapInstance.setFitView(overlays, immediately, avoid, maxZoom);
      } else {
        // 没有传入覆盖物，自适应所有覆盖物
        mapInstance.setFitView(undefined, immediately, avoid, maxZoom);
      }
      return true;
    } catch (error) {
      console.error('Failed to set fit view:', error);
      return false;
    }
  }

  /**
   * @description 设置地图缩放级别
   * @param zoom 缩放级别，范围 [2, 30]
   * @param immediately 是否立即过渡到目标位置，默认 false
   * @param duration 动画过渡时长，单位 ms
   */
  function setZoom(zoom: number, immediately: boolean = false, duration?: number): boolean {
    if (typeof zoom !== 'number' || zoom < 2 || zoom > 30) {
      console.error('Invalid zoom level provided:', zoom, 'Expected range: [2, 30]');
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.setZoom(zoom, immediately, duration);
      return true;
    } catch (error) {
      console.error('Failed to set zoom level:', error);
      return false;
    }
  }

  /**
   * 验证位置参数是否有效
   */
  function validatePosition(position: AMapPoint): boolean {
    if (Array.isArray(position)) {
      return validateCoordinates(position);
    }
    // 如果是AMap.LngLat对象，检查其属性
    if (position && typeof position === 'object' && 'lng' in position && 'lat' in position) {
      return validateCoordinates([position.lng, position.lat]);
    }
    return false;
  }

  /**
   * 添加标记点
   * @param position 位置坐标
   * @param options 标记选项
   */
  function addMarker(position: AMapPoint, options: AMap.MarkerOptions = {}): AMap.Marker | null {
    if (!validatePosition(position)) {
      console.error('Invalid position provided:', position);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const marker = new AMap.Marker({
        position,
        ...options,
      });
      mapInstance.add(marker);
      return marker;
    } catch (error) {
      console.error('Failed to add marker:', error);
      return null;
    }
  }

  /**
   * 添加折线
   * @param points 路径点数组
   * @param options 折线选项
   */
  function addPolyline(
    points: AMapPoint[],
    options: Partial<AMap.PolylineOptions> = {},
  ): AMap.Polyline | null {
    if (!Array.isArray(points) || points.length < 2) {
      console.error('Invalid points array provided, at least 2 points required');
      return null;
    }

    // 验证所有点的有效性
    const invalidPoints = points.filter((point) => !validatePosition(point));
    if (invalidPoints.length > 0) {
      console.error('Invalid points found:', invalidPoints);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const polyline = new AMap.Polyline({
        path: points,
        borderWeight: 10,
        strokeColor: '#4d78bc',
        ...options,
      });
      mapInstance.add(polyline);
      return polyline;
    } catch (error) {
      console.error('Failed to add polyline:', error);
      return null;
    }
  }

  /**
   * 添加多边形
   * @param points 多边形路径点数组
   * @param options 多边形选项
   */
  function addPolygon(
    points: AMapPoint[] | AMapPoint[][] | AMapPoint[][][],
    options: AMap.PolygonOptions = {},
  ): AMap.Polygon | null {
    console.log('amap addPolygon: ', points);
    if ((!Array.isArray(points) || points.length < 3) && !options.path) {
      console.error('Invalid points array provided, at least 3 points required for polygon');
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      // @ts-ignore
      const polygon = new AMap.Polygon({
        path: points,
        ...options,
      });
      mapInstance.add(polygon);

      // 自动生成ID并存储多边形
      const polygonId = `polygon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      polygons.value.set(polygonId, polygon);

      // 将ID存储到多边形的extData中，方便后续查找
      const currentExtData = polygon.getExtData() || {};
      polygon.setExtData({ ...currentExtData, _internalId: polygonId });

      return polygon;
    } catch (error) {
      console.error('Failed to add polygon:', error);
      return null;
    }
  }

  /**
   * 添加圆形
   * @param center 圆心坐标
   * @param radius 半径（米）
   * @param options 圆形选项
   */
  function addCircle(
    center: AMapPoint,
    radius: number,
    options: AMap.CircleOptions = {},
  ): AMap.Circle | null {
    if (!validatePosition(center)) {
      console.error('Invalid center position provided:', center);
      return null;
    }

    if (typeof radius !== 'number' || radius <= 0) {
      console.error('Invalid radius provided, must be a positive number:', radius);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const circle = new AMap.Circle({
        center,
        radius,
        ...options,
      });
      mapInstance.add(circle);

      // 自动生成ID并存储圆形
      const circleId = `circle_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      circles.value.set(circleId, circle);

      // 将ID存储到圆形对象上，因为AMap.Circle可能没有extData方法
      (circle as any)._internalId = circleId;

      return circle;
    } catch (error) {
      console.error('Failed to add circle:', error);
      return null;
    }
  }

  /**
   * 移除多边形
   * @param polygon 要移除的多边形对象或多边形数组
   */
  function removePolygon(polygon: AMap.Polygon | AMap.Polygon[]): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      const polygonsToRemove = Array.isArray(polygon) ? polygon : [polygon];

      polygonsToRemove.forEach((poly) => {
        // 从地图上移除
        mapInstance.remove(poly);

        // 从存储中移除
        const internalId = (poly as any)._internalId || poly.getExtData()?._internalId;
        if (internalId && polygons.value.has(internalId)) {
          polygons.value.delete(internalId);
        }
      });

      return true;
    } catch (error) {
      console.error('Failed to remove polygon:', error);
      return false;
    }
  }

  /**
   * 移除圆形
   * @param circle 要移除的圆形对象或圆形数组
   */
  function removeCircle(circle: AMap.Circle | AMap.Circle[]): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      const circlesToRemove = Array.isArray(circle) ? circle : [circle];

      circlesToRemove.forEach((circ) => {
        // 从地图上移除
        mapInstance.remove(circ);

        // 从存储中移除
        const internalId = (circ as any)._internalId;
        if (internalId && circles.value.has(internalId)) {
          circles.value.delete(internalId);
        }
      });

      return true;
    } catch (error) {
      console.error('Failed to remove circle:', error);
      return false;
    }
  }

  /**
   * 移除所有多边形
   */
  function removeAllPolygons(): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      // 移除所有存储的多边形
      polygons.value.forEach((polygon) => {
        mapInstance.remove(polygon);
      });

      // 清空存储
      polygons.value.clear();

      return true;
    } catch (error) {
      console.error('Failed to remove all polygons:', error);
      return false;
    }
  }

  /**
   * 移除所有圆形
   */
  function removeAllCircles(): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      // 移除所有存储的圆形
      circles.value.forEach((circle) => {
        mapInstance.remove(circle);
      });

      // 清空存储
      circles.value.clear();

      return true;
    } catch (error) {
      console.error('Failed to remove all circles:', error);
      return false;
    }
  }

  /**
   * 移除所有多边形和圆形
   */
  function removeAllPolygonsAndCircles(): boolean {
    const polygonResult = removeAllPolygons();
    const circleResult = removeAllCircles();
    return polygonResult && circleResult;
  }

  /**
   * 获取所有多边形
   */
  function getAllPolygons(): AMap.Polygon[] {
    return Array.from(polygons.value.values());
  }

  /**
   * 获取所有圆形
   */
  function getAllCircles(): AMap.Circle[] {
    return Array.from(circles.value.values());
  }

  /**
   * 初始化绘制工具
   */
  function initializeDrawingTool(): void {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available for drawing tool initialization');
      return;
    }

    try {
      mouseTool.value = new AMap.MouseTool(mapInstance);

      // 监听绘制完成事件
      mouseTool.value.on('draw', (event: any) => {
        // 清除之前的绘制图形
        clearDrawingOverlays();

        // 保存新的绘制图形
        drawingOverlays.value = [event.obj];

        // 关闭绘制工具
        if (mouseTool.value) {
          mouseTool.value.close();
        }
      });
    } catch (error) {
      console.error('Failed to initialize drawing tool:', error);
    }
  }

  /**
   * 开始绘制多边形
   * @param options 多边形样式选项
   */
  function startDrawPolygon(options: AMap.PolygonOptions = {}): void {
    if (!mouseTool.value) {
      console.error('Drawing tool is not initialized');
      return;
    }

    const defaultOptions: AMap.PolygonOptions = {
      fillColor: '#00b0ff',
      fillOpacity: 0.3,
      strokeColor: '#80d8ff',
      strokeWeight: 2,
      strokeOpacity: 0.8,
    };

    mouseTool.value.polygon({
      ...defaultOptions,
      ...options,
    });
  }

  /**
   * 开始绘制圆形
   * @param options 圆形样式选项
   */
  function startDrawCircle(options: AMap.CircleOptions = {}): void {
    if (!mouseTool.value) {
      console.error('Drawing tool is not initialized');
      return;
    }

    const defaultOptions: AMap.CircleOptions = {
      fillColor: '#00b0ff',
      fillOpacity: 0.3,
      strokeColor: '#80d8ff',
      strokeWeight: 2,
      strokeOpacity: 0.8,
    };

    mouseTool.value.circle({
      ...defaultOptions,
      ...options,
    });
  }

  /**
   * 停止绘制
   */
  function stopDrawing(): void {
    if (mouseTool.value) {
      mouseTool.value.close();
    }
  }

  /**
   * 清除绘制的图形
   */
  function clearDrawingOverlays(): void {
    const mapInstance = unref(map);
    if (!mapInstance) return;

    drawingOverlays.value.forEach((overlay) => {
      try {
        mapInstance.remove(overlay);
      } catch (error) {
        console.error('Failed to remove drawing overlay:', error);
      }
    });

    drawingOverlays.value = [];
  }

  /**
   * 获取当前绘制的图形
   */
  function getDrawingOverlays(): (AMap.Polygon | AMap.Circle)[] {
    return [...drawingOverlays.value];
  }

  /**
   * 获取绘制图形的几何数据
   */
  function getDrawingData(): Array<{
    type: 'polygon' | 'circle';
    data: any;
  }> {
    return drawingOverlays.value.map((overlay) => {
      if (overlay instanceof AMap.Polygon) {
        return {
          type: 'polygon',
          data: overlay.getPath(),
        };
      } else if (overlay instanceof AMap.Circle) {
        return {
          type: 'circle',
          data: {
            center: overlay.getCenter(),
            radius: overlay.getRadius(),
          },
        };
      }
      return { type: 'polygon', data: null };
    });
  }

  /**
   * 设置地图显示的城市
   * @param cityName 城市名称，如"北京"、"上海"等
   */
  function setCity(cityName: string): Promise<boolean> {
    return new Promise((resolve) => {
      const mapInstance = unref(map);
      if (!mapInstance) {
        console.error('Map instance is not available');
        resolve(false);
        return;
      }

      try {
        // 高德地图的 setCity 方法
        mapInstance.setCity(cityName, (status: string, result: any) => {
          if (status === 'complete') {
            console.log(`Successfully set city to: ${cityName}`);
            resolve(true);
          } else {
            console.error(`Failed to set city to: ${cityName}`, result);
            resolve(false);
          }
        });
      } catch (error) {
        console.error('Error setting city:', error);
        resolve(false);
      }
    });
  }

  /**
   * 行政区域搜索
   * @param keyword 搜索关键词（行政区域名称或代码）
   * @param options 搜索选项
   */
  function districtSearch(
    keyword: string,
    options: {
      level?: 'country' | 'province' | 'city' | 'district';
      subdistrict?: number;
      extensions?: 'base' | 'all';
    } = {},
  ): Promise<{
    status: string;
    info: string;
    districtList: Array<{
      name: string;
      center: AMapPoint;
      boundaries?: AMapPoint[][];
      adcode: string;
      level: string;
    }>;
  }> {
    return new Promise((resolve, reject) => {
      const mapInstance = unref(map);
      if (!mapInstance) {
        reject(new Error('Map instance is not available'));
        return;
      }

      try {
        const districtSearch = new AMap.DistrictSearch({
          level: options.level || 'district',
          subdistrict: options.subdistrict || 0,
          extensions: options.extensions || 'all',
        });

        districtSearch.search(keyword, (status: string, result: any) => {
          if (status === 'complete' && result.info === 'OK') {
            resolve({
              status,
              info: result.info,
              districtList: result.districtList.map((district: any) => ({
                name: district.name,
                center: district.center,
                boundaries: district.boundaries,
                adcode: district.adcode,
                level: district.level,
              })),
            });
          } else {
            reject(new Error(`District search failed: ${status}`));
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 获取地图实例
   */
  function getMapInstance(): AMap.Map | undefined {
    return unref(map);
  }

  /**
   * 清空地图上的所有覆盖物
   */
  function clearMap(): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.clearMap();
      return true;
    } catch (error) {
      console.error('Failed to clear map:', error);
      return false;
    }
  }

  /**
   * 获取地图边界信息
   */
  function getMapBoundary(): MapBoundary | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const bounds = mapInstance.getBounds();
      if (!bounds || !bounds.northEast || !bounds.southWest) {
        console.error('Invalid bounds returned from map');
        return null;
      }

      return {
        maxLat: bounds.northEast.lat,
        maxLng: bounds.northEast.lng,
        minLat: bounds.southWest.lat,
        minLng: bounds.southWest.lng,
      };
    } catch (error) {
      console.error('Failed to get map boundary:', error);
      return null;
    }
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: AMapPoint): Promise<string> {
    if (!validatePosition(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise((resolve, reject) => {
      try {
        const coordinates = Array.isArray(lngLat) ? lngLat : [lngLat.lng, lngLat.lat];
        geocoderInstance.getAddress(
          new (window as any).AMap.LngLat(coordinates[0], coordinates[1]),
          (status: string, result: GeocodeResult) => {
            if (status === 'complete' && result.regeocode) {
              resolve(result.regeocode.formattedAddress || '');
            } else if (status === 'no_data') {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 手动触发地图对象的事件
   * @param target 目标对象（地图、marker、polyline 等）
   * @param eventType 事件类型
   * @param eventData 事件数据（可选）
   */
  function triggerEvent(
    target: AMap.Map | AMap.Marker | AMap.Polyline | AMap.Polygon | AMap.Circle | any,
    eventType: string,
    eventData?: any,
  ): boolean {
    if (!target) {
      console.error('Target object is not available');
      return false;
    }

    try {
      // 使用 AMap.Event.trigger 手动触发事件
      AMap.Event.trigger(target, eventType, eventData);
      return true;
    } catch (error) {
      console.error('Failed to trigger event:', error);
      return false;
    }
  }

  /**
   * 为地图对象添加事件监听器（通用方法）
   * @param target 目标对象（地图、marker、polyline、polygon、circle 等）
   * @param eventType 事件类型
   * @param handler 事件处理函数
   */
  function addEventListener(
    target: AMap.Map | AMap.Marker | AMap.Polyline | AMap.Polygon | AMap.Circle | any,
    eventType: string,
    handler: (event: any) => void,
  ): void {
    console.log('event target : ', target);
    if (!target) {
      console.error('Target object is not available');
      return;
    }

    try {
      target.on(eventType, handler);
    } catch (error) {
      console.error('Failed to add event listener:', error);
    }
  }

  /**
   * 移除地图对象的事件监听器（通用方法）
   * @param target 目标对象（地图、marker、polyline、polygon、circle 等）
   * @param eventType 事件类型
   * @param handler 事件处理函数（可选，不传则移除该事件类型的所有监听器）
   */
  function removeEventListener(
    target: AMap.Map | AMap.Marker | AMap.Polyline | AMap.Polygon | AMap.Circle | any,
    eventType: string,
    handler?: (event: any) => void,
  ): void {
    if (!target) {
      console.error('Target object is not available');
      return;
    }

    try {
      if (handler) {
        target.off(eventType, handler);
      } else {
        // 移除所有该类型的事件监听器
        target.off(eventType, () => {});
      }
    } catch (error) {
      console.error('Failed to remove event listener:', error);
    }
  }

  // 导入统一类型
  type UnifiedSize = [number, number] | { width: number; height: number };
  type UnifiedOffset = [number, number] | { x: number; y: number };

  /**
   * 统一信息窗口包装器
   */
  interface UnifiedInfoWindow {
    open(mapOrPosition?: any, position?: any): void;
    close(): void;
    setContent(content: string | HTMLElement): void;
    getContent(): string | HTMLElement | null;
    setPosition(position: AMapPoint): void;
    getPosition(): AMapPoint | null;
    setOffset(offset: UnifiedOffset): void;
    getOffset(): UnifiedOffset | null;
    setSize(size: UnifiedSize): void;
    getSize(): UnifiedSize | null;
    getRawInstance(): AMap.InfoWindow | null;
  }

  /**
   * 创建统一的信息窗口
   * @param options 信息窗口选项
   */
  function createInfoWindow(options: any = {}): UnifiedInfoWindow | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const infoWindow = new AMap.InfoWindow({
        anchor: 'bottom-center',
        map: mapInstance,
        ...options,
      });

      // 创建统一的包装器
      const wrapper: UnifiedInfoWindow = {
        /**
         * 统一的 open 方法
         * 支持多种调用方式：
         * - open() - 在当前位置打开
         * - open(position) - 在指定位置打开
         * - open(map, position) - 在指定地图和位置打开
         * - open(map, marker) - 在指定地图和标记位置打开
         */
        open(mapOrPosition?: any, position?: any): void {
          try {
            if (arguments.length === 0) {
              // open() - 使用默认位置，需要提供位置参数
              const center = mapInstance.getCenter();
              infoWindow.open(mapInstance, center.toArray());
            } else if (arguments.length === 1) {
              if (
                Array.isArray(mapOrPosition) ||
                (mapOrPosition && typeof mapOrPosition.lng === 'number')
              ) {
                // open(position) - 在指定位置打开
                infoWindow.open(mapInstance, mapOrPosition);
              } else if (mapOrPosition && mapOrPosition.getPosition) {
                // open(marker) - 在标记位置打开
                infoWindow.open(mapInstance, mapOrPosition.getPosition());
              } else {
                // open(map) - 在指定地图打开，使用地图中心点
                const center = mapOrPosition.getCenter();
                infoWindow.open(mapOrPosition, center);
              }
            } else {
              // open(map, position/marker) - 在指定地图和位置/标记打开
              if (position && position.getPosition) {
                infoWindow.open(mapOrPosition, position.getPosition());
              } else {
                infoWindow.open(mapOrPosition, position);
              }
            }
          } catch (error) {
            console.error('Failed to open info window:', error);
          }
        },

        close(): void {
          try {
            infoWindow.close();
          } catch (error) {
            console.error('Failed to close info window:', error);
          }
        },

        setContent(content: string | HTMLElement): void {
          try {
            infoWindow.setContent(content);
          } catch (error) {
            console.error('Failed to set info window content:', error);
          }
        },

        getContent(): string | HTMLElement | null {
          try {
            return infoWindow.getContent();
          } catch (error) {
            console.error('Failed to get info window content:', error);
            return null;
          }
        },

        setPosition(position: any): void {
          try {
            // 转换位置格式
            let amapPosition;
            if (Array.isArray(position)) {
              amapPosition = new AMap.LngLat(position[0], position[1]);
            } else if (position && typeof position.lng === 'number') {
              amapPosition = position;
            } else {
              amapPosition = position;
            }
            infoWindow.setPosition(amapPosition);
          } catch (error) {
            console.error('Failed to set info window position:', error);
          }
        },

        getPosition(): AMapPoint | null {
          try {
            return infoWindow.getPosition();
          } catch (error) {
            console.error('Failed to get info window position:', error);
            return null;
          }
        },

        setOffset(offset: UnifiedOffset): void {
          try {
            let amapOffset: AMap.Pixel;
            if (Array.isArray(offset)) {
              amapOffset = new AMap.Pixel(offset[0], offset[1]);
            } else {
              amapOffset = new AMap.Pixel(offset.x, offset.y);
            }
            (infoWindow as any).setOffset(amapOffset);
          } catch (error) {
            console.error('Failed to set info window offset:', error);
          }
        },

        getOffset(): UnifiedOffset | null {
          try {
            const offset = (infoWindow as any).getOffset();
            if (offset && typeof offset.x === 'number' && typeof offset.y === 'number') {
              return [offset.x, offset.y];
            }
            return null;
          } catch (error) {
            console.error('Failed to get info window offset:', error);
            return null;
          }
        },

        setSize(size: UnifiedSize): void {
          try {
            let amapSize: AMap.Size;
            if (Array.isArray(size)) {
              amapSize = new AMap.Size(size[0], size[1]);
            } else {
              amapSize = new AMap.Size(size.width, size.height);
            }
            infoWindow.setSize(amapSize);
          } catch (error) {
            console.error('Failed to set info window size:', error);
          }
        },

        getSize(): UnifiedSize | null {
          try {
            const size = (infoWindow as any).getSize();
            if (size && typeof size.width === 'number' && typeof size.height === 'number') {
              return [size.width, size.height];
            }
            return null;
          } catch (error) {
            console.error('Failed to get info window size:', error);
            return null;
          }
        },

        getRawInstance(): AMap.InfoWindow | null {
          return infoWindow;
        },
      };

      return wrapper;
    } catch (error) {
      console.error('Failed to create info window:', error);
      return null;
    }
  }

  /**
   * 添加地图控件
   * @param controlId 控件唯一标识
   * @param config 控件配置
   */
  function addControl(controlId: string, config: AMapControlConfig): Promise<any> {
    return new Promise((resolve, reject) => {
      const mapInstance = unref(map);
      if (!mapInstance) {
        reject(new Error('Map instance is not available'));
        return;
      }

      // 如果控件已存在，先移除
      if (controls.value.has(controlId)) {
        removeControl(controlId);
      }

      const pluginName = `AMap.${config.type}`;

      (window as any).AMap.plugin([pluginName], () => {
        try {
          const ControlClass = (window as any).AMap[config.type];
          if (!ControlClass) {
            reject(new Error(`Control class ${config.type} not found`));
            return;
          }

          const controlOptions = { ...config.options };
          if (config.position) {
            controlOptions.position = config.position;
          }

          const control = new ControlClass(controlOptions);
          mapInstance.addControl(control);
          controls.value.set(controlId, control);

          console.log(`Control ${controlId} (${config.type}) added successfully`);
          resolve(control);
        } catch (error) {
          console.error(`Failed to add control ${controlId}:`, error);
          reject(error);
        }
      });
    });
  }

  /* 批量获取地址
   * @param data 数组对象
   * @param keys 需要传递数组对象的经纬度字段名 ['lng', 'lat']
   */
  function batchGetAddress(data: any, keys: string[] = ['lng', 'lat']): Promise<any[]> {
    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    // console.log('keys', keys);
    return new Promise((resolve, reject) => {
      const point = [data[keys[0]], data[keys[1]]];
      console.log('point', point);

      geocoderInstance.getAddress(point, function (status, result) {
        if (status === 'complete' && result.regeocode) {
          data.address = result.regeocode.formattedAddress;
          resolve(data);
        } else {
          console.error('根据经纬度查询地址失败：', result);
          reject(result);
        }
      });
    });
  }

  /**
   * 移除地图控件
   * @param controlId 控件唯一标识
   */
  function removeControl(controlId: string): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    const control = controls.value.get(controlId);
    if (!control) {
      console.warn(`Control ${controlId} not found`);
      return false;
    }

    try {
      mapInstance.removeControl(control);
      controls.value.delete(controlId);
      console.log(`Control ${controlId} removed successfully`);
      return true;
    } catch (error) {
      console.error(`Failed to remove control ${controlId}:`, error);
      return false;
    }
  }

  /**
   * 添加预定义的控件
   * @param controlName 预定义控件名称
   * @param customOptions 自定义选项（可选）
   */
  function addPredefinedControl(
    controlName: keyof typeof DEFAULT_CONTROL_CONFIGS,
    customOptions?: Partial<AMapControlConfig>,
  ): Promise<any> {
    const defaultConfig = DEFAULT_CONTROL_CONFIGS[controlName];
    if (!defaultConfig) {
      return Promise.reject(new Error(`Predefined control ${controlName} not found`));
    }

    const config: AMapControlConfig = {
      ...defaultConfig,
      ...customOptions,
      options: {
        ...defaultConfig.options,
        ...customOptions?.options,
      },
    };

    return addControl(controlName, config);
  }

  /**
   * 批量添加控件
   * @param controlConfigs 控件配置数组
   */
  async function addMultipleControls(
    controlConfigs: Array<{ id: string; config: AMapControlConfig }>,
  ): Promise<Array<{ id: string; control?: any; error?: any; success: boolean }>> {
    const results: Array<{ id: string; control?: any; error?: any; success: boolean }> = [];
    for (const { id, config } of controlConfigs) {
      try {
        const control = await addControl(id, config);
        results.push({ id, control, success: true });
      } catch (error) {
        console.error(`Failed to add control ${id}:`, error);
        results.push({ id, error, success: false });
      }
    }
    return results;
  }

  /**
   * 清除所有控件
   */
  function clearAllControls(): void {
    const controlIds = Array.from(controls.value.keys());
    controlIds.forEach((id) => removeControl(id));
  }

  /**
   * 获取已添加的控件列表
   */
  function getControls(): Map<string, any> {
    return new Map(controls.value);
  }

  const CAR_ICON = new AMap.Icon({
    image: Car,
    imageSize: new AMap.Size(30, 30),
    size: new AMap.Size(30, 30),
  });

  /**
   * @description 开始轨迹动画
   * @param points 轨迹点数组
   * @param speed 移动速度（千米/小时），默认 100
   * @param options 动画配置选项
   */
  function startMoveAnimation(
    points: AMapPoint[],
    options: {
      speed?: number;
      loop?: boolean;
      onStart?: () => void;
      onMoving?: (e: any) => void; // 新增一个移动中的回调
      onEnd?: () => void;
    } = {},
  ) {
    // 清除旧marker和动画
    stopMoveAnimation();

    // 创建移动marker（使用封装的 addMarker 方法）
    currentMarker.value = addMarker(points[0], {
      icon: CAR_ICON,
      offset: new AMap.Pixel(-15, -15),
    });

    if (currentMarker.value) {
      currentMarker.value.on('moving' as any, (e: any) => {
        console.log('on moving ~~~~~~');
        options.onMoving?.(e);
      });
      currentMarker.value.on('stop' as any, () => {
        console.log('动画结束');

        if (!options.loop) {
          options.onEnd?.();
        }
      });
    }

    // 执行移动动画
    if (currentMarker.value) {
      currentMarker.value.moveAlong(points, {
        duration: 200 * (100 / (options.speed || 100)),
        autoRotation: true,
        circulate: options.loop || false,
        speed: options.speed || 100,
      });

      // 立即调用 onStart 回调
      console.log('动画指令已发出，开始移动');
      options.onStart?.();
    }
  }

  /**
   * @description 停止轨迹动画
   */
  function stopMoveAnimation() {
    if (currentMarker.value) {
      // 使用 Marker 实例上的 stopMove 方法
      currentMarker.value.stopMove();
      unref(map)?.remove(currentMarker.value);
      currentMarker.value = null;
    }
  }

  return {
    mapRef,
    map,
    setMapCenter,
    setZoom,
    addMarker,
    addPolyline,
    addPolygon,
    addCircle,
    removePolygon,
    removeCircle,
    removeAllPolygons,
    removeAllCircles,
    removeAllPolygonsAndCircles,
    getAllPolygons,
    getAllCircles,
    districtSearch,
    startDrawPolygon,
    startDrawCircle,
    stopDrawing,
    clearDrawingOverlays,
    getDrawingOverlays,
    getDrawingData,
    initializeDrawingTool,
    setCity,
    getMapInstance,
    clearMap,
    getMapBoundary,
    geocoder,
    destroy,
    getAddress,
    batchGetAddress,
    createInfoWindow,
    init,
    setMapCenterFitView,
    setFitView,
    isInitialized,
    validateCoordinates,
    validatePosition,
    // 事件管理方法
    addEventListener,
    removeEventListener,
    triggerEvent,
    // 控件管理方法
    addControl,
    removeControl,
    addPredefinedControl,
    addMultipleControls,
    clearAllControls,
    getControls,
    CAR_ICON,
    startMoveAnimation,
    stopMoveAnimation,
  };
}

/**
 * @description 获取高德地图geocoder
 */
export function useGeoCoder() {
  const geocoder = ref<any>();
  const isInitialized = ref(false);

  /**
   * 初始化地理编码器
   */
  function initGeocoder(): boolean {
    if (isInitialized.value) {
      return true;
    }

    try {
      // 使用any类型避免类型检查问题
      geocoder.value = new (window as any).AMap.Geocoder({
        city: '全国',
        radius: 1000,
      });
      isInitialized.value = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize geocoder:', error);
      isInitialized.value = false;
      return false;
    }
  }

  onMounted(() => {
    initGeocoder();
  });

  onUnmounted(() => {
    geocoder.value = undefined;
    isInitialized.value = false;
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: [number, number]): Promise<string> {
    if (!validateCoordinates(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise<string>((resolve, reject) => {
      try {
        geocoderInstance.getAddress(
          new (window as any).AMap.LngLat(lngLat[0], lngLat[1]),
          (status: string, result: GeocodeResult) => {
            if (status === 'complete' && result.regeocode) {
              resolve(result.regeocode.formattedAddress || '');
            } else if (status === 'no_data') {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  return {
    geocoder,
    getAddress,
    isInitialized,
    initGeocoder,
    validateCoordinates,
  };
}

export type SetMapCenter = (lngLat: [number, number]) => void;
