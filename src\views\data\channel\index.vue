<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-if="showAddbtn" @click="handleCreate"> 新增通道 </a-button>
      </template>
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{
          record.settingCount
        }}</Tag>
      </template>
      <template #action="{ record }">
        <TableAction :actions="[
          {
            icon: 'clarity:play-solid',
            tooltip: '播放',
            onClick: handlePlay.bind(null, record),
          },
          {
            icon: 'clarity:note-edit-line',
            onClick: handleEdit.bind(null, record),
          },
          {
            icon: 'ant-design:delete-outlined',
            color: 'error',
            popConfirm: {
              title: '是否确认删除',
              confirm: handleDelete.bind(null, record),
            },
          },
        ]" />
      </template>
    </BasicTable>
    <AddFormModal @register="registerFormModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { Tag } from 'ant-design-vue';

import { BasicTable, useTable, TableAction } from '@/components/Table';
import { getchannelList, removechannel } from '@/api/data/channel';
import { useMessage } from '@/hooks/web/useMessage';

import { useModal } from '@/components/Modal';
import AddFormModal from './AddFormModal.vue';
import PalyModal from './PlayModal.vue';
import { useRoute, useRouter } from 'vue-router';
import { useDrawer } from '@/components/Drawer';

import { columns, searchFormSchema } from './role.data';

export default defineComponent({
  name: 'RoleList',
  components: { BasicTable, TableAction, Tag, AddFormModal, PalyModal },
  setup() {
    let showAddbtn = true;
    const { params } = useRoute();
    if (params.channelIds) {
      showAddbtn = false;
      console.log(showAddbtn);
    }

    const router = useRouter();
    const [registerTable, { reload }] = useTable({
      title: '通道列表',
      api: getchannelList,
      columns,
      formConfig: {
        labelAlign: 'left',
        schemas: searchFormSchema,
      },
      useSearchForm: true,
      showTableSetting: true,
      bordered: true,
      showIndexColumn: false,
      beforeFetch: async (searchInfo: any) => {
        console.log('before fetch : ', searchInfo);
        if (params.channelIds) {
          searchInfo.criteria.channelIds = params.channelIds.split(',');
        }
      },
      actionColumn: {
        width: 120,
        title: '操作',
        dataIndex: 'action',
        slot: 'action',
        fixed: undefined,
      },
    });

    const [registerFormModal, { openModal: openFormModal }] = useModal();


    const [registerDrawer] = useDrawer();

    function handleCreate() {
      openFormModal(true, {
        isUpdate: false,
      });
    }

    function handleEdit(record: Recordable) {
      console.log(record);
      openFormModal(true, {
        record,
        isUpdate: true,
      });
    }

    function handlePlay(record: Recordable) {
      //router.push({ path: `/ifram/${record.id}`, query: { id: record.id } });
      router.push({ path: `/singleVideo/${record.vehicleId}/${record.channelNo}` });
      console.log(record);
    }

    function handleDelete(record: Recordable) {
      console.log(record);
      const { createMessage } = useMessage();
      removechannel(record?.id).then((res) => {
        createMessage.success('删除成功');
        reload();
      });
    }

    function handleSuccess() {
      reload();
    }

    return {
      showAddbtn,
      registerTable,
      handleCreate,
      handleEdit,
      handleDelete,
      handleSuccess,
      handlePlay,
      registerFormModal,
      registerDrawer,
    };
  },
});
</script>
