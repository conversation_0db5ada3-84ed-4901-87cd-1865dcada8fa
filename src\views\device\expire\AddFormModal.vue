<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { FormSchema } from '@/components/Table';
  import { addDevice, editDevice } from '@/api/vehicle/vehlist';
  import { getOrgTreeOptions } from '@/api/passport/org';
  import { getModelTree } from '@/api/data/deviceModel';
  import Icon from '@/components/Icon/Icon.vue';
  import { getsimList } from '@/api/data/simCard';
  const simCardList = ref([]);
  onMounted(() => {
    getCardList();
  });
  const getCardList = async (simNo: any = '') => {
    console.log(simNo,111)
    let params = {
      pageIndex: 1,
      pageSize: 10,
      order: { property: 'id', direction: 'ASC' },
      criteria: {
        simNo,
      },
    };
    const res = await getsimList(params);
    console.log(res);
    simCardList.value = (res.data || []).map((v) => ({ label: v.simNo, value: v.id }));
  };
  const formSchema: FormSchema[] = [
    {
      field: 'name',
      label: '设备名称',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
    },
    {
      field: 'deviceModelId',
      label: '设备类型',
      colProps: { span: 12 },
      component: 'ApiTreeSelect',
      required: true,
      componentProps: {
        api: getModelTree,
        labelField: 'name',
        valueField: 'id',
        getPopupContainer: () => document.body,
      },
    },
    {
      field: 'deviceSn',
      label: 'IMEI',
      required: true,
      colProps: { span: 12 },
      component: 'Input',
    },
    {
      field: 'orgId',
      label: '所属机构',
      component: 'ApiTreeSelect',
      colProps: { span: 12 },
      required: true,
      componentProps: {
        api: getOrgTreeOptions,
        labelField: 'name',
        valueField: 'uid',
        getPopupContainer: () => document.body,
      },
    },
    {
      field: 'simId',
      label: 'sim卡',
      component: 'Select',
      colProps: { span: 12 },
      required: true,
      componentProps: {
        // api: getsimList,
        options: simCardList,
        filterOption: false,
        showSearch: true,
        onSearch: (value) => getCardList(value),
        getPopupContainer: () => document.body,
      },
    },
    {
      field: 'protocol',
      label: '协议类型',
      component: 'Select',
      required: true,
      defaultValue: null,
      colProps: { span: 12 },
      componentProps: {
        options: [
          { label: 'JT808', value: 'JT808' },
          { label: 'TIZA-V3', value: 'TIZAV3' },
        ],
      },
    },
    {
      field: 'activateTime',
      label: '激活时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: {
        style: 'width: 100%',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
    },
    {
      field: 'saleTime',
      label: '销售时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: {
        style: 'width: 100%',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
    },
    {
      field: 'expireTime',
      label: '平台到期时间',
      colProps: { span: 12 },
      component: 'DatePicker',
      componentProps: {
        style: 'width: 100%',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        showTime: true,
      },
    },
    {
      field: 'useWay',
      label: '使用范围',
      colProps: { span: 12 },
      component: 'DatePicker',
      slot: 'useWay',
    },
    {
      field: 'comment',
      label: '备注',
      colProps: { span: 24 },
      component: 'InputTextArea',
    },
  ];

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref<number | undefined>(undefined);

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增设备' : '编辑设备'));
  const wayActive = ref('轿车');

  async function handleSubmit() {
    try {
      let values = await validate();
      values.useWay = wayActive.value;
      setModalProps({ confirmLoading: true });
      // TODO custom api
      console.log(values);
      if (unref(isUpdate)) {
        await editDevice(rowId.value, values);
      } else {
        await addDevice(values);
      }
      closeModal();
      emit('success', {
        isUpdate: unref(isUpdate),
        values: { ...values, id: rowId.value },
      });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

</script>

<template>
  <BasicModal
    width="800px"
    v-bind="$attrs"
    :title="getTitle"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #useWay>
        <div class="way_list">
          <Icon
            icon="emojione-monotone:oncoming-automobile"
            title="轿车"
            :class="{ active: wayActive == '轿车' }"
            @click="wayActive = '轿车'"
          />
          <Icon
            icon="fa:truck"
            title="货车"
            :class="{ active: wayActive == '货车' }"
            @click="wayActive = '货车'"
          />
          <Icon
            icon="fa:bus"
            title="客车"
            :class="{ active: wayActive == '客车' }"
            @click="wayActive = '客车'"
          />
          <Icon
            icon="fa:taxi"
            title="出租车"
            :class="{ active: wayActive == '出租车' }"
            @click="wayActive = '出租车'"
          />
          <Icon
            icon="fa:motorcycle"
            title="摩托车"
            :class="{ active: wayActive == '摩托车' }"
            @click="wayActive = '摩托车'"
          />
          <Icon
            icon="fa:user"
            title="人"
            :class="{ active: wayActive == '人' }"
            @click="wayActive = '人'"
          />
          <Icon
            icon="mdi:excavator"
            title="挖掘机"
            :class="{ active: wayActive == '挖掘机' }"
            @click="wayActive = '挖掘机'"
          />
          <Icon
            icon="material-symbols-light:devices-other-outline"
            title="其他"
            :class="{ active: wayActive == '其他' }"
            @click="wayActive = '其他'"
          />
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<style lang="less" scoped>
  .way_list {
    color: #ccc;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    .app-iconify {
      cursor: pointer;
      font-size: 14px;
    }
    .app-iconify.active {
      color: #0421BC;
    }
  }
</style>
