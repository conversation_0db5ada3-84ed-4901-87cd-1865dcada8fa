<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增设备类型 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'icCardEquips'">
          <div
            class="icon_box"
            :class="{ active: record.icCardEquips.length > 0 }"
            @click="delForm(1, record, record.icCardEquips)"
          >
            <CreditCardOutlined />
            <span> {{ record.icCardEquips.length }} </span>
          </div>
        </template>

        <template v-if="column.key === 'fingerEquips'">
          <div
            class="icon_box"
            :class="{ active: record.fingerEquips.length > 0 }"
            @click="delForm(2, record, record.fingerEquips)"
          >
            <LikeOutlined />
            <span> {{ record.fingerEquips.length }} </span>
          </div>
        </template>

        <template v-if="column.key === 'faceEquips'">
          <div
            class="icon_box"
            :class="{ active: record.faceEquips.length > 0 }"
            @click="delForm(3, record, record.faceEquips)"
          >
            <UserOutlined />
            <span> {{ record.faceEquips.length }} </span>
          </div>
        </template>

        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <AddFormModal @register="registerFormModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getModelTree, removeModel } from '@/api/data/deviceModel';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useModal } from '@/components/Modal';
  import AddFormModal from './addFormModal.vue';
  import { columns } from './deviceModel';
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  import { CreditCardOutlined, LikeOutlined, UserOutlined } from '@ant-design/icons-vue';

  export default defineComponent({
    name: 'SimCardList',
    components: {
      BasicTable,
      TableAction,
      AddFormModal,
      CreditCardOutlined,
      LikeOutlined,
      UserOutlined,
    },
    setup() {
      const [registerTable, { reload }] = useTable({
        title: '设备类型列表',
        api: getModelTree,
        columns,
        // formConfig: {
        //   labelAlign: 'left',
        //   schemas: searchFormSchema,
        // },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
          slot: 'action',
          fixed: 'right',
        },
      });

      const [registerFormModal, { openModal: openFormModal }] = useModal();
      function handleCreate() {
        openFormModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        console.log(record);
        openFormModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        const { createMessage } = useMessage();
        removeModel(record?.id).then((res) => {
          createMessage.success('删除成功');
          reload();
        });
      }

      function handleSuccess() {
        reload();
      }
      return {
        registerTable,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        registerFormModal,
      };
    },
  });
</script>
<style scoped lang="less">
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    cursor: pointer;
  }

  .icon_box.active {
    color: #0FCEFD;
  }
</style>
./simCard.data./deviceModel