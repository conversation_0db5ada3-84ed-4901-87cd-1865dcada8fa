<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-25 11:11:39
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-26 14:16:35
 * @FilePath     : \tzlink-gps-web\src\views\dashboard\components\moveModal.vue
 * Copyright (C) 2025 chen. All rights reserved.
-->
<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { FormSchema } from '@/components/Table';
  import { changeDeviceName } from '@/api/vehicle/vehlist';
  const formSchema: FormSchema[] = [
    {
      field: 'name',
      label: '设备名称',
      colProps: { span: 24 },
      component: 'Input',
      required: true,
    },
  ];

  const emit = defineEmits(['success', 'register']);

  const deviceId = ref<number | undefined>(undefined);

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    deviceId.value = data.record.id;
    setFieldsValue({
      name: data.record.name || '',
    });
  });
  async function handleSubmit() {
    try {
      let values = await validate();
      setModalProps({ confirmLoading: true });
      await changeDeviceName(deviceId.value, values.name);
      closeModal();
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  };
</script>

<template>
  <BasicModal v-bind="$attrs" title="设备名称" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
