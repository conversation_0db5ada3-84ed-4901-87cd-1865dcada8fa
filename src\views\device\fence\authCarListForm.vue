<script lang="ts" setup name="role-list-form">
  import { ref } from 'vue';
  import { type TableActionType, BasicTable, useTable } from '@/components/Table';
  import { useModalInner, BasicModal } from '@/components/Modal';
  import { useMap } from '@/hooks/web/useMap';
  import { getDeviceList } from '@/api/vehicle/vehlist';
  import CarPic from '@/assets/images/equip/car.png';

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const emit = defineEmits(['success']);

  let params = ref({
    fenceId: 0,
    keyword: '',
  });
  const dataSource = ref([]);
  const fenceDetail: any = ref(null);
  let markers: any = [];
  const selectedKeys = ref<any>([]);

  const {
    containerRef,
    initializeMap,
    addMarker,
    destroy,
    clearMap,
    getAddress,
    getMapInstance,
    setFitView,
    addPolygon,
    addCircle,
    districtSearch,
    removeAllPolygonsAndCircles,
    setMapCenter,
  } = useMap(
    {
      center: [116.33, 39.9],
      zoom: 5,
      mapStyle: 'amap://styles/normal',
    },
    onMapInit,
  );

  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    markers = [];
    dataSource.value = [];
    selectedKeys.value = [];
    // 销毁地图实例
    destroy();

    await initializeMap();
    await onMapInit();
    setModalProps({ confirmLoading: false, showCancelBtn: false, showOkBtn: false });
    params.value.fenceId = data.item.id;
    params.value.keyword = '';
    fenceDetail.value = data.item;
  });

  async function onMapInit() {
    await initMap();
    await reload();
  }

  function initMap() {
    const { fenceJson, fenceType, districtCodes = '' } = fenceDetail.value;
    let json = fenceJson ? JSON.parse(fenceJson) : [];
    if (!getMapInstance()) return;
    removeAllPolygonsAndCircles();
    clearMap();
    // 根据数据显示已保存的围栏
    if (json.length && fenceType === 'POLYGON') {
      let polygon = addPolygon(json, {
        fillColor: '#00b0ff',
        strokeColor: '#80d8ff',
        strokeOpacity: 1,
        fillOpacity: 0.5,
        strokeWeight: 1,
        amapOptions: {
          path: json,
          strokeDasharray: [5, 5],
        },
      });
      setFitView([polygon]);
    } else if (json.length && fenceType === 'CIRCULAR') {
      let circleRadius = json.filter((v) => typeof v === 'number');
      let circle = addCircle(json[0], circleRadius[0] || 0, {
        fillColor: '#00b0ff',
        strokeColor: '#80d8ff',
      });
      setFitView([circle]);
    } else if (fenceType === 'AREA' && districtCodes) {
      drawDistricts(districtCodes);
    }
  }

  function drawDistricts(districtCodes: any) {
    if (!getMapInstance()) return;
    removeAllPolygonsAndCircles();
    clearMap();
    if (!districtCodes) return;
    let codesArray = Array.isArray(districtCodes) ? districtCodes : districtCodes.split(',');

    if (codesArray.length) {
      // 使用已加载的DistrictSearch插件
      codesArray.forEach((code) => {
        districtSearch(code, {
          level: 'district',
          subdistrict: 0,
          extensions: 'all',
        }).then((result) => {
          if (result.status === 'complete' && result.info === 'OK') {
            let bounds = result.districtList[0].boundaries;
            if (bounds) {
              let polygons: any[] = [];
              for (let i = 0; i < bounds.length; i++) {
                polygons.push(
                  addPolygon(bounds[i], {
                    fillColor: '#00b0ff',
                    strokeColor: '#80d8ff',
                    strokeOpacity: 1,
                    fillOpacity: 0.5,
                    strokeWeight: 1,
                    amapOptions: {
                      strokeDasharray: [5, 5],
                    },
                  }),
                );
              }
              setFitView(polygons);
            }
          }
        });
      });
    }
  }

  // 创建标记函数
  const createMarker = (item: any) => {
    if (!item.lng || !item.lat) return null;

    const marker = addMarker([item.lng, item.lat], {
      icon: {
        imageSize: [30, 30],
        url: CarPic,
        size: [30, 30],
      },
      anchor: 'center',
      extData: item,
    });

    return marker;
  };

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'name',
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '所属机构',
      dataIndex: 'orgName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '定位时间',
      dataIndex: 'gpsTime',
    },
    {
      title: '定位位置',
      dataIndex: 'address',
    },
  ];

  const [registerTable, { reload }] = useTable({
    api: getDeviceList,
    immediate: false,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 90,
      schemas: [
        {
          field: 'criteria.keyword',
          component: 'Input',
          label: '关键字',
          colProps: { span: 9 },
        },
      ],
    },
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    rowSelection: {
      type: 'radio',
      hideSelectAll: true,
      // @ts-ignore
      selectedRowKeys: selectedKeys,
      onChange: (selectedRowKeys: any) => {
        const marker = markers.find((m) => m.getExtData().id === selectedRowKeys[0]);
        if (marker) {
          setMapCenter(marker.getPosition());
        }
      },
    },
    // actionColumn: {
    //   width: 120,
    //   title: '操作',
    //   dataIndex: 'action',
    //   slot: 'action',
    //   fixed: undefined,
    // },
    maxHeight: 500,
    rowKey: 'id',
    beforeFetch: ({ criteria }) => {
      criteria.fenceId = params.value.fenceId;
    },
    afterFetch: (data) => {
      const validMarkers = data.map((item) => createMarker(item)).filter(Boolean);

      markers.push(...validMarkers);

      return Promise.all(
        data.map(async (item) =>
          item.lng && item.lat
            ? getAddress([item.lng, item.lat]).then((res) => ({ ...item, address: res }))
            : item,
        ),
      );
    },
  });

  // const handleDelete = async (record) => {
  //   await removeFenceCar(record.deviceId);
  // };

  const tableRef = ref<Nullable<TableActionType>>(null);
</script>

<template>
  <BasicModal width="1200px" v-bind="$attrs" title="关联设备" @register="registerModal">
    <BasicTable @register="registerTable" ref="tableRef">
      <template #aboveOfTable>
        <div class="mapRef" ref="containerRef"></div>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<style lang="less" scoped>
  .mapRef {
    width: 100%;
    height: 500px;
  }
</style>
