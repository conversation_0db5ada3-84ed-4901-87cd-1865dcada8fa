<template>
  <div class="flex flex-col h-full p-4">
    <BasicForm @register="registerForm" @submit="handleSubmit" @reset="handleReset" />
    <div class="flex-1">
      <div class="topbar">
        <a-button type="primary" @click="handleCreate"> 新增 </a-button>
        <a-button @click="open"> 启用 </a-button>
        <a-button @click="stop"> 停用 </a-button>
      </div>

      <Modal v-model:open="isOp" title="提示" @ok="open" @cancel="!isOp">
        <div class="flex gap-4">
          <Icon icon="fe:warning" style="color: #ffba00" size="30" />
          <p class="pt-3">是否启用选中围栏？</p>
        </div>
      </Modal>
      <Modal v-model:open="isSp" title="提示" @ok="stop" @cancel="!isSp">
        <div class="flex gap-4">
          <Icon icon="fe:warning" style="color: #ffba00" />
          <p class="pt-3">是否停用选中围栏？</p>
        </div>
      </Modal>

      <!-- 围栏信息 -->
      <AddFormModal @register="registerModal" @success="handleReset" />
      <!-- 设备信息 -->
      <AuthCarListForm @register="registerCarModal" />
      <!-- 关联报警 -->
      <AlertListForm @register="registerAlertModal" />

      <CheckboxGroup v-model:value="checkList" class="flex flex-wrap">
        <div v-for="(item, index) in data" :key="index" class="item">
          <div class="topsjd">
            <Checkbox class="check" :value="item.id">
              {{ '' }}
            </Checkbox>
            <Dropdown :trigger="['click']" class="more" placement="bottomLeft">
              <template #overlay>
                <Menu>
                  <MenuItem>
                    <Badge class="mark">
                      <div @click="handleEdit(item)">修改</div>
                    </Badge>
                  </MenuItem>
                  <MenuItem>
                    <Badge class="mark">
                      <div @click="del(item.id)">删除</div>
                    </Badge>
                  </MenuItem>
                </Menu>
              </template>
              <a class="el-dropdown-link">
                <Icon icon="ri:more-line" style="font-size: 22px" />
              </a>
            </Dropdown>
          </div>

          <div style="display: flex; justify-content: space-between">
            <h3>{{ item.name }}</h3>
          </div>
          <mapItem :key="'map-item-' + item.id" :info="item" class="item-top" />
          <div class="item-middle">
            <dl>
              <dt style="letter-spacing: 28px">触发</dt>
              <dd>
                ：<span v-if="item.alertEventName == '驶入'" class="normal"> 驶入 </span>
                <span v-else-if="item.alertEventName == '驶出'" class="normal"> 驶出 </span>
              </dd>
            </dl>
            <dl>
              <dt>所属机构</dt>
              <dd
                >：<span>{{ item.orgName }}</span></dd
              >
            </dl>
            <dl>
              <dt>查看信息</dt>
              <dd>
                ：<span
                  style="color: #1890ff; cursor: pointer; padding: 3px 5px; font-size: 14px"
                  @click="relVehicle({ item })"
                >
                  {{ item.deviceCount }}
                </span>
              </dd>
            </dl>
            <dl>
              <dt>关联预警</dt>
              <dd>
                ：<span
                  style="color: #1890ff; cursor: pointer; padding: 3px 5px; font-size: 14px"
                  @click="relAlarm(item)"
                >
                  {{ item.alertCount }}
                </span>
              </dd>
            </dl>
          </div>
        </div>
      </CheckboxGroup>
    </div>
    <div class="flex justify-start gap-3">
      <Pagination
        v-model:current="searchParams.pageIndex"
        v-model:page-size="searchParams.pageSize"
        :total="total"
        size="small"
        :show-total="(total) => `共&nbsp;${total}&nbsp;条`"
        show-size-changer
        @show-size-change="handleSizeChange"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import {
    CheckboxGroup,
    Checkbox,
    Dropdown,
    Menu,
    MenuItem,
    Badge,
    Modal,
    Pagination,
  } from 'ant-design-vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import Icon from '/@/components/Icon/Icon.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { getFenceList, removeFence, unUseFence, useFence } from '@/api/vehicle/fence';
  // import { useDrawer } from '@/components/Drawer';
  import { useModal } from '@/components/Modal';
  import { searchFormSchema } from './fence.data';
  import mapItem from './districtMap.vue';
  import AddFormModal from './AddFormModal.vue';
  import AuthCarListForm from './authCarListForm.vue';
  import AlertListForm from './alertListForm.vue';
  // import { LinkOutlined, AlertOutlined } from '@ant-design/icons-vue';
  const { createMessage } = useMessage();
  const [registerModal, { openModal }] = useModal();
  const [registerCarModal, { openModal: openCarModal }] = useModal();
  const [registerAlertModal, { openModal: openAlertModal }] = useModal();

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelAlign: 'left',
    schemas: searchFormSchema,
    showActionButtonGroup: true,
  });

  const isOp = ref(false);
  const isSp = ref(false);

  const data = ref([]);
  const checkList = ref([]);

  const total = ref(0);
  const searchParams = ref({
    column: 'createTime',
    order: {
      property: 'id',
      direction: 'DESC',
    },
    pageIndex: 1,
    pageSize: 10,
  });

  // 查询
  const handleSubmit = async () => {
    const form = await validate();
    const params = Object.assign({}, form, searchParams.value);
    const res = await getFenceList(params);
    data.value = res.data;
    total.value = res.total;
    searchParams.value.pageSize = res.pageSize;
  };

  // 重置
  const handleReset = async () => {
    setFieldsValue({});
    searchParams.value.pageIndex = 1;
    searchParams.value.pageSize = 10;
    await handleSubmit();
  };

  const handleSizeChange = (current, size) => {
    searchParams.value.pageIndex = 1;
    handleSubmit();
  };

  // 新增
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record) {
    // console.log('handleEdit', record);
    openModal(true, {
      isUpdate: true,
      record,
    });
  }

  // 启用
  async function open() {
    console.log('open', checkList.value);
    if (checkList.value.length === 0) {
      createMessage.warning('请选择围栏');
      return;
    }
    await useFence(checkList.value);
    isOp.value = false;
    await handleSubmit();
  }

  // 停用
  async function stop() {
    console.log('stop', checkList.value);
    if (checkList.value.length === 0) {
      createMessage.warning('请选择围栏');
      return;
    }
    await unUseFence(checkList.value);
    isSp.value = false;
    await handleSubmit();
  }

  async function del(id) {
    await removeFence(id);
    await handleSubmit();
  }

  function relVehicle(item) {
    console.log('relVehicle', item);
    openCarModal(true, {
      ...item,
    });
  }

  function relAlarm(item) {
    openAlertModal(true, {
      ...item,
    });
  }

  onMounted(() => {
    handleSubmit();
  });
</script>
<style lang="less" scoped>
  .topbar {
    margin-top: 10px;
    gap: 10px;
    display: flex;
    justify-content: flex-start;
  }

  .item {
    width: 306px;
    height: 295px;
    background: linear-gradient(
      to bottom,
      rgba(240, 249, 255, 1),
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 1)
    );
    box-sizing: border-box;
    border-radius: 2px;
    position: relative;
    margin-top: 10px;
    margin-right: 25px;
    box-shadow: 0 2px 4px 0 #bbb;
    padding: 10px 10px 0;

    h3 {
      font-size: 14px;
      font-weight: 700;
      padding: 0 0 3px;
    }
  }
  .item-top {
    width: 100%;
    height: 150px;
    border: solid 1px #eee;
  }
  .item-middle {
    width: 100%;
    text-align: center;
    padding: 5px 0 0;

    dl {
      width: 50%;
      float: left;
      padding: 5px 0 0;
      margin: 0;

      dt,
      dd {
        float: left;
        padding: 0;
        margin: 0;
        text-align: left;
        color: #666;
      }
      dt {
        width: 65px;
      }
      dd {
        width: calc(100% - 65px);
        span {
          color: #000;
        }

        .lan {
          width: 40px;
          height: 18px;
          background: #1890ff;
          line-height: 18px;
          color: white;
          text-align: center;
          border-radius: 2px;
        }
        .normal {
          width: 40px;
          height: 18px;
          background: #e6f7ff;
          line-height: 18px;
          color: #339900;
          text-align: center;
          border-radius: 2px;
          display: inline-block;
        }
      }
    }
  }
  .check {
    float: left;
  }
  .topsjd {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    padding: 3px 10px;
    color: #000;
    border-top: dotted 1px #999;
  }
  .more {
    float: right;
    cursor: pointer;
  }

  :deep(.ant-pagination-options) {
    margin-left: 10px !important;
  }
</style>
