<template>
  <div>
    <BasicTable @register="registerTable">
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>
      <TableAction
        :actions="[
          // {
          //   icon: 'flowbite:link-break-outline',
          //   color: 'error',
          //   popConfirm: {
          //     title: '是否确认解绑该设备',
          //     confirm: handleDelete.bind(null, record),
          //   },
          // },
        ]"
      />
    </BasicTable>

    <AlarmHistory @register="registerModal" />
  </div>
</template>
<script lang="tsx" setup>
  import { onMounted, ref } from 'vue';
  import { Tag } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getHistoryList } from '@/api/statis/metrics';
  import { useModal } from '@/components/Modal';
  import { getOrgTreeOptions } from '@/api/passport/org';
  import { getvehicleList } from '@/api/vehicle/vehlist';

  const [registerModal, { openModal }] = useModal();
  const deviceOptions = ref([]);

  const orgBranch = ref('');

  async function getDevice() {
    deviceOptions.value = [];
    const params = {
      pageIndex: 1,
      pageSize: 10000,
      order: { property: 'id', direction: 'ASC' },
      criteria: {
        orgBranch: orgBranch.value,
      },
    };
    const { data } = await getvehicleList(params);
    deviceOptions.value = data;
  }

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '设备型号',
      dataIndex: 'deviceName',
    },
    {
      title: '定位时间',
      dataIndex: 'gpsTime',
    },
    {
      title: '状态',
      dataIndex: 'acc',
      customRender: ({ record, value }) => {
        switch (value) {
          case 'OFF':
            return <Tag color="gray">关闭</Tag>;
          case 'ON':
            return <Tag color="green">打开</Tag>;
          default:
            return <Tag color="yellow">所有</Tag>;
        }
      },
    },
  ];


  const [registerTable, { reload, getDataSource }] = useTable({
    api: getHistoryList,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 90,
      schemas: [
        {
          field: 'criteria.dateRange',
          component: 'RangePicker',
          label: '时间范围',
          colProps: { span: 6 },
          componentProps: {
            format: 'YYYY-MM-DD HH:mm:ss',
            showTime: true,
            presets: [
              { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
              {
                label: '昨天',
                value: [
                  dayjs().subtract(1, 'day').startOf('day'),
                  dayjs().subtract(1, 'day').endOf('day'),
                ],
              },
              {
                label: '本周',
                value: [dayjs().startOf('week'), dayjs().endOf('week')],
              },
              {
                label: '本月',
                value: [dayjs().startOf('month'), dayjs().endOf('month')],
              },
              {
                label: '上月',
                value: [
                  dayjs().subtract(1, 'month').startOf('month'),
                  dayjs().subtract(1, 'month').endOf('month'),
                ],
              },
            ],
          },
        },
        {
          field: 'criteria.acc',
          component: 'Select',
          label: '状态',
          colProps: { span: 6 },
          componentProps: {
            options: [
              { label: '打开', value: 'ON' },
              { label: '关闭', value: 'OFF' },
            ],
          },
        },
        {
          field: 'criteria.orgId',
          label: '所属机构',
          component: 'ApiTreeSelect',
          colProps: { span: 6 },
          componentProps: {
            api: getOrgTreeOptions,
            labelField: 'name',
            valueField: 'uid',
            allowClear: false,
            onChange: async (value) => {
              if (!value) orgBranch.value = '';
            },
            onSelect: async (value, item) => {
              console.log('onSelect', value, item);
              orgBranch.value = item.branch;
              await getDevice();
            },
            getPopupContainer: () => document.body,
          },
        },
        {
          field: 'criteria.deviceIds',
          component: 'ApiSelect',
          label: '设备',
          colProps: { span: 6 },
          componentProps: {
            options: deviceOptions,
            mode: 'multiple',
            fieldNames: { label: 'name', value: 'id' },
          },
        },
      ],
    },
    showTableSetting: true,
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    maxHeight: 500,
    rowKey: 'id',
    beforeFetch: ({ criteria, order }) => {
      if (criteria.dateRange && criteria.dateRange.length) {
        criteria.dateTimeRange = {
          begin: criteria.dateRange[0],
          end: criteria.dateRange[1],
        };
        delete criteria.dateRange;
      }
      order.property = 'gatewayTime';
    },
  });

  onMounted(async () => {
    await getDevice();
  });
</script>
