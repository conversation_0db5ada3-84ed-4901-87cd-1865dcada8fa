/**
 * 坐标系转换工具
 * 
 * 坐标系说明：
 * - WGS-84: 国际标准坐标系，GPS 原始坐标系，Google Maps 使用
 * - GCJ-02: 中国官方坐标系（火星坐标系），高德地图使用
 * - BD-09: 百度坐标系，百度地图使用
 */

// 坐标系类型枚举
export enum CoordinateSystem {
  WGS84 = 'WGS84',    // 国际标准坐标系
  GCJ02 = 'GCJ02',    // 中国官方坐标系（火星坐标系）
  BD09 = 'BD09',      // 百度坐标系
}

// 坐标点接口
export interface CoordinatePoint {
  lng: number;
  lat: number;
}

// 常量定义
const PI = Math.PI;
const X_PI = (PI * 3000.0) / 180.0;
const A = 6378245.0; // 长半轴
const EE = 0.00669342162296594323; // 偏心率平方

/**
 * 判断坐标是否在中国境内
 */
export function isInChina(lng: number, lat: number): boolean {
  return lng >= 72.004 && lng <= 137.8347 && lat >= 0.8293 && lat <= 55.8271;
}

/**
 * 转换纬度
 */
function transformLat(lng: number, lat: number): number {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
  ret += ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) / 3.0;
  ret += ((160.0 * Math.sin((lat / 12.0) * PI) + 320 * Math.sin((lat * PI) / 30.0)) * 2.0) / 3.0;
  return ret;
}

/**
 * 转换经度
 */
function transformLng(lng: number, lat: number): number {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
  ret += ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) / 3.0;
  ret += ((150.0 * Math.sin((lng / 12.0) * PI) + 300.0 * Math.sin((lng / 30.0) * PI)) * 2.0) / 3.0;
  return ret;
}

/**
 * GCJ-02 转 WGS-84
 * 火星坐标系转国际标准坐标系
 */
export function gcj02ToWgs84(lng: number, lat: number): CoordinatePoint {
  if (!isInChina(lng, lat)) {
    return { lng, lat };
  }

  let dlat = transformLat(lng - 105.0, lat - 35.0);
  let dlng = transformLng(lng - 105.0, lat - 35.0);
  const radlat = (lat / 180.0) * PI;
  let magic = Math.sin(radlat);
  magic = 1 - EE * magic * magic;
  const sqrtmagic = Math.sqrt(magic);
  dlat = (dlat * 180.0) / (((A * (1 - EE)) / (magic * sqrtmagic)) * PI);
  dlng = (dlng * 180.0) / ((A / sqrtmagic) * Math.cos(radlat) * PI);
  const mglat = lat - dlat;
  const mglng = lng - dlng;
  return { lng: mglng, lat: mglat };
}

/**
 * WGS-84 转 GCJ-02
 * 国际标准坐标系转火星坐标系
 */
export function wgs84ToGcj02(lng: number, lat: number): CoordinatePoint {
  if (!isInChina(lng, lat)) {
    return { lng, lat };
  }

  let dlat = transformLat(lng - 105.0, lat - 35.0);
  let dlng = transformLng(lng - 105.0, lat - 35.0);
  const radlat = (lat / 180.0) * PI;
  let magic = Math.sin(radlat);
  magic = 1 - EE * magic * magic;
  const sqrtmagic = Math.sqrt(magic);
  dlat = (dlat * 180.0) / (((A * (1 - EE)) / (magic * sqrtmagic)) * PI);
  dlng = (dlng * 180.0) / ((A / sqrtmagic) * Math.cos(radlat) * PI);
  const mglat = lat + dlat;
  const mglng = lng + dlng;
  return { lng: mglng, lat: mglat };
}

/**
 * GCJ-02 转 BD-09
 * 火星坐标系转百度坐标系
 */
export function gcj02ToBd09(lng: number, lat: number): CoordinatePoint {
  const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * X_PI);
  const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * X_PI);
  const bd_lng = z * Math.cos(theta) + 0.0065;
  const bd_lat = z * Math.sin(theta) + 0.006;
  return { lng: bd_lng, lat: bd_lat };
}

/**
 * BD-09 转 GCJ-02
 * 百度坐标系转火星坐标系
 */
export function bd09ToGcj02(lng: number, lat: number): CoordinatePoint {
  const x = lng - 0.0065;
  const y = lat - 0.006;
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI);
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);
  const gcj_lng = z * Math.cos(theta);
  const gcj_lat = z * Math.sin(theta);
  return { lng: gcj_lng, lat: gcj_lat };
}

/**
 * BD-09 转 WGS-84
 * 百度坐标系转国际标准坐标系
 */
export function bd09ToWgs84(lng: number, lat: number): CoordinatePoint {
  const gcj = bd09ToGcj02(lng, lat);
  return gcj02ToWgs84(gcj.lng, gcj.lat);
}

/**
 * WGS-84 转 BD-09
 * 国际标准坐标系转百度坐标系
 */
export function wgs84ToBd09(lng: number, lat: number): CoordinatePoint {
  const gcj = wgs84ToGcj02(lng, lat);
  return gcj02ToBd09(gcj.lng, gcj.lat);
}

/**
 * 通用坐标转换函数
 */
export function transformCoordinate(
  lng: number,
  lat: number,
  from: CoordinateSystem,
  to: CoordinateSystem
): CoordinatePoint {
  if (from === to) {
    return { lng, lat };
  }

  // 转换映射表
  const transformMap: Record<string, (lng: number, lat: number) => CoordinatePoint> = {
    [`${CoordinateSystem.GCJ02}_${CoordinateSystem.WGS84}`]: gcj02ToWgs84,
    [`${CoordinateSystem.WGS84}_${CoordinateSystem.GCJ02}`]: wgs84ToGcj02,
    [`${CoordinateSystem.GCJ02}_${CoordinateSystem.BD09}`]: gcj02ToBd09,
    [`${CoordinateSystem.BD09}_${CoordinateSystem.GCJ02}`]: bd09ToGcj02,
    [`${CoordinateSystem.BD09}_${CoordinateSystem.WGS84}`]: bd09ToWgs84,
    [`${CoordinateSystem.WGS84}_${CoordinateSystem.BD09}`]: wgs84ToBd09,
  };

  const key = `${from}_${to}`;
  const transformFunc = transformMap[key];

  if (!transformFunc) {
    throw new Error(`Unsupported coordinate transformation: ${from} -> ${to}`);
  }

  return transformFunc(lng, lat);
}

/**
 * 批量坐标转换
 */
export function transformCoordinates(
  coordinates: CoordinatePoint[],
  from: CoordinateSystem,
  to: CoordinateSystem
): CoordinatePoint[] {
  return coordinates.map(coord => transformCoordinate(coord.lng, coord.lat, from, to));
}

/**
 * 坐标转换工具类
 */
export class CoordinateTransformer {
  private sourceSystem: CoordinateSystem;
  private targetSystem: CoordinateSystem;

  constructor(from: CoordinateSystem, to: CoordinateSystem) {
    this.sourceSystem = from;
    this.targetSystem = to;
  }

  /**
   * 转换单个坐标点
   */
  transform(lng: number, lat: number): CoordinatePoint {
    return transformCoordinate(lng, lat, this.sourceSystem, this.targetSystem);
  }

  /**
   * 转换坐标数组
   */
  transformArray(coordinates: CoordinatePoint[]): CoordinatePoint[] {
    return transformCoordinates(coordinates, this.sourceSystem, this.targetSystem);
  }

  /**
   * 转换经纬度数组 [lng, lat]
   */
  transformLngLatArray(lngLat: [number, number]): [number, number] {
    const result = this.transform(lngLat[0], lngLat[1]);
    return [result.lng, result.lat];
  }
}

// 预定义的常用转换器
export const GCJ02_TO_WGS84 = new CoordinateTransformer(CoordinateSystem.GCJ02, CoordinateSystem.WGS84);
export const WGS84_TO_GCJ02 = new CoordinateTransformer(CoordinateSystem.WGS84, CoordinateSystem.GCJ02);
export const GCJ02_TO_BD09 = new CoordinateTransformer(CoordinateSystem.GCJ02, CoordinateSystem.BD09);
export const BD09_TO_GCJ02 = new CoordinateTransformer(CoordinateSystem.BD09, CoordinateSystem.GCJ02);
