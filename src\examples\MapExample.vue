<template>
  <div class="map-example">
    <h2>双地图系统示例</h2>

    <!-- 状态显示 -->
    <div class="status-panel">
      <div v-if="isLoading" class="loading">
        🔄 正在加载地图...
      </div>
      <div v-if="error" class="error">
        ❌ {{ error }}
      </div>
      <div v-if="provider" class="provider">
        🗺️ 当前使用: {{ provider === 'amap' ? '高德地图' : 'Google Maps' }}
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <button @click="addSampleMarker" :disabled="isLoading">添加标记</button>
      <button @click="addSamplePolyline" :disabled="isLoading">添加路线</button>
      <button @click="clearMapContent" :disabled="isLoading">清空地图</button>
      <button @click="getSampleAddress" :disabled="isLoading">获取地址</button>
      <button @click="switchToGoogle" :disabled="isLoading">切换到Google Maps</button>
      <button @click="switchToAMap" :disabled="isLoading">切换到高德地图</button>
      <button @click="testRawMapAccess" :disabled="isLoading">测试原始地图访问</button>
    </div>

    <!-- 地图容器 -->
    <div ref="mapContainer" class="map-container" style="width: 100%; height: 500px; border: 1px solid #ccc;"></div>

    <!-- 地址显示 -->
    <div v-if="currentAddress" class="address-display">
      📍 当前地址: {{ currentAddress }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useMap, MapProvider } from '@/hooks/web/useMap';

// 响应式数据
const mapContainer = ref<HTMLDivElement>();
const currentAddress = ref<string>('');

// 地图配置
const mapOptions = {
  center: [116.397428, 39.90923] as [number, number], // 北京天安门
  zoom: 13,
  // 高德地图特有配置
  amapOptions: {
    mapStyle: 'amap://styles/normal',
  },
  // Google Maps特有配置
  gmapOptions: {
    mapTypeId: 'roadmap' as google.maps.MapTypeId,
    disableDefaultUI: false,
    // 注意：language 和 region 现在会根据用户位置自动设置
    // 如果需要强制指定，可以在这里设置：
    // language: 'en',
    // region: 'US',
  },
};

// 网络检测配置（可选）
const detectionOptions = {
  timeout: 5000,
  fallbackToTimezone: true,
  fallbackToLanguage: true,
  useMultipleMethods: true,
};

// 初始化地图
const {
  provider,
  isLoading,
  error,
  mapRef,
  getMapInstance,
  getAMapInstance,
  getGoogleMapInstance,
  getRawMapInstance,
  executeProviderSpecific,
  setMapCenter,
  addMarker,
  addPolyline,
  clearMap,
  getMapBoundary,
  getAddress,
  createInfoWindow,
  switchProvider,
} = useMap(mapOptions, (map) => {
  console.log('地图初始化完成:', map);
}, detectionOptions);

// 组件挂载后绑定地图容器
onMounted(() => {
  if (mapContainer.value) {
    mapRef.value = mapContainer.value;
  }
});

// 添加示例标记
function addSampleMarker() {
  const marker = addMarker([116.397428, 39.90923], {
    title: '天安门广场',
    // 高德地图特有选项
    amapOptions: {
      icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
    },
    // Google Maps特有选项
    gmapOptions: {
      icon: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
    },
  });

  if (marker) {
    console.log('标记添加成功:', marker);

    // 创建信息窗口
    const infoWindow = createInfoWindow({
      amapOptions: {
        content: '<div>天安门广场</div>',
      },
      gmapOptions: {
        content: '<div>天安门广场</div>',
      },
    });

    if (infoWindow) {
      // 为标记添加点击事件显示信息窗口
      if (provider.value === MapProvider.AMAP) {
        (marker as AMap.Marker).on('click', () => {
          (infoWindow as AMap.InfoWindow).open(getMapInstance() as AMap.Map, [116.397428, 39.90923]);
        });
      } else {
        (marker as google.maps.Marker).addListener('click', () => {
          (infoWindow as google.maps.InfoWindow).open(getMapInstance() as google.maps.Map, marker as google.maps.Marker);
        });
      }
    }
  }
}

// 添加示例路线
function addSamplePolyline() {
  const points: [number, number][] = [
    [116.397428, 39.90923], // 天安门
    [116.39, 39.91],        // 中间点
    [116.385, 39.915],      // 终点
  ];

  const polyline = addPolyline(points, {
    strokeColor: '#FF0000',
    strokeWeight: 5,
    // 高德地图特有选项
    amapOptions: {
      strokeOpacity: 0.8,
    },
    // Google Maps特有选项
    gmapOptions: {
      strokeOpacity: 0.8,
    },
  });

  if (polyline) {
    console.log('路线添加成功:', polyline);
  }
}

// 清空地图内容
function clearMapContent() {
  const success = clearMap();
  if (success) {
    console.log('地图清空成功');
  }
}

// 获取示例地址
async function getSampleAddress() {
  try {
    const address = await getAddress([116.397428, 39.90923]);
    currentAddress.value = address;
    console.log('地址获取成功:', address);
  } catch (err) {
    console.error('获取地址失败:', err);
    currentAddress.value = '获取地址失败';
  }
}

// 切换到Google Maps
async function switchToGoogle() {
  try {
    await switchProvider(MapProvider.GOOGLE);
    console.log('已切换到Google Maps');
  } catch (err) {
    console.error('切换到Google Maps失败:', err);
  }
}

// 切换到高德地图
async function switchToAMap() {
  try {
    await switchProvider(MapProvider.AMAP);
    console.log('已切换到高德地图');
  } catch (err) {
    console.error('切换到高德地图失败:', err);
  }
}

// 测试原始地图访问
function testRawMapAccess() {
  console.log('=== 测试原始地图访问 ===');

  // 方法1: 获取通用地图实例
  const mapInstance = getMapInstance();
  console.log('通用地图实例:', mapInstance);

  // 方法2: 获取特定类型的地图实例
  const amapInstance = getAMapInstance();
  const gmapInstance = getGoogleMapInstance();
  console.log('高德地图实例:', amapInstance);
  console.log('Google Maps实例:', gmapInstance);

  // 方法3: 获取原始地图信息
  const rawMap = getRawMapInstance();
  console.log('原始地图信息:', rawMap);

  // 方法4: 执行特定于提供商的操作
  const result = executeProviderSpecific(
    // 高德地图操作
    (amapInstance) => {
      console.log('执行高德地图特有操作');
      // 例如：设置高德地图特有的样式
      amapInstance.setMapStyle('amap://styles/dark');
      return '高德地图操作完成';
    },
    // Google Maps操作
    (gmapInstance) => {
      console.log('执行Google Maps特有操作');
      // 例如：设置Google Maps特有的地图类型
      gmapInstance.setMapTypeId(google.maps.MapTypeId.SATELLITE);
      return 'Google Maps操作完成';
    }
  );

  console.log('操作结果:', result);

  // 方法5: 根据地图类型执行不同的操作
  if (rawMap.isAMap && rawMap.amapInstance) {
    console.log('当前是高德地图，执行高德地图特有功能');
    // 可以直接使用 rawMap.amapInstance 进行高德地图特有操作
    const bounds = rawMap.amapInstance.getBounds();
    console.log('高德地图边界:', bounds);
  } else if (rawMap.isGoogleMap && rawMap.googleMapInstance) {
    console.log('当前是Google Maps，执行Google Maps特有功能');
    // 可以直接使用 rawMap.googleMapInstance 进行Google Maps特有操作
    const bounds = rawMap.googleMapInstance.getBounds();
    console.log('Google Maps边界:', bounds);
  }
}
</script>

<style scoped>
.map-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.status-panel {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.loading {
  color: #1890ff;
}

.error {
  color: #ff4d4f;
}

.provider {
  color: #52c41a;
  font-weight: bold;
}

.control-panel {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.control-panel button {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.control-panel button:hover:not(:disabled) {
  background-color: #40a9ff;
}

.control-panel button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

.map-container {
  margin-bottom: 15px;
  border-radius: 4px;
  overflow: hidden;
}

.address-display {
  padding: 10px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  color: #389e0d;
}
</style>
