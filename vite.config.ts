/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:05
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-25 17:22:58
 * @FilePath     : \tzlink-gps-web\vite.config.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { defineApplicationConfig } from './build';
import vitePluginsAutoI18n, { YoudaoTranslator, BaiduTranslator, VolcengineTranslator, GoogleTranslator } from 'vite-auto-i18n-plugin';

export default defineApplicationConfig({
  overrides: {
    plugins: [
      //谷歌
      // vitePluginsAutoI18n({
      //   // enabled: false,
      //   // translateType: 'semi-auto',
      //   targetLangList: ['en', 'ar', 'tw', 'ja', 'ko', 'ru'],
      //   excludedPath: ['node_modules', 'src/components'],
      //   includePath: [/src\//],
      //   translator: new GoogleTranslator({
      //     proxyOption: {
      //       host: '127.0.0.1',
      //       port: 33210,
      //       headers: {
      //         'User-Agent': 'Node',
      //       },
      //     },
      //     interval: 3000,
      //   }),
      // }),
      //百度
      // vitePluginsAutoI18n({
      //   // enabled: false,
      //   // translateType: 'semi-auto',
      //   targetLangList: ['en', 'ar', 'zh-tw'], //, 'zh-CHS'
      //   excludedPath: ['node_modules', 'src/components'],
      //   includePath: [/src\//],
      //   translator: new BaiduTranslator({
      //     appId: '', // 百度翻译 AppId
      //     appKey: '', // 百度翻译 AppKey
      //   }),
      // }),
      //有道翻译
      // vitePluginsAutoI18n({
      //   // enabled: false,
      //   // translateType: 'semi-auto',
      //   targetLangList: ['en', 'ar', 'zh-tw'], //, 'zh-CHS'
      //   excludedPath: ['node_modules', 'src/components'],
      //   includePath: [/src\//],
      //   translator: new YoudaoTranslator({
      //     appId: '',
      //     appKey: '',
      //   }),
      // }),
      //火山引擎
      vitePluginsAutoI18n({
        enabled: false,
        // translateType: 'semi-auto',
        targetLangList: ['en', 'ar', 'tw', 'ja', 'ko', 'ru'],
        excludedPath: ['node_modules', 'src/components'],
        includePath: [/src\//],
        translator: new VolcengineTranslator({
          apiKey: '9bb48aeb-d3e0-4609-bd43-ec77ab4ef3fe',
          model: 'doubao-1.5-vision-pro-250328',
        }),
      }),
    ],
    optimizeDeps: {
      include: [
        'echarts/core',
        'echarts/charts',
        'echarts/components',
        'echarts/renderers',
        'qrcode',
        '@iconify/iconify',
        'ant-design-vue/es/locale/zh_CN',
        'ant-design-vue/es/locale/en_US',
      ],
    },
    server: {
      proxy: {
        '/basic-api': {
          target: 'http://*************:21188',
          // target: 'http://*************:21188',
          //target: 'http://localhost:2480',
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(new RegExp(`^/basic-api`), ''),
          // only https
          // secure: false
        },
        '/file': {
          target: 'http://*************:21188',
          changeOrigin: true,
          ws: true,
        },
        '/iphone': {
          target: 'http://devimages.apple.com',
          changeOrigin: true,
          ws: true,
        },
      },
    },
  },
});
