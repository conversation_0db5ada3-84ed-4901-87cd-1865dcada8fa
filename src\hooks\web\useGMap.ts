import { ref, unref, onMounted, onUnmounted } from 'vue';
import { type MapBoundary } from './useAMap';

export type GMapPoint = [number, number] | google.maps.LatLng;

// 地理编码结果接口
export interface GeocodeResult {
  status: google.maps.GeocoderStatus;
  results?: google.maps.GeocoderResult[];
}

/**
 * @description Google地图hook，便于管理地图加载、创建和销毁。
 * @param {google.maps.MapOptions} options Google地图实例化参数
 * @param {Function} callback 地图初始化完成后的回调函数
 */
export function useGMap(
  options: google.maps.MapOptions,
  callback?: (map: google.maps.Map) => void,
) {
  const map = ref<google.maps.Map | undefined>();
  const mapRef = ref<HTMLDivElement | undefined>();
  const geocoder = ref<google.maps.Geocoder | undefined>();
  const isInitialized = ref(false);

  /**
   * 初始化地图实例的核心逻辑
   */
  function initializeMap(): boolean {
    const container = unref(mapRef);
    if (!container) {
      console.error('Map container ref is not available');
      return false;
    }

    if (isInitialized.value) {
      console.warn('Map is already initialized');
      return true;
    }

    try {
      // 检查Google Maps API是否已加载
      if (!window.google || !window.google.maps) {
        console.error('Google Maps API is not loaded');
        return false;
      }

      map.value = new google.maps.Map(container, options);

      // 初始化地理编码器
      geocoder.value = new google.maps.Geocoder();

      isInitialized.value = true;

      if (typeof callback === 'function') {
        callback(map.value);
      }

      return true;
    } catch (error) {
      console.error('Failed to initialize Google Map:', error);
      isInitialized.value = false;
      return false;
    }
  }

  // dom挂载完成后初始化map实例
  onMounted(() => {
    if (unref(mapRef)) {
      initializeMap();
    }
  });

  /**
   * 手动初始化地图
   */
  function init(): boolean {
    return initializeMap();
  }

  /**
   * 销毁地图实例和相关资源
   */
  function destroy(): void {
    try {
      // Google Maps 没有destroy方法，只需要清空引用
      map.value = undefined;
      geocoder.value = undefined;
      isInitialized.value = false;
    } catch (error) {
      console.error('Failed to destroy Google Map:', error);
    }
  }

  // 组件卸载时销毁地图
  onUnmounted(() => {
    destroy();
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * 验证位置参数是否有效
   */
  function validatePosition(position: GMapPoint): boolean {
    if (Array.isArray(position)) {
      return validateCoordinates(position);
    }
    // 如果是google.maps.LatLng对象，检查其属性
    if (position && typeof position === 'object' && 'lng' in position && 'lat' in position) {
      return validateCoordinates([position.lng(), position.lat()]);
    }
    return false;
  }

  /**
   * @description 设置中心点
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenter(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      const latLng = new google.maps.LatLng(lngLat[1], lngLat[0]); // 注意：Google Maps是lat, lng顺序
      mapInstance.setCenter(latLng);
      return true;
    } catch (error) {
      console.error('Failed to set map center:', error);
      return false;
    }
  }

  /**
   * @description 设置中心点并自适应视野
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenterFitView(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      const latLng = new google.maps.LatLng(lngLat[1], lngLat[0]);
      mapInstance.setCenter(latLng);
      // Google Maps 没有直接的fitView方法，这里设置一个合适的缩放级别
      mapInstance.setZoom(15);
      return true;
    } catch (error) {
      console.error('Failed to set map center and fit view:', error);
      return false;
    }
  }

  /**
   * 添加标记点
   * @param position 位置坐标
   * @param options 标记选项
   */
  function addMarker(
    position: GMapPoint,
    options: google.maps.MarkerOptions = {},
  ): google.maps.Marker | null {
    if (!validatePosition(position)) {
      console.error('Invalid position provided:', position);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      let latLng: google.maps.LatLng;
      if (Array.isArray(position)) {
        latLng = new google.maps.LatLng(position[1], position[0]); // lat, lng顺序
      } else {
        latLng = position;
      }

      const marker = new google.maps.Marker({
        position: latLng,
        map: mapInstance,
        ...options,
      });

      return marker;
    } catch (error) {
      console.error('Failed to add marker:', error);
      return null;
    }
  }

  /**
   * 添加折线
   * @param points 路径点数组
   * @param options 折线选项
   */
  function addPolyline(
    points: GMapPoint[],
    options: Partial<google.maps.PolylineOptions> = {},
  ): google.maps.Polyline | null {
    if (!Array.isArray(points) || points.length < 2) {
      console.error('Invalid points array provided, at least 2 points required');
      return null;
    }

    // 验证所有点的有效性
    const invalidPoints = points.filter((point) => !validatePosition(point));
    if (invalidPoints.length > 0) {
      console.error('Invalid points found:', invalidPoints);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const path = points.map((point) => {
        if (Array.isArray(point)) {
          return new google.maps.LatLng(point[1], point[0]); // lat, lng顺序
        }
        return point;
      });

      const polyline = new google.maps.Polyline({
        path,
        map: mapInstance,
        strokeColor: '#4d78bc',
        strokeWeight: 10,
        ...options,
      });

      return polyline;
    } catch (error) {
      console.error('Failed to add polyline:', error);
      return null;
    }
  }

  /**
   * 获取地图实例
   */
  function getMapInstance(): google.maps.Map | undefined {
    return unref(map);
  }

  /**
   * 清空地图上的所有覆盖物
   */
  function clearMap(): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      // Google Maps 没有直接的clearMap方法，需要手动清除所有覆盖物
      // 这里只是一个基础实现，实际使用中可能需要维护覆盖物的引用
      console.warn('Google Maps clearMap: Please manually remove overlays');
      return true;
    } catch (error) {
      console.error('Failed to clear map:', error);
      return false;
    }
  }

  /**
   * 获取地图边界信息
   */
  function getMapBoundary(): MapBoundary | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const bounds = mapInstance.getBounds();
      if (!bounds) {
        console.error('Invalid bounds returned from map');
        return null;
      }

      const ne = bounds.getNorthEast();
      const sw = bounds.getSouthWest();

      return {
        maxLat: ne.lat(),
        maxLng: ne.lng(),
        minLat: sw.lat(),
        minLng: sw.lng(),
      };
    } catch (error) {
      console.error('Failed to get map boundary:', error);
      return null;
    }
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: GMapPoint): Promise<string> {
    if (!validatePosition(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise((resolve, reject) => {
      try {
        let latLng: google.maps.LatLng;
        if (Array.isArray(lngLat)) {
          latLng = new google.maps.LatLng(lngLat[1], lngLat[0]); // lat, lng顺序
        } else {
          latLng = lngLat;
        }

        geocoderInstance.geocode(
          { location: latLng },
          (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {
            if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
              resolve(results[0].formatted_address || '');
            } else if (status === google.maps.GeocoderStatus.ZERO_RESULTS) {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 创建信息窗口
   * @param options 信息窗口选项
   */
  function createInfoWindow(
    options: google.maps.InfoWindowOptions = {},
  ): google.maps.InfoWindow | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const infoWindow = new google.maps.InfoWindow({
        ...options,
      });
      return infoWindow;
    } catch (error) {
      console.error('Failed to create info window:', error);
      return null;
    }
  }

  return {
    mapRef,
    map,
    setMapCenter,
    addMarker,
    addPolyline,
    getMapInstance,
    clearMap,
    getMapBoundary,
    geocoder,
    destroy,
    getAddress,
    createInfoWindow,
    init,
    setMapCenterFitView,
    isInitialized,
    validateCoordinates,
    validatePosition,
  };
}

/**
 * @description 获取Google地图geocoder
 */
export function useGGeoCoder() {
  const geocoder = ref<google.maps.Geocoder | undefined>();
  const isInitialized = ref(false);

  /**
   * 初始化地理编码器
   */
  function initGeocoder(): boolean {
    if (isInitialized.value) {
      return true;
    }

    try {
      if (!window.google || !window.google.maps) {
        console.error('Google Maps API is not loaded');
        return false;
      }

      geocoder.value = new google.maps.Geocoder();
      isInitialized.value = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize geocoder:', error);
      isInitialized.value = false;
      return false;
    }
  }

  onMounted(() => {
    initGeocoder();
  });

  onUnmounted(() => {
    geocoder.value = undefined;
    isInitialized.value = false;
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: [number, number]): Promise<string> {
    if (!validateCoordinates(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise<string>((resolve, reject) => {
      try {
        const latLng = new google.maps.LatLng(lngLat[1], lngLat[0]); // lat, lng顺序

        geocoderInstance.geocode(
          { location: latLng },
          (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {
            if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
              resolve(results[0].formatted_address || '');
            } else if (status === google.maps.GeocoderStatus.ZERO_RESULTS) {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  return {
    geocoder,
    getAddress,
    isInitialized,
    initGeocoder,
    validateCoordinates,
  };
}

export type SetMapCenter = (lngLat: [number, number]) => boolean;
