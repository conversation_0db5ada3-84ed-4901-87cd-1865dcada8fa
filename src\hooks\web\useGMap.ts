import { ref, unref, onMounted, onUnmounted } from 'vue';
import { type MapBoundary } from './useAMap';

export type GMapPoint = [number, number] | google.maps.LatLng;

// 地理编码结果接口
export interface GeocodeResult {
  status: google.maps.GeocoderStatus;
  results?: google.maps.GeocoderResult[];
}

// Google Maps控件类型枚举
export enum GMapControlType {
  ZOOM = 'ZoomControl',
  MAPTYPE = 'MapTypeControl',
  STREETVIEW = 'StreetViewControl',
  FULLSCREEN = 'FullscreenControl',
  SCALE = 'ScaleControl',
  ROTATE = 'RotateControl',
}

// Google Maps控件配置接口
export interface GMapControlConfig {
  type: GMapControlType;
  position?: google.maps.ControlPosition;
  style?: any;
  options?: any;
}

// 预定义的Google Maps控件配置
export const DEFAULT_GMAP_CONTROL_CONFIGS: Record<string, GMapControlConfig> = {
  zoom: {
    type: GMapControlType.ZOOM,
    // position: google.maps.ControlPosition.RIGHT_CENTER,
  },
  maptype: {
    type: GMapControlType.MAPTYPE,
    // position: google.maps.ControlPosition.TOP_RIGHT,
    // options: {
    //   style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
    // },
  },
  streetview: {
    type: GMapControlType.STREETVIEW,
    // position: google.maps.ControlPosition.RIGHT_BOTTOM,
  },
  fullscreen: {
    type: GMapControlType.FULLSCREEN,
    // position: google.maps.ControlPosition.TOP_RIGHT,
  },
  scale: {
    type: GMapControlType.SCALE,
  },
  rotate: {
    type: GMapControlType.ROTATE,
    // position: google.maps.ControlPosition.RIGHT_CENTER,
  },
};

/**
 * @description Google地图hook，便于管理地图加载、创建和销毁。
 * @param {google.maps.MapOptions} options Google地图实例化参数
 * @param {Function} callback 地图初始化完成后的回调函数
 */
export function useGMap(
  options: google.maps.MapOptions,
  callback?: (map: google.maps.Map) => void,
) {
  const map = ref<google.maps.Map | undefined>();
  const mapRef = ref<HTMLDivElement | undefined>();
  const geocoder = ref<google.maps.Geocoder | undefined>();
  const isInitialized = ref(false);
  const controls = ref<Map<string, any>>(new Map());

  // 存储地图上的多边形和圆形
  const polygons = ref<Map<string, google.maps.Polygon>>(new Map());
  const circles = ref<Map<string, google.maps.Circle>>(new Map());

  // 绘制工具相关
  const drawingManager = ref<google.maps.drawing.DrawingManager | null>(null);
  const drawingOverlays = new Set<google.maps.Polygon | google.maps.Circle>();

  // 添加事件监听器引用，用于清理
  const drawingEventListeners = ref<google.maps.MapsEventListener[]>([]);

  /**
   * 初始化地图实例的核心逻辑
   */
  function initializeMap(): boolean {
    const container = unref(mapRef);
    DEFAULT_GMAP_CONTROL_CONFIGS.zoom.position = google.maps.ControlPosition.RIGHT_CENTER;
    DEFAULT_GMAP_CONTROL_CONFIGS.maptype.position = google.maps.ControlPosition.TOP_RIGHT;
    DEFAULT_GMAP_CONTROL_CONFIGS.maptype.options = {
      style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
    };
    DEFAULT_GMAP_CONTROL_CONFIGS.streetview.position = google.maps.ControlPosition.RIGHT_BOTTOM;
    DEFAULT_GMAP_CONTROL_CONFIGS.fullscreen.position = google.maps.ControlPosition.TOP_RIGHT;
    DEFAULT_GMAP_CONTROL_CONFIGS.rotate.position = google.maps.ControlPosition.RIGHT_CENTER;
    if (!container) {
      console.error('Map container ref is not available');
      return false;
    }

    if (isInitialized.value) {
      console.warn('Map is already initialized');
      return true;
    }

    try {
      // 检查Google Maps API是否已加载
      if (!window.google || !window.google.maps) {
        console.error('Google Maps API is not loaded');
        return false;
      }

      map.value = new google.maps.Map(container, options);

      // 初始化地理编码器
      geocoder.value = new google.maps.Geocoder();

      // 初始化绘制工具
      initializeDrawingTool();

      isInitialized.value = true;

      if (typeof callback === 'function') {
        callback(map.value);
      }

      return true;
    } catch (error) {
      console.error('Failed to initialize Google Map:', error);
      isInitialized.value = false;
      return false;
    }
  }

  // dom挂载完成后初始化map实例
  onMounted(() => {
    if (unref(mapRef)) {
      initializeMap();
    }
  });

  /**
   * 手动初始化地图
   */
  function init(containerRef: HTMLDivElement): boolean {
    mapRef.value = containerRef;
    return initializeMap();
  }

  /**
   * 销毁地图实例和相关资源
   */
  function destroy(): void {
    try {
      // Google Maps 没有destroy方法，只需要清空引用
      map.value = undefined;
      geocoder.value = undefined;
      isInitialized.value = false;
    } catch (error) {
      console.error('Failed to destroy Google Map:', error);
    }
  }

  // 组件卸载时销毁地图
  onUnmounted(() => {
    destroy();
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * 验证位置参数是否有效
   */
  function validatePosition(position: GMapPoint): boolean {
    if (Array.isArray(position)) {
      return validateCoordinates(position);
    }
    // 如果是google.maps.LatLng对象，检查其属性
    if (position && typeof position === 'object' && 'lng' in position && 'lat' in position) {
      return validateCoordinates([position.lng(), position.lat()]);
    }
    return false;
  }

  /**
   * @description 设置中心点
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenter(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      const latLng = new google.maps.LatLng(lngLat[1], lngLat[0]); // 注意：Google Maps是lat, lng顺序
      mapInstance.setCenter(latLng);
      return true;
    } catch (error) {
      console.error('Failed to set map center:', error);
      return false;
    }
  }

  /**
   * @description 设置中心点并自适应视野
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenterFitView(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      const latLng = new google.maps.LatLng(lngLat[1], lngLat[0]);
      mapInstance.setCenter(latLng);
      // Google Maps 没有直接的fitView方法，这里设置一个合适的缩放级别
      mapInstance.setZoom(15);
      return true;
    } catch (error) {
      console.error('Failed to set map center and fit view:', error);
      return false;
    }
  }

  /**
   * @description 统一的自适应视野方法 (Google Maps 实现)
   * @param overlays 覆盖物数组，如果不传则自适应所有覆盖物
   * @param immediately 是否立即执行（Google Maps 中忽略此参数）
   * @param avoid 四周边距（Google Maps 中转换为 padding）
   * @param maxZoom 最大缩放级别，默认 18
   */
  function setFitView(
    overlays?: any[],
    immediately: boolean = false,
    avoid: number[] = [60, 60, 60, 60],
    maxZoom: number = 18,
  ): boolean {
    console.log('immediately : ', immediately);
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      const bounds = new google.maps.LatLngBounds();
      let hasValidBounds = false;

      if (overlays && overlays.length > 0) {
        // 传入了特定覆盖物，计算这些覆盖物的边界
        overlays.forEach((overlay) => {
          if (overlay instanceof google.maps.Marker) {
            const position = overlay.getPosition();
            if (position) {
              bounds.extend(position);
              hasValidBounds = true;
            }
          } else if (overlay instanceof google.maps.Polyline) {
            const path = overlay.getPath();
            if (path) {
              path.forEach((latLng: google.maps.LatLng) => {
                bounds.extend(latLng);
                hasValidBounds = true;
              });
            }
          } else if (overlay instanceof google.maps.Polygon) {
            const paths = overlay.getPaths();
            if (paths) {
              paths.forEach((path) => {
                path.forEach((latLng: google.maps.LatLng) => {
                  bounds.extend(latLng);
                  hasValidBounds = true;
                });
              });
            }
          } else if (overlay instanceof google.maps.Circle) {
            const center = overlay.getCenter();
            const radius = overlay.getRadius();
            if (center && radius) {
              // 计算圆形的边界
              const ne = google.maps.geometry.spherical.computeOffset(center, radius, 45);
              const sw = google.maps.geometry.spherical.computeOffset(center, radius, 225);
              bounds.extend(ne);
              bounds.extend(sw);
              hasValidBounds = true;
            }
          }
        });
      } else {
        // 没有传入覆盖物，尝试获取所有已添加的覆盖物
        // 这里需要维护一个覆盖物列表，暂时使用当前视野
        console.warn('Google Maps setFitView without overlays: using current bounds');
        return true;
      }

      if (hasValidBounds) {
        // 设置边距
        const padding = {
          top: avoid[0] || 60,
          bottom: avoid[1] || 60,
          left: avoid[2] || 60,
          right: avoid[3] || 60,
        };

        // 应用边界
        mapInstance.fitBounds(bounds, padding);

        // 限制最大缩放级别
        google.maps.event.addListenerOnce(mapInstance, 'bounds_changed', () => {
          const currentZoom = mapInstance.getZoom();
          if (currentZoom && currentZoom > maxZoom) {
            mapInstance.setZoom(maxZoom);
          }
        });
      }

      return true;
    } catch (error) {
      console.error('Failed to set fit view:', error);
      return false;
    }
  }

  /**
   * @description 设置地图缩放级别
   * @param zoom 缩放级别，Google Maps 通常范围 [1, 20]
   * @param immediately 是否立即过渡到目标位置（Google Maps 不支持此参数，保留用于接口统一）
   * @param duration 动画过渡时长（Google Maps 不支持此参数，保留用于接口统一）
   */
  function setZoom(zoom: number, immediately: boolean = false, duration?: number): boolean {
    console.log('immediately : ', immediately, duration);
    if (typeof zoom !== 'number' || zoom < 1 || zoom > 20) {
      console.error(
        'Invalid zoom level provided:',
        zoom,
        'Expected range: [1, 20] for Google Maps',
      );
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      mapInstance.setZoom(zoom);
      return true;
    } catch (error) {
      console.error('Failed to set zoom level:', error);
      return false;
    }
  }

  /**
   * 添加标记点
   * @param position 位置坐标
   * @param options 标记选项
   */
  function addMarker(
    position: GMapPoint,
    options: google.maps.MarkerOptions & { extData?: any } = {},
  ): google.maps.Marker | null {
    if (!validatePosition(position)) {
      console.error('Invalid position provided:', position);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      let latLng: google.maps.LatLng;
      if (Array.isArray(position)) {
        latLng = new google.maps.LatLng(position[1], position[0]); // lat, lng顺序
      } else {
        latLng = position;
      }

      // 提取 extData，避免传递给 Google Maps Marker 构造函数
      const { extData, ...markerOptions } = options;

      const marker = new google.maps.Marker({
        position: latLng,
        map: mapInstance,
        ...markerOptions,
      });

      // 直接将 extData 设置到 marker 对象上，模拟 AMap 的 extData 功能
      if (extData !== undefined) {
        (marker as any).extData = extData;

        // 添加 getExtData 方法，模拟 AMap.Marker 的 getExtData() 方法
        (marker as any).getExtData = function () {
          return (this as any).extData;
        };

        // 添加 setExtData 方法，模拟 AMap.Marker 的 setExtData() 方法
        (marker as any).setExtData = function (data: any) {
          (this as any).extData = data;
        };
      }

      return marker;
    } catch (error) {
      console.error('Failed to add marker:', error);
      return null;
    }
  }

  /**
   * 添加折线
   * @param points 路径点数组
   * @param options 折线选项
   */
  function addPolyline(
    points: GMapPoint[],
    options: Partial<google.maps.PolylineOptions> = {},
  ): google.maps.Polyline | null {
    if (!Array.isArray(points) || points.length < 2) {
      console.error('Invalid points array provided, at least 2 points required');
      return null;
    }

    // 验证所有点的有效性
    const invalidPoints = points.filter((point) => !validatePosition(point));
    if (invalidPoints.length > 0) {
      console.error('Invalid points found:', invalidPoints);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const path = points.map((point) => {
        if (Array.isArray(point)) {
          return new google.maps.LatLng(point[1], point[0]); // lat, lng顺序
        }
        return point;
      });

      const polyline = new google.maps.Polyline({
        path,
        map: mapInstance,
        strokeColor: '#4d78bc',
        strokeWeight: 10,
        ...options,
      });

      return polyline;
    } catch (error) {
      console.error('Failed to add polyline:', error);
      return null;
    }
  }

  /**
   * 添加多边形
   * @param points 多边形路径点数组
   * @param options 多边形选项
   */
  function addPolygon(
    points: GMapPoint[] | GMapPoint[][],
    options: Partial<google.maps.PolygonOptions> & { extData?: any } = {},
  ): google.maps.Polygon | null {
    if (!Array.isArray(points) || points.length < 3) {
      console.error('Invalid points array provided, at least 3 points required for polygon');
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      // 提取 extData，避免传递给 Google Maps Polygon 构造函数
      const { extData, ...polygonOptions } = options;

      // 转换坐标格式
      let paths: google.maps.LatLng[] | google.maps.LatLng[][];

      if (points.length > 0 && Array.isArray(points[0]) && Array.isArray(points[0][0])) {
        // 多个路径（带孔的多边形）
        paths = (points as GMapPoint[][]).map((path) =>
          path.map((point) => {
            if (Array.isArray(point)) {
              return new google.maps.LatLng(point[1], point[0]); // lat, lng顺序
            }
            return point;
          }),
        );
      } else {
        // 单个路径
        paths = (points as GMapPoint[]).map((point) => {
          if (Array.isArray(point)) {
            return new google.maps.LatLng(point[1], point[0]); // lat, lng顺序
          }
          return point;
        });
      }

      const polygon = new google.maps.Polygon({
        paths,
        map: mapInstance,
        ...polygonOptions,
      });

      // 自动生成ID并存储多边形
      const polygonId = `polygon_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      polygons.value.set(polygonId, polygon);

      // 直接将 extData 设置到 polygon 对象上，模拟 AMap 的 extData 功能
      if (extData !== undefined) {
        (polygon as any).extData = extData;

        // 添加 getExtData 方法，模拟 AMap.Polygon 的 getExtData() 方法
        (polygon as any).getExtData = function () {
          return (this as any).extData;
        };

        // 添加 setExtData 方法，模拟 AMap.Polygon 的 setExtData() 方法
        (polygon as any).setExtData = function (data: any) {
          (this as any).extData = data;
        };
      }

      // 将ID存储到多边形对象上，方便后续查找
      (polygon as any)._internalId = polygonId;

      return polygon;
    } catch (error) {
      console.error('Failed to add polygon:', error);
      return null;
    }
  }

  /**
   * 添加圆形
   * @param center 圆心坐标
   * @param radius 半径（米）
   * @param options 圆形选项
   */
  function addCircle(
    center: GMapPoint,
    radius: number,
    options: Partial<google.maps.CircleOptions> & { extData?: any } = {},
  ): google.maps.Circle | null {
    if (!validatePosition(center)) {
      console.error('Invalid center position provided:', center);
      return null;
    }

    if (typeof radius !== 'number' || radius <= 0) {
      console.error('Invalid radius provided, must be a positive number:', radius);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      // 提取 extData，避免传递给 Google Maps Circle 构造函数
      const { extData, ...circleOptions } = options;

      // 转换坐标格式
      let centerLatLng: google.maps.LatLng;
      if (Array.isArray(center)) {
        centerLatLng = new google.maps.LatLng(center[1], center[0]); // lat, lng顺序
      } else {
        centerLatLng = center;
      }

      const circle = new google.maps.Circle({
        center: centerLatLng,
        radius,
        map: mapInstance,
        ...circleOptions,
      });

      // 自动生成ID并存储圆形
      const circleId = `circle_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      circles.value.set(circleId, circle);

      // 直接将 extData 设置到 circle 对象上，模拟 AMap 的 extData 功能
      if (extData !== undefined) {
        (circle as any).extData = extData;

        // 添加 getExtData 方法，模拟 AMap.Circle 的 getExtData() 方法
        (circle as any).getExtData = function () {
          return (this as any).extData;
        };

        // 添加 setExtData 方法，模拟 AMap.Circle 的 setExtData() 方法
        (circle as any).setExtData = function (data: any) {
          (this as any).extData = data;
        };
      }

      // 将ID存储到圆形对象上，方便后续查找
      (circle as any)._internalId = circleId;

      return circle;
    } catch (error) {
      console.error('Failed to add circle:', error);
      return null;
    }
  }

  /**
   * 移除多边形
   * @param polygon 要移除的多边形对象或多边形数组
   */
  function removePolygon(polygon: google.maps.Polygon | google.maps.Polygon[]): boolean {
    try {
      const polygonsToRemove = Array.isArray(polygon) ? polygon : [polygon];

      polygonsToRemove.forEach((poly) => {
        // 从地图上移除
        poly.setMap(null);

        // 从存储中移除
        const internalId = (poly as any)._internalId;
        if (internalId && polygons.value.has(internalId)) {
          polygons.value.delete(internalId);
        }
      });

      return true;
    } catch (error) {
      console.error('Failed to remove polygon:', error);
      return false;
    }
  }

  /**
   * 移除圆形
   * @param circle 要移除的圆形对象或圆形数组
   */
  function removeCircle(circle: google.maps.Circle | google.maps.Circle[]): boolean {
    try {
      const circlesToRemove = Array.isArray(circle) ? circle : [circle];

      circlesToRemove.forEach((circ) => {
        // 从地图上移除
        circ.setMap(null);

        // 从存储中移除
        const internalId = (circ as any)._internalId;
        if (internalId && circles.value.has(internalId)) {
          circles.value.delete(internalId);
        }
      });

      return true;
    } catch (error) {
      console.error('Failed to remove circle:', error);
      return false;
    }
  }

  /**
   * 移除所有多边形
   */
  function removeAllPolygons(): boolean {
    try {
      // 移除所有存储的多边形
      polygons.value.forEach((polygon) => {
        polygon.setMap(null);
      });

      // 清空存储
      polygons.value.clear();

      return true;
    } catch (error) {
      console.error('Failed to remove all polygons:', error);
      return false;
    }
  }

  /**
   * 移除所有圆形
   */
  function removeAllCircles(): boolean {
    try {
      // 移除所有存储的圆形
      circles.value.forEach((circle) => {
        circle.setMap(null);
      });

      // 清空存储
      circles.value.clear();

      return true;
    } catch (error) {
      console.error('Failed to remove all circles:', error);
      return false;
    }
  }

  /**
   * 移除所有多边形和圆形
   */
  function removeAllPolygonsAndCircles(): boolean {
    const polygonResult = removeAllPolygons();
    const circleResult = removeAllCircles();
    return polygonResult && circleResult;
  }

  /**
   * 获取所有多边形
   */
  function getAllPolygons(): google.maps.Polygon[] {
    return Array.from(polygons.value.values());
  }

  /**
   * 获取所有圆形
   */
  function getAllCircles(): google.maps.Circle[] {
    return Array.from(circles.value.values());
  }

  /**
   * 初始化绘制工具
   */
  function initializeDrawingTool(): void {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available for drawing tool initialization');
      return;
    }

    try {
      // 检查 Drawing Library 是否已加载
      if (!google.maps.drawing) {
        console.error('Google Maps Drawing Library is not loaded');
        return;
      }

      // 如果已存在Drawing Manager，先清理
      if (drawingManager.value) {
        cleanupDrawingManager();
      }

      drawingManager.value = new google.maps.drawing.DrawingManager({
        drawingMode: null,
        drawingControl: false, // 我们通过代码控制绘制模式
        drawingControlOptions: {
          position: google.maps.ControlPosition.TOP_CENTER,
          drawingModes: [
            google.maps.drawing.OverlayType.POLYGON,
            google.maps.drawing.OverlayType.CIRCLE,
          ],
        },
        polygonOptions: {
          fillColor: '#00b0ff',
          fillOpacity: 0.3,
          strokeColor: '#80d8ff',
          strokeWeight: 2,
          strokeOpacity: 0.8,
          editable: false,
          draggable: false,
        },
        circleOptions: {
          fillColor: '#00b0ff',
          fillOpacity: 0.3,
          strokeColor: '#80d8ff',
          strokeWeight: 2,
          strokeOpacity: 0.8,
          editable: false,
          draggable: false,
        },
      });

      drawingManager.value.setMap(mapInstance);

      // 监听绘制完成事件
      const overlayCompleteListener = google.maps.event.addListener(
        drawingManager.value,
        'overlaycomplete',
        (event: any) => {
          // 保存新的绘制图形
          const newOverlay = event.overlay;
          drawingOverlays.add(newOverlay);

          // 停止绘制模式
          if (drawingManager.value) {
            drawingManager.value.setDrawingMode(null);
          }
        },
      );

      // 保存事件监听器引用
      drawingEventListeners.value = [overlayCompleteListener];
    } catch (error) {
      console.error('Failed to initialize drawing tool:', error);
    }
  }

  /**
   * 开始绘制多边形
   * @param options 多边形样式选项
   */
  function startDrawPolygon(options: Partial<google.maps.PolygonOptions> = {}): void {
    if (!drawingManager.value) {
      console.error('Drawing tool is not initialized');
      return;
    }

    const defaultOptions: Partial<google.maps.PolygonOptions> = {
      fillColor: '#00b0ff',
      fillOpacity: 0.3,
      strokeColor: '#80d8ff',
      strokeWeight: 2,
      strokeOpacity: 0.8,
      editable: false,
      draggable: false,
    };

    // 更新多边形选项
    drawingManager.value.setOptions({
      polygonOptions: {
        ...defaultOptions,
        ...options,
      },
    });

    // 开始绘制多边形
    drawingManager.value.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
  }

  /**
   * 开始绘制圆形
   * @param options 圆形样式选项
   */
  function startDrawCircle(options: Partial<google.maps.CircleOptions> = {}): void {
    if (!drawingManager.value) {
      console.error('Drawing tool is not initialized');
      return;
    }

    const defaultOptions: Partial<google.maps.CircleOptions> = {
      fillColor: '#00b0ff',
      fillOpacity: 0.3,
      strokeColor: '#80d8ff',
      strokeWeight: 2,
      strokeOpacity: 0.8,
      editable: false,
      draggable: false,
    };

    // 更新圆形选项
    drawingManager.value.setOptions({
      circleOptions: {
        ...defaultOptions,
        ...options,
      },
    });

    // 开始绘制圆形
    drawingManager.value.setDrawingMode(google.maps.drawing.OverlayType.CIRCLE);
  }

  /**
   * 停止绘制
   */
  function stopDrawing(): void {
    if (drawingManager.value) {
      drawingManager.value.setDrawingMode(null);
    }
  }

  /**
   * 清理Drawing Manager
   */
  function cleanupDrawingManager(): void {
    if (drawingManager.value) {
      try {
        // 清理事件监听器
        drawingEventListeners.value.forEach((listener) => {
          google.maps.event.removeListener(listener);
        });
        drawingEventListeners.value = [];

        // 停止绘制模式
        drawingManager.value.setDrawingMode(null);

        // 从地图上移除
        drawingManager.value.setMap(null);

        // 清理所有事件监听器
        google.maps.event.clearInstanceListeners(drawingManager.value);

        // 清空引用
        drawingManager.value = null;
      } catch (error) {
        console.error('Error cleaning up drawing manager:', error);
      }
    }
  }

  /**
   * 清除绘制的图形
   */
  function clearDrawingOverlays(): void {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available for clearing drawing overlays');
      return;
    }

    const totalOverlays = drawingOverlays.size;
    console.log('Clearing drawing overlays, total count:', totalOverlays);

    // 1. 清除主要跟踪的绘制图形
    drawingOverlays.forEach((overlay, index) => {
      try {
        console.log(`Removing main drawing overlay ${index}:`, overlay);

        // 强制从地图上移除
        overlay.setMap(null);

        // 清理overlay的所有事件监听器
        google.maps.event.clearInstanceListeners(overlay);

        // 如果overlay有unbindAll方法，调用它
        if (typeof overlay.unbindAll === 'function') {
          overlay.unbindAll();
        }

        console.log(`Successfully removed main drawing overlay ${index}`);
      } catch (error) {
        console.error(`Failed to remove main drawing overlay ${index}:`, error);
      }
    });

    // 3. 清空所有跟踪数组和集合
    const previousCount = drawingOverlays.size;
    drawingOverlays.clear();

    // 4. 重置 DrawingManager 状态
    if (drawingManager.value) {
      try {
        cleanupDrawingManager();
      } catch (error) {
        console.error('Error resetting DrawingManager:', error);
      }
    }

    // 5. 强制地图重绘和状态刷新
    try {
      // 触发地图重绘事件
      google.maps.event.trigger(mapInstance, 'resize');

      // 强制重新渲染地图
      const currentCenter = mapInstance.getCenter();
      const currentZoom = mapInstance.getZoom();

      if (currentCenter && currentZoom !== undefined) {
        // 微调地图位置来强制重绘
        mapInstance.panBy(0, 0);

        // 确保缩放级别保持不变
        setTimeout(() => {
          if (mapInstance.getZoom() !== currentZoom) {
            mapInstance.setZoom(currentZoom);
          }
        }, 50);
      }
    } catch (error) {
      console.error('Error triggering map refresh:', error);
    }

    drawingManager.value?.unbindAll();

    console.log(`Drawing overlays cleared successfully: ${previousCount} overlays removed`);
  }

  /**
   * 获取当前绘制的图形
   */
  function getDrawingOverlays(): (google.maps.Polygon | google.maps.Circle)[] {
    return [...drawingOverlays];
  }

  /**
   * 获取绘制图形的几何数据
   */
  function getDrawingData(): Array<{
    type: 'polygon' | 'circle';
    data: any;
  }> {
    return [...drawingOverlays].map((overlay) => {
      if (overlay instanceof google.maps.Polygon) {
        const path = overlay.getPath();
        const coordinates: GMapPoint[] = [];
        for (let i = 0; i < path.getLength(); i++) {
          const point = path.getAt(i);
          coordinates.push([point.lng(), point.lat()]);
        }
        return {
          type: 'polygon',
          data: coordinates,
        };
      } else if (overlay instanceof google.maps.Circle) {
        const center = overlay.getCenter();
        return {
          type: 'circle',
          data: {
            center: center ? [center.lng(), center.lat()] : [0, 0],
            radius: overlay.getRadius(),
          },
        };
      }
      return { type: 'polygon', data: null };
    });
  }

  /**
   * 设置地图显示的城市
   * @param cityName 城市名称，如"北京"、"上海"等
   */
  function setCity(cityName: string): Promise<boolean> {
    return new Promise((resolve) => {
      const mapInstance = unref(map);
      const geocoderInstance = unref(geocoder);

      if (!mapInstance || !geocoderInstance) {
        console.error('Map instance or geocoder is not available');
        resolve(false);
        return;
      }

      try {
        // 使用 Google Maps Geocoding API 查找城市位置
        geocoderInstance.geocode(
          {
            address: cityName,
            componentRestrictions: {
              // 可以根据需要添加国家限制
              // country: 'CN' // 限制在中国
            },
          },
          (results, status) => {
            if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
              const location = results[0].geometry.location;
              const viewport = results[0].geometry.viewport;

              if (viewport) {
                // 如果有视口信息，使用 fitBounds 来显示整个城市区域
                mapInstance.fitBounds(viewport);
              } else {
                // 否则只设置中心点和合适的缩放级别
                mapInstance.setCenter(location);
                mapInstance.setZoom(10); // 城市级别的缩放
              }

              console.log(`Successfully set city to: ${cityName}`);
              resolve(true);
            } else {
              console.error(`Failed to find city: ${cityName}`, status);
              resolve(false);
            }
          },
        );
      } catch (error) {
        console.error('Error setting city:', error);
        resolve(false);
      }
    });
  }

  /**
   * 行政区域搜索（Google Maps版本）
   * 使用多种数据源来获取行政区域边界数据
   * @param keyword 搜索关键词（行政区域名称）
   * @param options 搜索选项
   */
  async function districtSearch(
    keyword: string,
    options: {
      level?: 'country' | 'province' | 'city' | 'district';
      subdistrict?: number;
      extensions?: 'base' | 'all';
    } = {},
  ): Promise<{
    status: string;
    info: string;
    districtList: Array<{
      name: string;
      center: GMapPoint;
      boundaries?: GMapPoint[][];
      adcode?: string;
      level: string;
    }>;
  }> {
    try {
      const mapInstance = unref(map);
      if (!mapInstance) {
        throw new Error('Map instance is not available');
      }

      // 方案1: 使用 Google Maps Geocoding API 获取基本信息
      const geocoderInstance = unref(geocoder);
      if (!geocoderInstance) {
        throw new Error('Geocoder is not available');
      }

      const geocodeResult = await new Promise<google.maps.GeocoderResult[]>((resolve, reject) => {
        geocoderInstance.geocode(
          {
            address: keyword,
            componentRestrictions: { country: 'CN' }, // 限制在中国
          },
          (results, status) => {
            if (status === 'OK' && results) {
              resolve(results);
            } else {
              reject(new Error(`Geocoding failed: ${status}`));
            }
          },
        );
      });

      if (geocodeResult.length === 0) {
        return {
          status: 'no_data',
          info: 'No results found',
          districtList: [],
        };
      }

      const result = geocodeResult[0];
      const location = result.geometry.location;
      const center: GMapPoint = [location.lng(), location.lat()];

      // 方案2: 尝试从 OpenStreetMap Nominatim API 获取边界数据
      let boundaries: GMapPoint[][] | undefined;

      if (options.extensions === 'all') {
        try {
          boundaries = await fetchDistrictBoundariesFromNominatim(keyword);
        } catch (error) {
          console.warn('Failed to fetch boundaries from Nominatim:', error);
          // 如果获取边界失败，可以尝试其他数据源或使用默认边界
        }
      }

      // 确定行政级别
      let level = 'district';
      const types = result.types;
      if (types.includes('country')) {
        level = 'country';
      } else if (types.includes('administrative_area_level_1')) {
        level = 'province';
      } else if (types.includes('administrative_area_level_2')) {
        level = 'city';
      } else if (types.includes('administrative_area_level_3') || types.includes('sublocality')) {
        level = 'district';
      }

      return {
        status: 'complete',
        info: 'OK',
        districtList: [
          {
            name: result.formatted_address,
            center,
            boundaries,
            level,
          },
        ],
      };
    } catch (error) {
      console.error('District search failed:', error);
      return {
        status: 'error',
        info: error instanceof Error ? error.message : 'Unknown error',
        districtList: [],
      };
    }
  }

  /**
   * 从 OpenStreetMap Nominatim API 获取行政区域边界
   * @param keyword 搜索关键词
   */
  async function fetchDistrictBoundariesFromNominatim(keyword: string): Promise<GMapPoint[][]> {
    const nominatimUrl = `https://nominatim.openstreetmap.org/search?format=json&polygon_geojson=1&addressdetails=1&limit=1&q=${encodeURIComponent(keyword)}, China`;

    const response = await fetch(nominatimUrl, {
      headers: {
        'User-Agent': 'TzlinkGPS/1.0', // Nominatim 要求设置 User-Agent
      },
    });

    if (!response.ok) {
      throw new Error(`Nominatim API request failed: ${response.status}`);
    }

    const data = await response.json();

    if (!data || data.length === 0) {
      throw new Error('No boundary data found');
    }

    const result = data[0];
    if (!result.geojson || !result.geojson.coordinates) {
      throw new Error('No geometry data in response');
    }

    // 转换 GeoJSON 坐标到 Google Maps 格式
    const coordinates = result.geojson.coordinates;
    const boundaries: GMapPoint[][] = [];

    if (result.geojson.type === 'Polygon') {
      // 单个多边形
      coordinates.forEach((ring: number[][]) => {
        const boundary = ring.map((coord: number[]) => [coord[0], coord[1]] as GMapPoint);
        boundaries.push(boundary);
      });
    } else if (result.geojson.type === 'MultiPolygon') {
      // 多个多边形
      coordinates.forEach((polygon: number[][][]) => {
        polygon.forEach((ring: number[][]) => {
          const boundary = ring.map((coord: number[]) => [coord[0], coord[1]] as GMapPoint);
          boundaries.push(boundary);
        });
      });
    }

    return boundaries;
  }

  /**
   * 获取地图实例
   */
  function getMapInstance(): google.maps.Map | undefined {
    return unref(map);
  }

  /**
   * 清空地图上的所有覆盖物
   */
  function clearMap(): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      // Google Maps 没有直接的clearMap方法，需要手动清除所有覆盖物
      // 这里只是一个基础实现，实际使用中可能需要维护覆盖物的引用
      console.warn('Google Maps clearMap: Please manually remove overlays');
      return true;
    } catch (error) {
      console.error('Failed to clear map:', error);
      return false;
    }
  }

  /**
   * 获取地图边界信息
   */
  function getMapBoundary(): MapBoundary | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const bounds = mapInstance.getBounds();
      if (!bounds) {
        console.error('Invalid bounds returned from map');
        return null;
      }

      const ne = bounds.getNorthEast();
      const sw = bounds.getSouthWest();

      return {
        maxLat: ne.lat(),
        maxLng: ne.lng(),
        minLat: sw.lat(),
        minLng: sw.lng(),
      };
    } catch (error) {
      console.error('Failed to get map boundary:', error);
      return null;
    }
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: GMapPoint): Promise<string> {
    if (!validatePosition(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise((resolve, reject) => {
      try {
        let latLng: google.maps.LatLng;
        if (Array.isArray(lngLat)) {
          latLng = new google.maps.LatLng(lngLat[1], lngLat[0]); // lat, lng顺序
        } else {
          latLng = lngLat;
        }

        geocoderInstance.geocode(
          { location: latLng },
          (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {
            if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
              resolve(results[0].formatted_address || '');
            } else if (status === google.maps.GeocoderStatus.ZERO_RESULTS) {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 手动触发地图对象的事件
   * @param target 目标对象（地图、marker、polyline 等）
   * @param eventType 事件类型
   * @param eventData 事件数据（可选）
   */
  function triggerEvent(
    target:
      | google.maps.Map
      | google.maps.Marker
      | google.maps.Polyline
      | google.maps.Polygon
      | google.maps.Circle
      | any,
    eventType: string,
    eventData?: any,
  ): boolean {
    if (!target) {
      console.error('Target object is not available');
      return false;
    }

    try {
      // 使用 google.maps.event.trigger 手动触发事件
      google.maps.event.trigger(target, eventType, eventData);
      return true;
    } catch (error) {
      console.error('Failed to trigger event:', error);
      return false;
    }
  }

  /**
   * 为地图对象添加事件监听器（通用方法）
   * @param target 目标对象（地图、marker、polyline、polygon、circle 等）
   * @param eventType 事件类型
   * @param handler 事件处理函数
   */
  function addEventListener(
    target:
      | google.maps.Map
      | google.maps.Marker
      | google.maps.Polyline
      | google.maps.Polygon
      | google.maps.Circle
      | any,
    eventType: string,
    handler: (event: any) => void,
  ): void {
    if (!target) {
      console.error('Target object is not available');
      return;
    }

    try {
      target.addListener(eventType, handler);
    } catch (error) {
      console.error('Failed to add event listener:', error);
    }
  }

  /**
   * 移除地图对象的事件监听器（通用方法）
   * @param target 目标对象（地图、marker、polyline、polygon、circle 等）
   * @param eventType 事件类型
   * @param handler 事件处理函数（可选，不传则移除该事件类型的所有监听器）
   */
  function removeEventListener(
    target:
      | google.maps.Map
      | google.maps.Marker
      | google.maps.Polyline
      | google.maps.Polygon
      | google.maps.Circle
      | any,
    eventType: string,
    handler?: (event: any) => void,
  ): void {
    if (!target) {
      console.error('Target object is not available');
      return;
    }

    try {
      // Google Maps 没有直接的 off 方法，需要使用 clearListeners
      if (handler) {
        // 对于特定的处理函数，Google Maps API 没有直接的移除方法
        // 这里我们清除所有该类型的监听器
        google.maps.event.clearListeners(target, eventType);
      } else {
        // 移除所有该类型的事件监听器
        google.maps.event.clearListeners(target, eventType);
      }
    } catch (error) {
      console.error('Failed to remove event listener:', error);
    }
  }

  // 导入统一类型
  type UnifiedSize = [number, number] | { width: number; height: number };
  type UnifiedOffset = [number, number] | { x: number; y: number };

  // 统一的 InfoWindow 事件参数接口
  interface UnifiedInfoWindowEvent {
    type: string;
    target: UnifiedInfoWindow;
    lnglat: [number, number] | null;
    pixel: [number, number] | null;
    originalEvent: any;
    timestamp: number;
    domEvent?: Event;
  }

  // 统一的 InfoWindow 事件处理函数类型
  type UnifiedInfoWindowEventHandler = (event: UnifiedInfoWindowEvent) => void;

  /**
   * 统一信息窗口包装器接口
   */
  interface UnifiedInfoWindow {
    open(mapOrPosition?: any, position?: any): void;
    close(): void;
    setContent(content: string | HTMLElement): void;
    getContent(): string | HTMLElement | null;
    setPosition(position: any): void;
    getPosition(): any | null;
    setOffset(offset: UnifiedOffset): void;
    getOffset(): UnifiedOffset | null;
    setSize(size: UnifiedSize): void;
    getSize(): UnifiedSize | null;
    addEventListener(event: string, handler: UnifiedInfoWindowEventHandler): void;
    removeEventListener(event: string, handler: UnifiedInfoWindowEventHandler): void;
    getRawInstance(): google.maps.InfoWindow | null;
  }

  /**
   * 创建统一的信息窗口
   * @param options 信息窗口选项
   */
  function createInfoWindow(options: google.maps.InfoWindowOptions = {}): UnifiedInfoWindow | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const infoWindow = new google.maps.InfoWindow({
        ...options,
      });

      // 创建统一的包装器
      const wrapper: UnifiedInfoWindow = {
        /**
         * 统一的 open 方法
         * 支持多种调用方式：
         * - open() - 在当前位置打开
         * - open(position) - 在指定位置打开
         * - open(map, position) - 在指定地图和位置打开
         * - open(map, marker) - 在指定地图和标记位置打开
         */
        open(mapOrPosition?: any, position?: any): void {
          try {
            if (arguments.length === 0) {
              // open() - 在地图中心打开
              infoWindow.open({
                map: mapInstance,
              });
            } else if (arguments.length === 1) {
              if (
                Array.isArray(mapOrPosition) ||
                (mapOrPosition && typeof mapOrPosition.lat === 'number')
              ) {
                // open(position) - 在指定位置打开
                let latLng: google.maps.LatLng | google.maps.LatLngLiteral;
                if (Array.isArray(mapOrPosition)) {
                  latLng = new google.maps.LatLng(mapOrPosition[1], mapOrPosition[0]);
                } else {
                  latLng = mapOrPosition;
                }
                infoWindow.setPosition(latLng);
                infoWindow.open({
                  map: mapInstance,
                });
              } else if (mapOrPosition && mapOrPosition.getPosition) {
                // open(marker) - 在标记位置打开
                infoWindow.open({
                  map: mapInstance,
                  anchor: mapOrPosition,
                });
              } else {
                // open(map) - 在指定地图打开
                infoWindow.open({
                  map: mapOrPosition,
                });
              }
            } else {
              // open(map, position/marker) - 在指定地图和位置/标记打开
              if (position && position.getPosition) {
                infoWindow.open({
                  map: mapOrPosition,
                  anchor: position,
                });
              } else {
                let latLng: google.maps.LatLng | google.maps.LatLngLiteral;
                if (Array.isArray(position)) {
                  latLng = new google.maps.LatLng(position[1], position[0]);
                } else {
                  latLng = position;
                }
                infoWindow.setPosition(latLng);
                infoWindow.open({
                  map: mapOrPosition,
                });
              }
            }
          } catch (error) {
            console.error('Failed to open info window:', error);
          }
        },

        close(): void {
          try {
            infoWindow.close();
          } catch (error) {
            console.error('Failed to close info window:', error);
          }
        },

        setContent(content: string | HTMLElement): void {
          try {
            infoWindow.setContent(content);
          } catch (error) {
            console.error('Failed to set info window content:', error);
          }
        },

        getContent(): string | HTMLElement | null {
          try {
            const content = infoWindow.getContent();
            if (typeof content === 'string' || content instanceof HTMLElement) {
              return content;
            }
            return null;
          } catch (error) {
            console.error('Failed to get info window content:', error);
            return null;
          }
        },

        setPosition(position: any): void {
          try {
            let latLng: google.maps.LatLng | google.maps.LatLngLiteral;
            if (Array.isArray(position)) {
              latLng = new google.maps.LatLng(position[1], position[0]);
            } else if (position && typeof position.lat === 'number') {
              latLng = position;
            } else {
              latLng = position;
            }
            infoWindow.setPosition(latLng);
          } catch (error) {
            console.error('Failed to set info window position:', error);
          }
        },

        getPosition(): any | null {
          try {
            return infoWindow.getPosition() || null;
          } catch (error) {
            console.error('Failed to get info window position:', error);
            return null;
          }
        },

        setOffset(offset: UnifiedOffset): void {
          try {
            let googleOffset: google.maps.Size;
            if (Array.isArray(offset)) {
              googleOffset = new google.maps.Size(offset[0], offset[1]);
            } else {
              googleOffset = new google.maps.Size(offset.x, offset.y);
            }
            // Google Maps InfoWindow 使用 pixelOffset 选项
            infoWindow.setOptions({
              pixelOffset: googleOffset,
            });
          } catch (error) {
            console.error('Failed to set info window offset:', error);
          }
        },

        getOffset(): UnifiedOffset | null {
          try {
            // Google Maps 没有直接获取 pixelOffset 的方法
            // 这里返回 null，实际使用中可能需要在创建时保存偏移量
            console.warn('Google Maps InfoWindow getOffset is not fully supported');
            return null;
          } catch (error) {
            console.error('Failed to get info window offset:', error);
            return null;
          }
        },

        setSize(size: UnifiedSize): void {
          try {
            // Google Maps InfoWindow 没有直接的 size 设置方法
            // 可以通过设置 maxWidth 来间接控制尺寸
            let width: number;
            if (Array.isArray(size)) {
              width = size[0];
            } else {
              width = size.width;
            }
            infoWindow.setOptions({
              maxWidth: width,
            });
            console.warn('Google Maps InfoWindow setSize only supports width (maxWidth)');
          } catch (error) {
            console.error('Failed to set info window size:', error);
          }
        },

        getSize(): UnifiedSize | null {
          try {
            // Google Maps InfoWindow 没有直接获取 size 的方法
            console.warn('Google Maps InfoWindow getSize is not supported');
            return null;
          } catch (error) {
            console.error('Failed to get info window size:', error);
            return null;
          }
        },

        addEventListener(event: string, handler: UnifiedInfoWindowEventHandler): void {
          try {
            // 直接使用原始的 handler，因为在 useMap.ts 中已经进行了事件转换
            infoWindow.addListener(event, handler);
          } catch (error) {
            console.error('Failed to add info window event listener:', error);
          }
        },

        removeEventListener(_event: string, _handler: UnifiedInfoWindowEventHandler): void {
          try {
            // Google Maps 没有直接的 removeListener 方法，需要保存监听器引用
            // 这里简化处理，实际使用中可能需要更复杂的管理
            console.warn('Google Maps InfoWindow removeEventListener is not fully supported');
          } catch (error) {
            console.error('Failed to remove info window event listener:', error);
          }
        },

        getRawInstance(): google.maps.InfoWindow | null {
          return infoWindow;
        },
      };

      return wrapper;
    } catch (error) {
      console.error('Failed to create info window:', error);
      return null;
    }
  }

  /**
   * 添加Google Maps控件
   * @param controlId 控件唯一标识
   * @param config 控件配置
   */
  function addControl(controlId: string, config: GMapControlConfig): Promise<any> {
    return new Promise((resolve, reject) => {
      const mapInstance = unref(map);
      if (!mapInstance) {
        reject(new Error('Map instance is not available'));
        return;
      }

      // 如果控件已存在，先移除
      if (controls.value.has(controlId)) {
        removeControl(controlId);
      }

      try {
        // Google Maps控件通过设置地图选项来启用
        switch (config.type) {
          case GMapControlType.ZOOM:
            mapInstance.setOptions({
              zoomControl: true,
              zoomControlOptions: {
                position: config.position || google.maps.ControlPosition.RIGHT_CENTER,
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          case GMapControlType.MAPTYPE:
            mapInstance.setOptions({
              mapTypeControl: true,
              mapTypeControlOptions: {
                position: config.position || google.maps.ControlPosition.TOP_RIGHT,
                style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          case GMapControlType.STREETVIEW:
            mapInstance.setOptions({
              streetViewControl: true,
              streetViewControlOptions: {
                position: config.position || google.maps.ControlPosition.RIGHT_BOTTOM,
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          case GMapControlType.FULLSCREEN:
            mapInstance.setOptions({
              fullscreenControl: true,
              fullscreenControlOptions: {
                position: config.position || google.maps.ControlPosition.TOP_RIGHT,
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          case GMapControlType.SCALE:
            mapInstance.setOptions({
              scaleControl: true,
              scaleControlOptions: {
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          case GMapControlType.ROTATE:
            mapInstance.setOptions({
              rotateControl: true,
              rotateControlOptions: {
                position: config.position || google.maps.ControlPosition.RIGHT_CENTER,
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          default:
            reject(new Error(`Unsupported control type: ${config.type}`));
            return;
        }

        console.log(`Google Maps control ${controlId} (${config.type}) added successfully`);
        resolve({ type: config.type, enabled: true });
      } catch (error) {
        console.error(`Failed to add Google Maps control ${controlId}:`, error);
        reject(error);
      }
    });
  }

  /**
   * 移除Google Maps控件
   * @param controlId 控件唯一标识
   */
  function removeControl(controlId: string): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    const control = controls.value.get(controlId);
    if (!control) {
      console.warn(`Control ${controlId} not found`);
      return false;
    }

    try {
      // Google Maps控件通过设置地图选项来禁用
      switch (control.type) {
        case GMapControlType.ZOOM:
          mapInstance.setOptions({ zoomControl: false });
          break;
        case GMapControlType.MAPTYPE:
          mapInstance.setOptions({ mapTypeControl: false });
          break;
        case GMapControlType.STREETVIEW:
          mapInstance.setOptions({ streetViewControl: false });
          break;
        case GMapControlType.FULLSCREEN:
          mapInstance.setOptions({ fullscreenControl: false });
          break;
        case GMapControlType.SCALE:
          mapInstance.setOptions({ scaleControl: false });
          break;
        case GMapControlType.ROTATE:
          mapInstance.setOptions({ rotateControl: false });
          break;
      }

      controls.value.delete(controlId);
      console.log(`Google Maps control ${controlId} removed successfully`);
      return true;
    } catch (error) {
      console.error(`Failed to remove Google Maps control ${controlId}:`, error);
      return false;
    }
  }

  /**
   * 添加预定义的Google Maps控件
   * @param controlName 预定义控件名称
   * @param customOptions 自定义选项（可选）
   */
  function addPredefinedControl(
    controlName: keyof typeof DEFAULT_GMAP_CONTROL_CONFIGS,
    customOptions?: Partial<GMapControlConfig>,
  ): Promise<any> {
    const defaultConfig = DEFAULT_GMAP_CONTROL_CONFIGS[controlName];
    if (!defaultConfig) {
      return Promise.reject(new Error(`Predefined Google Maps control ${controlName} not found`));
    }

    const config: GMapControlConfig = {
      ...defaultConfig,
      ...customOptions,
      options: {
        ...defaultConfig.options,
        ...customOptions?.options,
      },
    };

    return addControl(controlName, config);
  }

  /**
   * 批量添加Google Maps控件
   * @param controlConfigs 控件配置数组
   */
  async function addMultipleControls(
    controlConfigs: Array<{ id: string; config: GMapControlConfig }>,
  ): Promise<Array<{ id: string; control?: any; error?: any; success: boolean }>> {
    const results: Array<{ id: string; control?: any; error?: any; success: boolean }> = [];
    for (const { id, config } of controlConfigs) {
      try {
        const control = await addControl(id, config);
        results.push({ id, control, success: true });
      } catch (error) {
        console.error(`Failed to add Google Maps control ${id}:`, error);
        results.push({ id, error, success: false });
      }
    }
    return results;
  }

  /**
   * 清除所有Google Maps控件
   */
  function clearAllControls(): void {
    const controlIds = Array.from(controls.value.keys());
    controlIds.forEach((id) => removeControl(id));
  }

  /**
   * 获取已添加的Google Maps控件列表
   */
  function getControls(): Map<string, any> {
    return new Map(controls.value);
  }

  return {
    mapRef,
    map,
    setMapCenter,
    setZoom,
    addMarker,
    addPolyline,
    addPolygon,
    addCircle,
    removePolygon,
    removeCircle,
    removeAllPolygons,
    removeAllCircles,
    removeAllPolygonsAndCircles,
    getAllPolygons,
    getAllCircles,
    districtSearch,
    startDrawPolygon,
    startDrawCircle,
    stopDrawing,
    clearDrawingOverlays,
    getDrawingOverlays,
    getDrawingData,
    initializeDrawingTool,
    setCity,
    getMapInstance,
    clearMap,
    getMapBoundary,
    geocoder,
    destroy,
    getAddress,
    createInfoWindow,
    init,
    setMapCenterFitView,
    setFitView,
    isInitialized,
    validateCoordinates,
    validatePosition,
    // 事件管理方法
    addEventListener,
    removeEventListener,
    triggerEvent,
    // Google Maps控件管理方法
    addControl,
    removeControl,
    addPredefinedControl,
    addMultipleControls,
    clearAllControls,
    getControls,
  };
}

/**
 * @description 获取Google地图geocoder
 */
export function useGGeoCoder() {
  const geocoder = ref<google.maps.Geocoder | undefined>();
  const isInitialized = ref(false);

  /**
   * 初始化地理编码器
   */
  function initGeocoder(): boolean {
    console.log(`init geocoder`);
    if (isInitialized.value) {
      return true;
    }

    try {
      if (!window.google || !window.google.maps) {
        console.error('Google Maps API is not loaded');
        return false;
      }

      geocoder.value = new google.maps.Geocoder();
      isInitialized.value = true;

      console.log(`google geocoder initialized successfully`, geocoder.value);
      return true;
    } catch (error) {
      console.error('Failed to initialize geocoder:', error);
      isInitialized.value = false;
      return false;
    }
  }

  onMounted(() => {
    initGeocoder();
  });

  onUnmounted(() => {
    geocoder.value = undefined;
    isInitialized.value = false;
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: [number, number]): Promise<string> {
    if (!validateCoordinates(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise<string>((resolve, reject) => {
      try {
        const latLng = new google.maps.LatLng(lngLat[1], lngLat[0]); // lat, lng顺序

        geocoderInstance.geocode(
          { location: latLng },
          (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {
            if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
              resolve(results[0].formatted_address || '');
            } else if (status === google.maps.GeocoderStatus.ZERO_RESULTS) {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  return {
    geocoder,
    getAddress,
    isInitialized,
    initGeocoder,
    validateCoordinates,
  };
}

export type SetMapCenter = (lngLat: [number, number]) => boolean;
