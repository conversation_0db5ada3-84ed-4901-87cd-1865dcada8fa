import { ref, unref, onMounted, onUnmounted } from 'vue';
import { type MapBoundary } from './useAMap';

export type GMapPoint = [number, number] | google.maps.LatLng;

// 地理编码结果接口
export interface GeocodeResult {
  status: google.maps.GeocoderStatus;
  results?: google.maps.GeocoderResult[];
}

// Google Maps控件类型枚举
export enum GMapControlType {
  ZOOM = 'ZoomControl',
  MAPTYPE = 'MapTypeControl',
  STREETVIEW = 'StreetViewControl',
  FULLSCREEN = 'FullscreenControl',
  SCALE = 'ScaleControl',
  ROTATE = 'RotateControl',
}

// Google Maps控件配置接口
export interface GMapControlConfig {
  type: GMapControlType;
  position?: google.maps.ControlPosition;
  style?: any;
  options?: any;
}

// 预定义的Google Maps控件配置
export const DEFAULT_GMAP_CONTROL_CONFIGS: Record<string, GMapControlConfig> = {
  zoom: {
    type: GMapControlType.ZOOM,
    position: google.maps.ControlPosition.RIGHT_CENTER,
  },
  maptype: {
    type: GMapControlType.MAPTYPE,
    position: google.maps.ControlPosition.TOP_RIGHT,
    options: {
      style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
    },
  },
  streetview: {
    type: GMapControlType.STREETVIEW,
    position: google.maps.ControlPosition.RIGHT_BOTTOM,
  },
  fullscreen: {
    type: GMapControlType.FULLSCREEN,
    position: google.maps.ControlPosition.TOP_RIGHT,
  },
  scale: {
    type: GMapControlType.SCALE,
  },
  rotate: {
    type: GMapControlType.ROTATE,
    position: google.maps.ControlPosition.RIGHT_CENTER,
  },
};

/**
 * @description Google地图hook，便于管理地图加载、创建和销毁。
 * @param {google.maps.MapOptions} options Google地图实例化参数
 * @param {Function} callback 地图初始化完成后的回调函数
 */
export function useGMap(
  options: google.maps.MapOptions,
  callback?: (map: google.maps.Map) => void,
) {
  const map = ref<google.maps.Map | undefined>();
  const mapRef = ref<HTMLDivElement | undefined>();
  const geocoder = ref<google.maps.Geocoder | undefined>();
  const isInitialized = ref(false);
  const controls = ref<Map<string, any>>(new Map());

  /**
   * 初始化地图实例的核心逻辑
   */
  function initializeMap(): boolean {
    const container = unref(mapRef);
    if (!container) {
      console.error('Map container ref is not available');
      return false;
    }

    if (isInitialized.value) {
      console.warn('Map is already initialized');
      return true;
    }

    try {
      // 检查Google Maps API是否已加载
      if (!window.google || !window.google.maps) {
        console.error('Google Maps API is not loaded');
        return false;
      }

      map.value = new google.maps.Map(container, options);

      // 初始化地理编码器
      geocoder.value = new google.maps.Geocoder();

      isInitialized.value = true;

      if (typeof callback === 'function') {
        callback(map.value);
      }

      return true;
    } catch (error) {
      console.error('Failed to initialize Google Map:', error);
      isInitialized.value = false;
      return false;
    }
  }

  // dom挂载完成后初始化map实例
  onMounted(() => {
    if (unref(mapRef)) {
      initializeMap();
    }
  });

  /**
   * 手动初始化地图
   */
  function init(): boolean {
    return initializeMap();
  }

  /**
   * 销毁地图实例和相关资源
   */
  function destroy(): void {
    try {
      // Google Maps 没有destroy方法，只需要清空引用
      map.value = undefined;
      geocoder.value = undefined;
      isInitialized.value = false;
    } catch (error) {
      console.error('Failed to destroy Google Map:', error);
    }
  }

  // 组件卸载时销毁地图
  onUnmounted(() => {
    destroy();
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * 验证位置参数是否有效
   */
  function validatePosition(position: GMapPoint): boolean {
    if (Array.isArray(position)) {
      return validateCoordinates(position);
    }
    // 如果是google.maps.LatLng对象，检查其属性
    if (position && typeof position === 'object' && 'lng' in position && 'lat' in position) {
      return validateCoordinates([position.lng(), position.lat()]);
    }
    return false;
  }

  /**
   * @description 设置中心点
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenter(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      const latLng = new google.maps.LatLng(lngLat[1], lngLat[0]); // 注意：Google Maps是lat, lng顺序
      mapInstance.setCenter(latLng);
      return true;
    } catch (error) {
      console.error('Failed to set map center:', error);
      return false;
    }
  }

  /**
   * @description 设置中心点并自适应视野
   * @param lngLat [116.397083, 39.874531]
   */
  function setMapCenterFitView(lngLat: [number, number]): boolean {
    if (!validateCoordinates(lngLat)) {
      console.error('Invalid coordinates provided:', lngLat);
      return false;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      const latLng = new google.maps.LatLng(lngLat[1], lngLat[0]);
      mapInstance.setCenter(latLng);
      // Google Maps 没有直接的fitView方法，这里设置一个合适的缩放级别
      mapInstance.setZoom(15);
      return true;
    } catch (error) {
      console.error('Failed to set map center and fit view:', error);
      return false;
    }
  }

  /**
   * 添加标记点
   * @param position 位置坐标
   * @param options 标记选项
   */
  function addMarker(
    position: GMapPoint,
    options: google.maps.MarkerOptions = {},
  ): google.maps.Marker | null {
    if (!validatePosition(position)) {
      console.error('Invalid position provided:', position);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      let latLng: google.maps.LatLng;
      if (Array.isArray(position)) {
        latLng = new google.maps.LatLng(position[1], position[0]); // lat, lng顺序
      } else {
        latLng = position;
      }

      const marker = new google.maps.Marker({
        position: latLng,
        map: mapInstance,
        ...options,
      });

      return marker;
    } catch (error) {
      console.error('Failed to add marker:', error);
      return null;
    }
  }

  /**
   * 添加折线
   * @param points 路径点数组
   * @param options 折线选项
   */
  function addPolyline(
    points: GMapPoint[],
    options: Partial<google.maps.PolylineOptions> = {},
  ): google.maps.Polyline | null {
    if (!Array.isArray(points) || points.length < 2) {
      console.error('Invalid points array provided, at least 2 points required');
      return null;
    }

    // 验证所有点的有效性
    const invalidPoints = points.filter((point) => !validatePosition(point));
    if (invalidPoints.length > 0) {
      console.error('Invalid points found:', invalidPoints);
      return null;
    }

    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const path = points.map((point) => {
        if (Array.isArray(point)) {
          return new google.maps.LatLng(point[1], point[0]); // lat, lng顺序
        }
        return point;
      });

      const polyline = new google.maps.Polyline({
        path,
        map: mapInstance,
        strokeColor: '#4d78bc',
        strokeWeight: 10,
        ...options,
      });

      return polyline;
    } catch (error) {
      console.error('Failed to add polyline:', error);
      return null;
    }
  }

  /**
   * 获取地图实例
   */
  function getMapInstance(): google.maps.Map | undefined {
    return unref(map);
  }

  /**
   * 清空地图上的所有覆盖物
   */
  function clearMap(): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    try {
      // Google Maps 没有直接的clearMap方法，需要手动清除所有覆盖物
      // 这里只是一个基础实现，实际使用中可能需要维护覆盖物的引用
      console.warn('Google Maps clearMap: Please manually remove overlays');
      return true;
    } catch (error) {
      console.error('Failed to clear map:', error);
      return false;
    }
  }

  /**
   * 获取地图边界信息
   */
  function getMapBoundary(): MapBoundary | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const bounds = mapInstance.getBounds();
      if (!bounds) {
        console.error('Invalid bounds returned from map');
        return null;
      }

      const ne = bounds.getNorthEast();
      const sw = bounds.getSouthWest();

      return {
        maxLat: ne.lat(),
        maxLng: ne.lng(),
        minLat: sw.lat(),
        minLng: sw.lng(),
      };
    } catch (error) {
      console.error('Failed to get map boundary:', error);
      return null;
    }
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: GMapPoint): Promise<string> {
    if (!validatePosition(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise((resolve, reject) => {
      try {
        let latLng: google.maps.LatLng;
        if (Array.isArray(lngLat)) {
          latLng = new google.maps.LatLng(lngLat[1], lngLat[0]); // lat, lng顺序
        } else {
          latLng = lngLat;
        }

        geocoderInstance.geocode(
          { location: latLng },
          (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {
            if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
              resolve(results[0].formatted_address || '');
            } else if (status === google.maps.GeocoderStatus.ZERO_RESULTS) {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 为 marker 添加事件监听器
   * @param marker 标记对象
   * @param eventType 事件类型
   * @param handler 事件处理函数
   */
  function addMarkerEvent(
    marker: google.maps.Marker,
    eventType:
      | 'click'
      | 'dblclick'
      | 'rightclick'
      | 'mouseover'
      | 'mouseout'
      | 'mousedown'
      | 'mouseup'
      | 'drag'
      | 'dragstart'
      | 'dragend',
    handler: (event: any) => void,
  ): void {
    if (!marker) {
      console.error('Marker is not available');
      return;
    }

    try {
      marker.addListener(eventType, handler);
    } catch (error) {
      console.error('Failed to add marker event:', error);
    }
  }

  /**
   * 移除 marker 的事件监听器
   * @param marker 标记对象
   * @param eventType 事件类型
   * @param handler 事件处理函数（可选，不传则移除该事件类型的所有监听器）
   */
  function removeMarkerEvent(
    marker: google.maps.Marker,
    eventType:
      | 'click'
      | 'dblclick'
      | 'rightclick'
      | 'mouseover'
      | 'mouseout'
      | 'mousedown'
      | 'mouseup'
      | 'drag'
      | 'dragstart'
      | 'dragend',
    handler?: (event: any) => void,
  ): void {
    if (!marker) {
      console.error('Marker is not available');
      return;
    }

    try {
      // Google Maps 没有直接的 off 方法，需要使用 clearListeners
      if (handler) {
        // 对于特定的处理函数，Google Maps API 没有直接的移除方法
        // 这里我们清除所有该类型的监听器
        google.maps.event.clearListeners(marker, eventType);
      } else {
        // 移除所有该类型的事件监听器
        google.maps.event.clearListeners(marker, eventType);
      }
    } catch (error) {
      console.error('Failed to remove marker event:', error);
    }
  }

  /**
   * 创建信息窗口
   * @param options 信息窗口选项
   */
  function createInfoWindow(
    options: google.maps.InfoWindowOptions = {},
  ): google.maps.InfoWindow | null {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return null;
    }

    try {
      const infoWindow = new google.maps.InfoWindow({
        ...options,
      });
      return infoWindow;
    } catch (error) {
      console.error('Failed to create info window:', error);
      return null;
    }
  }

  /**
   * 添加Google Maps控件
   * @param controlId 控件唯一标识
   * @param config 控件配置
   */
  function addControl(controlId: string, config: GMapControlConfig): Promise<any> {
    return new Promise((resolve, reject) => {
      const mapInstance = unref(map);
      if (!mapInstance) {
        reject(new Error('Map instance is not available'));
        return;
      }

      // 如果控件已存在，先移除
      if (controls.value.has(controlId)) {
        removeControl(controlId);
      }

      try {
        // Google Maps控件通过设置地图选项来启用
        switch (config.type) {
          case GMapControlType.ZOOM:
            mapInstance.setOptions({
              zoomControl: true,
              zoomControlOptions: {
                position: config.position || google.maps.ControlPosition.RIGHT_CENTER,
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          case GMapControlType.MAPTYPE:
            mapInstance.setOptions({
              mapTypeControl: true,
              mapTypeControlOptions: {
                position: config.position || google.maps.ControlPosition.TOP_RIGHT,
                style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          case GMapControlType.STREETVIEW:
            mapInstance.setOptions({
              streetViewControl: true,
              streetViewControlOptions: {
                position: config.position || google.maps.ControlPosition.RIGHT_BOTTOM,
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          case GMapControlType.FULLSCREEN:
            mapInstance.setOptions({
              fullscreenControl: true,
              fullscreenControlOptions: {
                position: config.position || google.maps.ControlPosition.TOP_RIGHT,
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          case GMapControlType.SCALE:
            mapInstance.setOptions({
              scaleControl: true,
              scaleControlOptions: {
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          case GMapControlType.ROTATE:
            mapInstance.setOptions({
              rotateControl: true,
              rotateControlOptions: {
                position: config.position || google.maps.ControlPosition.RIGHT_CENTER,
                ...config.options,
              },
            });
            controls.value.set(controlId, { type: config.type, enabled: true });
            break;

          default:
            reject(new Error(`Unsupported control type: ${config.type}`));
            return;
        }

        console.log(`Google Maps control ${controlId} (${config.type}) added successfully`);
        resolve({ type: config.type, enabled: true });
      } catch (error) {
        console.error(`Failed to add Google Maps control ${controlId}:`, error);
        reject(error);
      }
    });
  }

  /**
   * 移除Google Maps控件
   * @param controlId 控件唯一标识
   */
  function removeControl(controlId: string): boolean {
    const mapInstance = unref(map);
    if (!mapInstance) {
      console.error('Map instance is not available');
      return false;
    }

    const control = controls.value.get(controlId);
    if (!control) {
      console.warn(`Control ${controlId} not found`);
      return false;
    }

    try {
      // Google Maps控件通过设置地图选项来禁用
      switch (control.type) {
        case GMapControlType.ZOOM:
          mapInstance.setOptions({ zoomControl: false });
          break;
        case GMapControlType.MAPTYPE:
          mapInstance.setOptions({ mapTypeControl: false });
          break;
        case GMapControlType.STREETVIEW:
          mapInstance.setOptions({ streetViewControl: false });
          break;
        case GMapControlType.FULLSCREEN:
          mapInstance.setOptions({ fullscreenControl: false });
          break;
        case GMapControlType.SCALE:
          mapInstance.setOptions({ scaleControl: false });
          break;
        case GMapControlType.ROTATE:
          mapInstance.setOptions({ rotateControl: false });
          break;
      }

      controls.value.delete(controlId);
      console.log(`Google Maps control ${controlId} removed successfully`);
      return true;
    } catch (error) {
      console.error(`Failed to remove Google Maps control ${controlId}:`, error);
      return false;
    }
  }

  /**
   * 添加预定义的Google Maps控件
   * @param controlName 预定义控件名称
   * @param customOptions 自定义选项（可选）
   */
  function addPredefinedControl(
    controlName: keyof typeof DEFAULT_GMAP_CONTROL_CONFIGS,
    customOptions?: Partial<GMapControlConfig>,
  ): Promise<any> {
    const defaultConfig = DEFAULT_GMAP_CONTROL_CONFIGS[controlName];
    if (!defaultConfig) {
      return Promise.reject(new Error(`Predefined Google Maps control ${controlName} not found`));
    }

    const config: GMapControlConfig = {
      ...defaultConfig,
      ...customOptions,
      options: {
        ...defaultConfig.options,
        ...customOptions?.options,
      },
    };

    return addControl(controlName, config);
  }

  /**
   * 批量添加Google Maps控件
   * @param controlConfigs 控件配置数组
   */
  async function addMultipleControls(
    controlConfigs: Array<{ id: string; config: GMapControlConfig }>,
  ): Promise<Array<{ id: string; control?: any; error?: any; success: boolean }>> {
    const results: Array<{ id: string; control?: any; error?: any; success: boolean }> = [];
    for (const { id, config } of controlConfigs) {
      try {
        const control = await addControl(id, config);
        results.push({ id, control, success: true });
      } catch (error) {
        console.error(`Failed to add Google Maps control ${id}:`, error);
        results.push({ id, error, success: false });
      }
    }
    return results;
  }

  /**
   * 清除所有Google Maps控件
   */
  function clearAllControls(): void {
    const controlIds = Array.from(controls.value.keys());
    controlIds.forEach((id) => removeControl(id));
  }

  /**
   * 获取已添加的Google Maps控件列表
   */
  function getControls(): Map<string, any> {
    return new Map(controls.value);
  }

  return {
    mapRef,
    map,
    setMapCenter,
    addMarker,
    addPolyline,
    getMapInstance,
    clearMap,
    getMapBoundary,
    geocoder,
    destroy,
    getAddress,
    createInfoWindow,
    init,
    setMapCenterFitView,
    isInitialized,
    validateCoordinates,
    validatePosition,
    // Marker 事件管理方法
    addMarkerEvent,
    removeMarkerEvent,
    // Google Maps控件管理方法
    addControl,
    removeControl,
    addPredefinedControl,
    addMultipleControls,
    clearAllControls,
    getControls,
  };
}

/**
 * @description 获取Google地图geocoder
 */
export function useGGeoCoder() {
  const geocoder = ref<google.maps.Geocoder | undefined>();
  const isInitialized = ref(false);

  /**
   * 初始化地理编码器
   */
  function initGeocoder(): boolean {
    if (isInitialized.value) {
      return true;
    }

    try {
      if (!window.google || !window.google.maps) {
        console.error('Google Maps API is not loaded');
        return false;
      }

      geocoder.value = new google.maps.Geocoder();
      isInitialized.value = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize geocoder:', error);
      isInitialized.value = false;
      return false;
    }
  }

  onMounted(() => {
    initGeocoder();
  });

  onUnmounted(() => {
    geocoder.value = undefined;
    isInitialized.value = false;
  });

  /**
   * 验证经纬度坐标是否有效
   */
  function validateCoordinates(lngLat: [number, number]): boolean {
    if (!Array.isArray(lngLat) || lngLat.length !== 2) {
      return false;
    }
    const [lng, lat] = lngLat;
    return (
      typeof lng === 'number' &&
      typeof lat === 'number' &&
      lng >= -180 &&
      lng <= 180 &&
      lat >= -90 &&
      lat <= 90 &&
      !isNaN(lng) &&
      !isNaN(lat)
    );
  }

  /**
   * 根据经纬度逆地址解析
   * @param lngLat 经纬度-[116.397083, 39.874531]
   */
  function getAddress(lngLat: [number, number]): Promise<string> {
    if (!validateCoordinates(lngLat)) {
      return Promise.reject(new Error('Invalid coordinates provided'));
    }

    const geocoderInstance = unref(geocoder);
    if (!geocoderInstance) {
      return Promise.reject(new Error('Geocoder is not available'));
    }

    return new Promise<string>((resolve, reject) => {
      try {
        const latLng = new google.maps.LatLng(lngLat[1], lngLat[0]); // lat, lng顺序

        geocoderInstance.geocode(
          { location: latLng },
          (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {
            if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
              resolve(results[0].formatted_address || '');
            } else if (status === google.maps.GeocoderStatus.ZERO_RESULTS) {
              resolve('');
            } else {
              reject(new Error(`Geocoding failed with status: ${status}`));
            }
          },
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  return {
    geocoder,
    getAddress,
    isInitialized,
    initGeocoder,
    validateCoordinates,
  };
}

export type SetMapCenter = (lngLat: [number, number]) => boolean;
