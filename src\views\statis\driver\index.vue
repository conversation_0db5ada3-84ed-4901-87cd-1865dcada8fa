<script lang="tsx" setup>
  import { BasicTable, useTable, BasicColumn, FormSchema } from '@/components/Table';
  import { getDriverStatistic, getDriverDaily } from '@/api/data/statis';
  import { useModal } from '@/components/Modal';
  import { ref, onMounted, toRefs } from 'vue';
  import { ECharts } from '@/components/ECharts';
  import driverOptions from '../chart/bar-driver.option';
  import dayjs from 'dayjs';
  import { getOrgTreeOptions } from '@/api/passport/org';
  import { AlertOutlined } from '@ant-design/icons-vue';
  import AlertListForm from './alertListForm.vue';

  defineOptions({
    name: 'report:driver-static',
  });

  // const { getAddress } = useGeoCoder();
  const columns: BasicColumn[] = [
    {
      title: '姓名',
      dataIndex: 'name',
    },
    {
      title: '司机编码',
      dataIndex: 'driverNo',
    },
    {
      title: '联系方式',
      dataIndex: 'mobile',
    },
    {
      title: '关联报警',
      dataIndex: 'alertCount',
    },
    {
      title: '工作时长（分钟）',
      dataIndex: 'workHours',
    },
    {
      title: '总里程',
      dataIndex: 'workMileage',
    },
  ];
  const dateType = ref('month');
  const searchFormSchema: FormSchema[] = [
    {
      field: 'criteria.dateType',
      component: 'RadioButtonGroup',
      label: '',
      colProps: {
        span: 4,
      },
      defaultValue: 'month',
      componentProps: {
        options: [
          {
            label: '按月统计',
            value: 'month',
          },
          {
            label: '按日统计',
            value: 'day',
          },
        ],
        onChange: (e, v) => {
          console.log('RadioButtonGroup====>:', e, v);
          dateType.value = e;
        },
      },
    },
    {
      field: 'criteria.range',
      label: '日期范围',
      component: 'RangePicker',
      colProps: { span: 7 },
      defaultValue: [
        dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      componentProps: {
        style: 'width: 100%;',
        valueFormat: 'YYYY-MM-DD',
      },
      ifShow: () => dateType.value == 'day',
    },
    {
      field: 'criteria.range',
      label: '日期范围',
      component: 'RangePicker',
      colProps: { span: 7 },
      defaultValue: [
        dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      componentProps: {
        style: 'width: 100%;',
        format: 'YYYY-MM',
        valueFormat: 'YYYY-MM-DD',
      },
      ifShow: () => dateType.value == 'month',
    },
    {
      field: 'criteria.orgBranch',
      label: '所属机构',
      component: 'ApiTreeSelect',
      colProps: { span: 6 },
      componentProps: {
        api: getOrgTreeOptions,
        labelField: 'name',
        valueField: 'uid',
      },
    },
  ];

  const [registerTable] = useTable({
    api: getDriverStatistic,
    columns,
    maxHeight: 500,
    canResize: false,
    formConfig: {
      labelWidth: 90,
      schemas: searchFormSchema,
    },
    useSearchForm: true,
    showTableSetting: true,
    showIndexColumn: false,
    beforeFetch: async (searchInfo: any) => {
      if (
        searchInfo.criteria.dateType == 'month' &&
        searchInfo.criteria.range &&
        searchInfo.criteria.range.length
      ) {
        searchInfo.criteria.range = [
          dayjs(searchInfo.criteria.range[0]).startOf('month').format('YYYY-MM-DD 00:00:00'),
          dayjs(searchInfo.criteria.range[1]).endOf('month').format('YYYY-MM-DD 23:59:59'),
        ];
      }
      let param = {
        ...searchInfo.criteria,
        begin: dayjs(searchInfo.criteria.range[0]).format('YYYY-MM-DD 00:00:00'),
        end: dayjs(searchInfo.criteria.range[1]).format('YYYY-MM-DD 23:59:59'),
      };
      initChart(searchInfo.criteria.dateType, param);
      if (searchInfo.criteria.range && searchInfo.criteria.range.length) {
        searchInfo.criteria.begin = dayjs(searchInfo.criteria.range[0]).format(
          'YYYY-MM-DD 00:00:00',
        );
        searchInfo.criteria.end = dayjs(searchInfo.criteria.range[1]).format('YYYY-MM-DD 00:00:00');
      } else {
        searchInfo.criteria.begin = dayjs().subtract(30, 'day').format('YYYY-MM-DD 00:00:00');
        searchInfo.criteria.end = dayjs().format('YYYY-MM-DD 23:59:59');
      }
    },
  });

  const barChartOptions = ref<any>(driverOptions);
  async function initChart(
    dateType: number,
    param: {
      orgBranch: any;
      categoryBranch: any;
      saleStatus: any;
      begin: any;
      end: any;
    },
  ) {
    const color = ['#00ffff', '#00cfff', '#006ced', '#ffe000', '#ffa800', '#ff5b00', '#ff3000'];
    let Data: any[] = [];
    // if (dateType == 1) {
    //   Data = await getMonthlyWorktime(param);
    // } else {
    //   Data = await getDailyWorktime(param);
    // }
    Data = await getDriverDaily(param);
    const xAxis = Data.map((item) => {
      return item.workDate;
    });
    const hours = Data.map((item) => {
      return item.workHours;
    });
    const alertCount = Data.map((item) => {
      return item.alertCount;
    });
    const mileage = Data.map((item) => {
      return item.workMileage;
    });
    barChartOptions.value.xAxis.data = xAxis;
    barChartOptions.value.color = color;
    barChartOptions.value.series[0].data = hours;
    barChartOptions.value.series[1].data = alertCount;
    barChartOptions.value.series[2].data = mileage;
  }

  //查看报警信息
  const [registerAlertFormModal, { openModal: openAlertFormModal }] = toRefs(useModal());
  function lookAlerts(record) {
    openAlertFormModal(true, {
      record,
    });
  }
  //组件挂载时
  onMounted(() => {
    // let param = {
    //   orgBranch: null,
    //   categoryBranch: null,
    //   saleStatus: null,
    //   range: {
    //     begin: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    //     end: dayjs().format('YYYY-MM-DD'),
    //   }
    // }
    // initChart(2, param);
  });
</script>
<template>
  <div>
    <BasicTable class="flex-1" @register="registerTable">
      <template #aboveOfTable>
        <div class="chartbox">
          <div style="width: 100%; height: 300px">
            <ECharts :options="barChartOptions" />
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'alertCount'">
          <div
            class="icon_box alert"
            :class="{ active: (record.alertCount || 0) > 0 }"
            @click="lookAlerts(record)"
          >
            <AlertOutlined />
            <span> {{ record.alertCount || 0 }} </span>
          </div>
        </template>
      </template>
    </BasicTable>
    <AlertListForm @register="registerAlertFormModal" />
  </div>
</template>
<style scoped>
  .chartbox {
    padding-bottom: 20px;
  }

  :deep(:where(.css-dev-only-do-not-override-ez24qu).ant-picker .ant-picker-suffix) {
    color: #fff;
  }

  .icon_box {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    cursor: pointer;
  }

  .icon_box.active {
    color: #0fcefd;
  }

  .alert.active {
    color: red;
  }
</style>
