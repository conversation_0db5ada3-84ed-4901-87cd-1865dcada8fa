<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增设备 </a-button>
        <Upload
          v-model:file-list="fileList"
          :showUploadList="false"
          :headers="headers"
          name="file"
          :beforeUpload="beforeUpload"
          @change="handleChange"
          action="/basic-api/device/import"
        >
          <a-button type="primary"> 批量导入 </a-button>
        </Upload>
        <a-button type="primary" @click="downloadFile"> 下载模板 </a-button>
      </template>
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              auth: 'device:edit',
              icon: 'clarity:note-edit-line',
              onClick: handleEdit.bind(null, record),
            },
            {
              auth: 'device:remove',
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '是否确认删除',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
          :dropDownActions="getDropDownAction(record)"
        />
      </template>
    </BasicTable>
    <AddFormModal @register="registerFormModal" @success="handleSuccess" />
    <DeviceDetail @register="registerDetailModal" @success="handleSuccess" />
    <SaleFormModal @register="registerSaleModal" @success="handleSuccess" />
    <UploadErrorForm @register="registerErrorModal" />
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { Tag, Upload } from 'ant-design-vue';

  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getDeviceList, removeDevice, importValidate } from '@/api/vehicle/vehlist';
  import { useMessage } from '@/hooks/web/useMessage';

  import { useModal } from '@/components/Modal';
  import AddFormModal from './AddFormModal.vue';
  import DeviceDetail from './deviceDetail.vue';
  import SaleFormModal from './saleFormModal.vue';
  import UploadErrorForm from './uploadErrorForm.vue';
  import { useDrawer } from '@/components/Drawer';
  import axios from 'axios';
  import { useUserStoreWithOut } from '@/store/modules/user';
  import { columns, searchFormSchema } from './vehicle.data';
  import { useRoute, useRouter } from 'vue-router';
  const { createMessage } = useMessage();
  const userStore = useUserStoreWithOut();
  export default defineComponent({
    name: 'VehicleList',
    components: {
      BasicTable,
      TableAction,
      Tag,
      AddFormModal,
      DeviceDetail,
      SaleFormModal,
      Upload,
      UploadErrorForm,
    },
    setup() {
      const router = useRouter();
      const [registerTable, { reload }] = useTable({
        title: '设备列表',
        api: getDeviceList,
        columns,
        formConfig: {
          labelAlign: 'left',
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        actionColumn: {
          width: 150,
          title: '操作',
          dataIndex: 'action',
          slot: 'action',
          fixed: 'right',
        },
      });

      const [registerFormModal, { openModal: openFormModal }] = useModal();

      const [registerDrawer] = useDrawer();

      function handleCreate() {
        openFormModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        console.log(record);
        openFormModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        removeDevice(record?.id).then((res) => {
          createMessage.success('删除成功');
          reload();
        });
      }
      //查看设备信息
      const [registerDetailModal, { openModal: openDetailModal }] = useModal();
      const queryDetail = (record) => {
        openDetailModal(true, {
          record,
        });
      };
      //销售设备
      const [registerSaleModal, { openModal: openSaleModal }] = useModal();
      const saleDevice = (record) => {
        openSaleModal(true, {
          record,
        });
      };
      function getDropDownAction(record) {
        return [
          {
            label: '销售',
            auth: 'device:sale',
            onClick: saleDevice.bind(null, record),
          },
          {
            label: '设备详情',
            onClick: queryDetail.bind(null, record),
          },
          {
            label: '轨迹回放',
            onClick: lookHistory.bind(null, record),
          },
          {
            label: '发送指令',
          },
          {
            label: '查看围栏',
            onClick: lookFence.bind(null, record),
          },
        ];
      }
      function handleSuccess() {
        reload();
      }
      //轨迹回放
      function lookHistory(record:Recordable){
        router.push({
          path: '/location/track',
          query: { code: record.id },
        });
      }
      //查看围栏
      function lookFence(record:Recordable){
        router.push({
          path: '/device/fence',
          query: { code: record.id },
        });
      }
      //下载模板
      function downloadFile() {
        axios
          .get(`/public/file/批量导入设备信息.xlsx`, { responseType: 'blob' })
          .then((response) => {
            const blobUrl = URL.createObjectURL(response.data);
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = '批量导入设备信息.xlsx';
            link.click();
            URL.revokeObjectURL(blobUrl); // 释放内存
        });
      }
      const fileList = ref([]);
      const [registerErrorModal, { openModal: openErrorModal }] = useModal();
      const beforeUpload = async (file, fileList) => {
        const formData = new FormData();
        formData.append('file', file);
        const res = await importValidate(formData);
        if (res.length) {
          createMessage.error('格式错误');
          openErrorModal(true, {
            record: res,
          });
          return false;
        }
      };
      const handleChange = (info: UploadChangeParam) => {
        if (info.file.status !== 'uploading') {
          console.log(info.file, info.fileList);
        }
        if (info.file.status === 'done') {
          createMessage.success(`${info.file.name} 文件上传成功`);
          reload();
        } else if (info.file.status === 'error') {
          createMessage.error(`${info.file.name} 文件上传失败`);
        }
      };
      const headers = ref({
        authorization: `Bearer ${userStore.getToken}`,
        'x-application-code': localStorage.getItem('application'),
        'x-authority': 'NONE',
      })
      return {
        headers,
        fileList,
        beforeUpload,
        downloadFile,
        registerTable,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        registerFormModal,
        registerDrawer,
        getDropDownAction,
        registerDetailModal,
        registerSaleModal,
        registerErrorModal,
        handleChange,
      };
    },
  });
</script>
