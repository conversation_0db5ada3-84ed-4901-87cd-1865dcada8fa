<script lang="ts" setup>
  import { ref, watch, onMounted, toRefs } from 'vue';
  import ECharts from '@/components/ECharts/src/ECharts.vue';
  import { getDeviceOverview } from '@/api/statis/deviceovw';
  import defaultOptions from '@/views/statis/device/pie.options';

  const props = defineProps({
    criteria: Object,
  });

  const { criteria }: any = toRefs(props);
  let options1 = ref({
    ...defaultOptions,
    title: { text: '库存统计', top: 'middle' },
    series: [
      {
        name: '销售状态',
        type: 'pie',
        data: [],
      },
    ],
  });
  let options2 = ref({
    ...defaultOptions,
    title: { text: '在离线统计', top: 'middle' },
    series: [
      {
        name: '在线状态',
        type: 'pie',
        data: [],
      },
    ],
  });
  let options3 = ref({
    ...defaultOptions,
    title: { text: '激活统计', top: 'middle' },
    series: [
      {
        name: '激活状态',
        type: 'pie',
        data: [],
      },
    ],
  });

  watch(
    criteria,
    (newValue, oldValue) => {
      const params = {
        orgId: newValue.orgId,
      };
      handleSearch(params);
    },
    {
      deep: true,
    },
  );

  async function handleSearch(params) {
    const { activation, onlineStatus, stock } = await getDeviceOverview(params);

    options1.value.series[0].data = [
      { value: stock.unSales || 0, name: '库存' },
      { value: stock.sales || 0, name: '已销售' },
    ];
    options2.value.series[0].data = [
      { value: onlineStatus.offline || 0, name: '离线' },
      { value: onlineStatus.online || 0, name: '在线' },
    ];
    options3.value.series[0].data = [
      { value: activation.unActivate || 0, name: '未激活' },
      { value: activation.activate || 0, name: '已激活' },
    ];
  }

  onMounted(() => {
    handleSearch({});
  });
</script>

<template>
  <div class="w-full h-80 flex boxleft">
    <ECharts :options="options1" />
  </div>
  <div class="w-full h-80 flex boxleft">
    <ECharts :options="options2" />
  </div>
  <div class="w-full h-80 flex boxleft">
    <ECharts :options="options3" />
  </div>
</template>

<style scoped>
  .boxleft {
    width: calc(33% - 33px);
    margin: 0 0 25px 30px;
    padding: 35px 30px 0;
    float: left;
    border-radius: 5px;
    box-shadow: 0 0 10px #ddd;
  }
</style>
