<template>
  <div>
    <BasicTable @register="registerTable">
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>
      <TableAction
        :actions="[
          // {
          //   icon: 'flowbite:link-break-outline',
          //   color: 'error',
          //   popConfirm: {
          //     title: '是否确认解绑该设备',
          //     confirm: handleDelete.bind(null, record),
          //   },
          // },
        ]"
      />
    </BasicTable>

    <AlarmHistory @register="registerModal" />
  </div>
</template>
<script lang="tsx" setup>
  import { onMounted, ref } from 'vue';
  import { Tag } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getHistoryList } from '@/api/statis/metrics';
  import { useModal } from '@/components/Modal';
  import { getOrgTreeOptions } from '@/api/passport/org';
  import { getvehicleList } from '@/api/vehicle/vehlist';
  import { getModelTree } from '@/api/data/deviceModel';

  const [registerModal, { openModal }] = useModal();
  const deviceOptions = ref([]);

  const orgBranch = ref('');

  async function getDevice() {
    deviceOptions.value = [];
    const params = {
      pageIndex: 1,
      pageSize: 10000,
      order: { property: 'id', direction: 'ASC' },
      criteria: {
        orgBranch: orgBranch.value,
      },
    };
    const { data } = await getvehicleList(params);
    deviceOptions.value = data;
  }

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'name',
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '设备型号',
      dataIndex: 'deviceModelName',
    },
    {
      title: '设备状态',
      dataIndex: 'online',
      customRender: ({ record, value }) => {
        switch (value) {
          case '静止':
            return <Tag color="blue">{value}</Tag>;
          case '在线':
            return <Tag color="green">{value}</Tag>;
          default:
            return <Tag color="gray">离线</Tag>;
        }
      },
    },
    {
      title: '定位时间',
      dataIndex: 'gpsTime',
    },
    {
      title: '位置',
      dataIndex: 'address',
      width: 250,
    },
    {
      title: '经纬度',
      dataIndex: 'lng',
      customRender: ({ record }) => `${record.lng},${record.lat}`,
    },
    {
      title: '速度(km/h)',
      dataIndex: 'speed',
    },
    {
      title: '时长(h)',
      dataIndex: 'workHour',
    },
    {
      title: '总里程(km)',
      dataIndex: 'totalMileage',
    },
  ];

  function batchGetAddress(data) {
    return new Promise((resolve, reject) => {
      AMap.plugin('AMap.Geocoder', function () {
        var geocoder = new AMap.Geocoder();
        var point = [data.lng, data.lat]; // 注意：高德地图的坐标顺序是先经度后纬度

        geocoder.getAddress(point, function (status, result) {
          if (status === 'complete' && result.regeocode) {
            data.address = result.regeocode.formattedAddress;
            resolve(data);
          } else {
            console.error('根据经纬度查询地址失败：', result);
            reject(result);
          }
        });
      });
    });
  }

  const [registerTable, { reload, getDataSource }] = useTable({
    api: getvehicleList,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 90,
      schemas: [
        {
          field: 'criteria.status',
          label: '设备状态',
          component: 'Select',
          colProps: { span: 6 },
          componentProps: {
            options: [
              { label: '在线', value: 'ON' },
              { label: '离线', value: 'OFF' },
            ],
          },
        },
        {
          field: 'criteria.orgId',
          label: '所属机构',
          component: 'ApiTreeSelect',
          colProps: { span: 6 },
          componentProps: {
            api: getOrgTreeOptions,
            labelField: 'name',
            valueField: 'uid',
            allowClear: false,
            onChange: async (value) => {
              if (!value) orgBranch.value = '';
            },
            onSelect: async (value, item) => {
              console.log('onSelect', value, item);
              orgBranch.value = item.branch;
              await getDevice();
            },
            getPopupContainer: () => document.body,
          },
        },
        {
          field: 'criteria.deviceIds',
          component: 'ApiSelect',
          label: '设备',
          colProps: { span: 6 },
          componentProps: {
            options: deviceOptions,
            mode: 'multiple',
            fieldNames: { label: 'name', value: 'id' },
          },
        },
        {
          field: 'criteria.deviceModelId',
          label: '设备型号',
          component: 'ApiTreeSelect',
          defaultValue: null,
          colProps: { span: 6 },
          componentProps: {
            placeholder: '请选择',
            api: getModelTree,
            labelField: 'name',
            valueField: 'id',
            getPopupContainer: () => document.body,
          },
          required: false,
        },
      ],
    },
    showTableSetting: true,
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    maxHeight: 500,
    rowKey: 'id',
    beforeFetch: ({ criteria, order }) => {
      if (criteria.dateRange && criteria.dateRange.length) {
        criteria.dateTimeRange = {
          begin: criteria.dateRange[0],
          end: criteria.dateRange[1],
        };
        delete criteria.dateRange;
      }
    },
    afterFetch: (data) => {
      return Promise.all(
        data.map(async (item) =>
          item.lng && item.lat
            ? batchGetAddress(item).then((res) => ({ ...item, address: res.address }))
            : item,
        ),
      );
    },
  });

  onMounted(async () => {
    await getDevice();
  });
</script>
