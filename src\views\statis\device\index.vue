<template>
  <div class="p-4 flex h-full flex-col njdjkd" style="background: #f5f5f5">
    <Card title="设备总览" ordered="true" class="w-full mb-4">
      <template #extra>
        <span>服务机构：</span>
        <TreeSelect
          class="w-80 mr-3"
          v-model:value="selectVal"
          show-search
          :tree-data="orgTree"
          @change="handleSelectChange"
          :fieldNames="{ label: 'name', value: 'uid' }"
          treeNodeFilterProp="name"
          :filterTreeNode="(val, treeNode) => treeNode.name.indexOf(val) !== -1"
          placeholder="请选择机构"
        />
      </template>
      <Overview :criteria="criteria" />
    </Card>
    <Card title=" " ordered="true" class="w-full mb-4">
      <Stock :criteria="criteria" />
    </Card>
    <Card title=" " ordered="true" class="w-full mb-4">
      <Active :criteria="criteria" />
    </Card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { Card, TreeSelect } from 'ant-design-vue';
  import { getOrgTreeOptions } from '@/api/passport/org';
  import Overview from './overview.vue';
  import Stock from './stock.vue';
  import Active from './active.vue';

  const selectVal = ref('');
  const orgTree = ref([]);
  const criteria = ref<any>({});

  onMounted(async () => {
    const res = await getOrgTreeOptions();
    console.log(res);
    orgTree.value = res;
  });

  const handleSelectChange = (value: string | undefined) => {
    if (value) {
      selectVal.value = value;
      criteria.value.orgId = value;
    } else {
      selectVal.value = '';
    }
  };
</script>

<style scoped>
  :deep(.ant-card) {
    border: none;
    border-radius: 5px;
  }

  :deep(.ant-card-head) {
    border: none;
    font-size: 15px !important;
  }

  :deep(.ant-card-body) {
    padding: 0;
  }

  :deep(.njdjkd .ant-card-head-titl) {
    font-size: 14px !important;
  }
</style>
