/**
 * Marker 事件管理功能使用示例 - 统一事件参数版本
 */
import { useMap, type UnifiedMarkerEvent } from '@/hooks/web/useMap';

export function markerEventsExample() {
  // 初始化地图
  const { initializeMap, addMarker, addMarkerEvent, removeMarkerEvent } = useMap({});

  async function demo() {
    // 初始化地图
    await initializeMap('map-container', {
      center: [116.397428, 39.90923],
      zoom: 13,
    });

    // 添加一个 marker
    const marker = await addMarker([116.397428, 39.90923], {
      title: '测试标记',
      icon: 'https://example.com/marker-icon.png',
    });

    if (marker) {
      // 添加点击事件 - 使用统一的事件参数
      addMarkerEvent(marker, 'click', (event: UnifiedMarkerEvent) => {
        console.log('Marker 被点击了:', {
          type: event.type,
          position: event.lnglat,
          pixel: event.pixel,
          timestamp: event.timestamp,
          extData: event.extData,
        });
        alert(
          `Marker 被点击了!\n位置: [${event.lnglat[0]}, ${event.lnglat[1]}]\n像素: [${event.pixel[0]}, ${event.pixel[1]}]`,
        );
      });

      // 添加鼠标悬停事件
      addMarkerEvent(marker, 'mouseover', (event: UnifiedMarkerEvent) => {
        console.log('鼠标悬停在 Marker 上:', {
          type: event.type,
          position: event.lnglat,
          timestamp: event.timestamp,
        });
      });

      // 添加鼠标离开事件
      addMarkerEvent(marker, 'mouseout', (event: UnifiedMarkerEvent) => {
        console.log('鼠标离开了 Marker:', {
          type: event.type,
          position: event.lnglat,
          timestamp: event.timestamp,
        });
      });

      // 添加双击事件
      addMarkerEvent(marker, 'dblclick', (event: UnifiedMarkerEvent) => {
        console.log('Marker 被双击了:', {
          type: event.type,
          position: event.lnglat,
          pixel: event.pixel,
          timestamp: event.timestamp,
        });
        // 移除点击事件
        removeMarkerEvent(marker, 'click');
        alert(`双击事件触发，点击事件已移除!\n位置: [${event.lnglat[0]}, ${event.lnglat[1]}]`);
      });

      // 添加右键点击事件
      addMarkerEvent(marker, 'rightclick', (event: UnifiedMarkerEvent) => {
        console.log('Marker 被右键点击了:', {
          type: event.type,
          position: event.lnglat,
          pixel: event.pixel,
          timestamp: event.timestamp,
          originalEvent: event.originalEvent, // 可以访问原始事件对象
        });
        // 移除所有鼠标事件
        removeMarkerEvent(marker, 'mouseover');
        removeMarkerEvent(marker, 'mouseout');
        alert(
          `右键点击事件触发，鼠标悬停事件已移除!\n位置: [${event.lnglat[0]}, ${event.lnglat[1]}]`,
        );
      });

      // 演示拖拽事件（如果支持）
      addMarkerEvent(marker, 'dragstart', (event: UnifiedMarkerEvent) => {
        console.log('开始拖拽 Marker:', {
          type: event.type,
          startPosition: event.lnglat,
          timestamp: event.timestamp,
        });
      });

      addMarkerEvent(marker, 'dragend', (event: UnifiedMarkerEvent) => {
        console.log('结束拖拽 Marker:', {
          type: event.type,
          endPosition: event.lnglat,
          timestamp: event.timestamp,
        });
        alert(`Marker 拖拽结束!\n新位置: [${event.lnglat[0]}, ${event.lnglat[1]}]`);
      });
    }
  }

  return {
    demo,
  };
}

/**
 * 使用方式：
 *
 * 1. 在 Vue 组件中使用：
 * ```vue
 * <template>
 *   <div id="map-container" style="width: 100%; height: 400px;"></div>
 *   <button @click="runDemo">运行 Marker 事件示例</button>
 * </template>
 *
 * <script setup>
 * import { markerEventsExample } from '@/examples/marker-events-example';
 * import type { UnifiedMarkerEvent } from '@/hooks/web/useMap';
 *
 * const { demo } = markerEventsExample();
 *
 * const runDemo = () => {
 *   demo();
 * };
 * </script>
 * ```
 *
 * 2. 统一事件参数结构 (UnifiedMarkerEvent)：
 * ```typescript
 * interface UnifiedMarkerEvent {
 *   type: string;                    // 事件类型
 *   target: UnifiedMarker;           // 触发事件的 marker 对象
 *   lnglat: [number, number];        // 事件发生的地理位置 [lng, lat]
 *   pixel: [number, number];         // 事件发生的像素位置 [x, y]
 *   originalEvent: any;              // 原始事件对象（用于特殊需求）
 *   extData?: any;                   // marker 的扩展数据
 *   timestamp: number;               // 事件的时间戳
 * }
 * ```
 *
 * 3. 支持的事件类型：
 * - 'click': 点击事件
 * - 'dblclick': 双击事件
 * - 'rightclick': 右键点击事件
 * - 'mouseover': 鼠标悬停事件
 * - 'mouseout': 鼠标离开事件
 * - 'mousedown': 鼠标按下事件
 * - 'mouseup': 鼠标释放事件
 * - 'drag': 拖拽事件 (Google Maps)
 * - 'dragstart': 开始拖拽事件
 * - 'dragend': 结束拖拽事件
 *
 * 4. 高德地图特有事件（在统一接口中不可用，但可通过 originalEvent 访问）：
 * - 'dragging': 拖拽中事件
 * - 'moving': 移动中事件
 * - 'moveend': 移动结束事件
 * - 'movealong': 沿路径移动事件
 * - 'touchstart': 触摸开始事件
 * - 'touchmove': 触摸移动事件
 * - 'touchend': 触摸结束事件
 *
 * 5. 优势：
 * - 🎯 统一的事件参数格式，无需在业务代码中区分地图提供商
 * - 🔄 自动参数转换，高德地图和 Google Maps 的事件参数被标准化
 * - 📍 一致的地理坐标格式 [lng, lat]
 * - 🖱️ 一致的像素坐标格式 [x, y]
 * - ⏰ 统一的时间戳格式
 * - 🔗 保留原始事件对象，支持特殊需求
 * - 💾 支持 marker 扩展数据访问
 */
