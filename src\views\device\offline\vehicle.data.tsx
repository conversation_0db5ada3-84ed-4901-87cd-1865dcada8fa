import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { getOrgTreeOptions } from '@/api/passport/org';

export const columns: BasicColumn[] = [
  {
    title: '设备名称',
    dataIndex: 'name',
    width: 280,
  },
  {
    title: 'IMEI',
    dataIndex: 'deviceSn',
  },
  {
    title: '设备状态',
    dataIndex: 'online',
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.online == '在线' ? 'green' : '#ccc',
        },
        {
          default: () => record.online || '离线',
        },
      );
    },
  },
  {
    title: '协议类型',
    dataIndex: 'protocol',
  },
  {
    title: '设备类型',
    dataIndex: 'deviceModelName',
  },
  {
    title: '激活时间',
    dataIndex: 'activateTime',
  },
  {
    title: 'SIM卡号',
    dataIndex: 'simNo',
  },
  {
    title: '所属机构',
    dataIndex: 'orgName',
  },
  {
    title: '平台到期时间',
    dataIndex: 'expireTime',
  },
  {
    title: '备注',
    dataIndex: 'comment',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];
export const statiusColumns: BasicColumn[] = [
  {
    title: '车架号',
    dataIndex: 'vin',
    width: 280,
  },
  {
    title: '车牌号',
    dataIndex: 'license',
  },
  {
    title: '设备序号',
    dataIndex: 'deviceSn',
  },
  {
    title: '所属机构',
    dataIndex: 'org',
  },
  {
    title: '在线状态',
    dataIndex: 'online',
    align: 'center',

    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.online == '在线' ? 'green' : 'blue',
        },
        {
          default: () => record.online,
        },
      );
    },
  },
  {
    title: 'gps时间',
    dataIndex: 'gpsTime',
    width: 280,
  },
  {
    title: '网关接受数据时间',
    dataIndex: 'gatewayTime',
    width: 280,
  },
  {
    title: '定位状态',
    dataIndex: 'location',
    align: 'center',
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.location == '有效定位' ? 'green' : 'blue',
        },
        {
          default: () => record.location || '-',
        },
      );
    },
  },
  {
    title: 'ACC状态',
    dataIndex: 'acc',
    align: 'center',
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.acc == '开' ? 'green' : 'red',
        },
        {
          default: () => record.acc || '-',
        },
      );
    },
  },
  {
    title: '锁车状态',
    dataIndex: 'lockStatus',
    align: 'center',
    customRender: ({ record }) => {
      return h(
        Tag,
        {
          color: record.lockStatus == '未锁' ? 'green' : 'red',
        },
        {
          default: () => record.lockStatus || '-',
        },
      );
    },
  },
  {
    title: '当前位置',
    dataIndex: 'gpsPosition',
    width: 200,
  },
  {
    title: 'ACC开机时长',
    dataIndex: 'accHour',
  },
  {
    title: '发动机工作时长',
    dataIndex: 'workHour',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'criteria.name',
    label: '设备名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'criteria.orgUid',
    label: '所属机构',
    component: 'ApiTreeSelect',
    colProps: { span: 6 },
    componentProps: {
      api: getOrgTreeOptions,
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
  },
];
