<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-10-06 08:53:36
 * @LastEditors  : chen
 * @LastEditTime : 2024-10-06 12:12:12
 * @FilePath     : \special-front\src\views\alarm\history\index.vue
 * Copyright (C) 2024 chen. All rights reserved.
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'range'">
          <span v-if="record.beginTime && record.endTime">{{
            record.beginTime + '~' + record.endTime
          }}</span>
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts">
  import { defineComponent, watch } from 'vue';
  import { Tag } from 'ant-design-vue';

  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getAlarmHistory } from '@/api/vehicle/vehlist';
  import { useMessage } from '@/hooks/web/useMessage';

  import { useModal } from '@/components/Modal';
  import { useDrawer } from '@/components/Drawer';
  import { columns, searchFormSchema } from './vehicle.data';
  import dayjs from 'dayjs';
  import { useRoute, useRouter } from 'vue-router';

  export default defineComponent({
    name: 'VehicleList',
    components: { BasicTable, TableAction, Tag },
    setup() {
      const route = useRoute();
      watch(
        () => route,
        () => {
          if (history.state?.code) {
            reload();
          }
        },
        { deep: true },
      );
      const [registerTable, { reload, getForm }] = useTable({
        title: '报警列表',
        api: (params) => {
          // eslint-disable-next-line no-async-promise-executor
          return new Promise(async (resolve, reject) => {
            try {
              let res = await getAlarmHistory(params);
              res.data = await getLocations(res.data, 0);
              resolve(res);
            } catch (error) {
              reject(error);
            }
          });
        },
        columns,
        formConfig: {
          labelAlign: 'left',
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        beforeFetch: async (searchInfo: any) => {
          if (history.state?.code) {
            getForm().setFieldsValue({ 'criteria.vin': history.state?.code }, 'criteria.vin');
            searchInfo.criteria.vin = history.state?.code;
            history.replaceState({}, '', window.location.href);
          }
          if (searchInfo.criteria.range && searchInfo.criteria.range.length) {
            searchInfo.criteria.begin = dayjs(searchInfo.criteria.range[0]).format(
              'YYYY-MM-DD 00:00:00',
            );
            searchInfo.criteria.end = dayjs(searchInfo.criteria.range[1]).format(
              'YYYY-MM-DD 23:59:59',
            );
          } else {
            searchInfo.criteria.begin = dayjs().subtract(7, 'day').format('YYYY-MM-DD 00:00:00');
            searchInfo.criteria.end = dayjs().format('YYYY-MM-DD 23:59:59');
          }
        },
      });

      const [registerFormModal, { openModal: openFormModal }] = useModal();

      const [registerDrawer] = useDrawer();

      function handleCreate() {
        openFormModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        console.log(record);
        openFormModal(true, {
          record,
          isUpdate: true,
        });
      }
      const geocoder = new AMap.Geocoder({
        city: '010', // 城市设为北京，默认：“全国”
        radius: 1000, // 范围，默认：500
      });

      function getLocations(list, num) {
        if (num >= list.length) {
          return Promise.resolve(list);
        }
        if (list[num].endTime)
          list[num].content =
            `报警持续了` +
            Math.ceil((dayjs(list[num].endTime).unix() - dayjs(list[num].beginTime).unix()) / 60) +
            `分钟`;
        if (list[num].gcj02Lat && list[num].gcj02Lng) {
          return new Promise((resolve) => {
            geocoder.getAddress(
              new AMap.LngLat(list[num].gcj02Lng, list[num].gcj02Lat),
              (status, result) => {
                if (status === 'complete' && result.regeocode) {
                  list[num].gpsPosition = result.regeocode.formattedAddress;
                } else {
                  list[num].gpsPosition = '';
                }
                resolve(getLocations(list, num + 1));
              },
            );
          });
        } else {
          return getLocations(list, num + 1);
        }
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        const { createMessage } = useMessage();
        removevehicle(record?.id).then((res) => {
          createMessage.success('删除成功');
          reload();
        });
      }
      //查询司机
      const [registerAuthFormModal, { openModal: openDriverFormModal }] = useModal();
      function queryDriver(type, record) {
        console.log(type, record);
        openDriverFormModal(true, {
          type,
          record,
        });
      }

      function getDropDownAction(record) {
        return [
          {
            label: 'IC卡查询',
            onClick: queryDriver.bind(null, 1, record),
          },
          {
            label: '指纹查询',
            onClick: queryDriver.bind(null, 2, record),
          },
          {
            label: '人脸查询',
            onClick: queryDriver.bind(null, 3, record),
          },
        ];
      }
      function handleSuccess() {
        reload();
      }
      function handleAuthSuccess() {
        reload();
      }
      return {
        registerTable,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        registerFormModal,
        registerDrawer,
        getDropDownAction,
        handleAuthSuccess,
        registerAuthFormModal,
      };
    },
  });
</script>
