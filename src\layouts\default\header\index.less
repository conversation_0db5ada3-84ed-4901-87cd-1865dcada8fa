@header-trigger-prefix-cls: ~'@{namespace}-layout-header-trigger';
@header-prefix-cls: ~'@{namespace}-layout-header';
@breadcrumb-prefix-cls: ~'@{namespace}-layout-breadcrumb';
@logo-prefix-cls: ~'@{namespace}-app-logo';

.ant-layout .@{header-prefix-cls} {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: @header-height;
  margin-left: -1px;
  padding: 0;
  background-color: @white;
  color: @white;
  line-height: @header-height;

  &--mobile {
    .@{breadcrumb-prefix-cls},
    .error-action,
    .notify-item,
    .fullscreen-item {
      display: none;
    }

    .@{logo-prefix-cls} {
      min-width: unset;
      padding-right: 0;

      &__title {
        display: none;
      }
    }
    .@{header-trigger-prefix-cls} {
      padding: 0 4px 0 8px !important;
    }
    .@{header-prefix-cls}-action {
      padding-right: 4px;
    }
  }

  &--fixed {
    position: fixed;
    z-index: @layout-header-fixed-z-index;
    top: 0;
    left: 0;
    width: 100%;
  }

  &-logo {
    min-width: 192px;
    height: @header-height;
    padding: 0 10px;
    font-size: 14px;

    img {
      width: @logo-width;
      height: @logo-width;
      margin-right: 2px;
    }
  }

  &-left {
    display: flex;
    align-items: center;
    height: 100%;

    .@{header-trigger-prefix-cls} {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 1px 10px 0;
      cursor: pointer;

      .anticon {
        font-size: 16px;
      }

      &.light {
        &:hover {
          background-color: @header-light-bg-hover-color;
        }

        svg {
          fill: #000;
        }
      }

      &.dark {
        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }

  &-menu {
    flex: 1;
    align-items: center;
    min-width: 0;
    height: 100%;
  }

  &-action {
    display: flex;
    // padding-right: 12px;
    align-items: center;
    min-width: 180px;

    &__item {
      display: flex !important;
      align-items: center;
      height: @header-height;
      padding: 0 2px;
      font-size: 1.2em;
      cursor: pointer;

      .ant-badge {
        height: @header-height;
        line-height: @header-height;
      }

      .ant-badge-dot {
        top: 14px;
        right: 2px;
      }
    }

    span[role='img'] {
      padding: 0 8px;
    }
  }

  &--light {
    border-bottom: 1px solid @header-light-bottom-border-color;
    border-left: 1px solid @header-light-bottom-border-color;
    background-color: @white !important;

    .@{header-prefix-cls}-logo {
      color: @text-color-base;

      &:hover {
        background-color: @header-light-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        color: @text-color-base;

        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        &:hover {
          background-color: @header-light-bg-hover-color;
        }
      }

      &-icon,
      span[role='img'] {
        color: @text-color-base;
      }
    }
  }

  &--dark {
    // border-bottom: 1px solid @border-color-base;
    border-left: 1px solid @border-color-base;
    background-color: @header-dark-bg-color !important;
    .@{header-prefix-cls}-logo {
      &:hover {
        background-color: @header-dark-bg-hover-color;
      }
    }

    .@{header-prefix-cls}-action {
      &__item {
        .app-iconify {
          padding: 0 10px;
          font-size: 16px !important;
        }

        .ant-badge {
          span {
            color: @white;
          }
        }

        &:hover {
          background-color: @header-dark-bg-hover-color;
        }
      }
    }
  }
}
.vben-layout-mix-sider{
  z-index: 999 !important;
  top:50px !important;
}
.vben-layout-mix-sider-module{
  padding-top: 10px !important;
}
.vben-layout-mix-sider-module__item{
  padding-top: 22px !important;
}
.vben-layout-mix-sider-module__item span{
  font-size: 23px !important;
}
.vben-layout-mix-sider-module__name{
  font-size: 14px !important;
  color: #8190DD !important;
}
.vben-layout-mix-sider-module__item--active{
  font-weight: 400 !important;
}
.vben-layout-mix-sider-module__item--active .vben-layout-mix-sider-module__name{
  color: #fff !important;
}
.vben-layout-mix-sider-module__item:hover{
  color: #fff !important;
}
.vben-layout-mix-sider-module__name:hover{
  color: #fff !important;
}
.ant-input-affix-wrapper,.ant-select-single .ant-select-selector,.ant-select-dropdown,.ant-input,.ant-picker,.ant-select-multiple .ant-select-selector,.ant-select-multiple .ant-select-selection-item{
  border-radius: 2px;
}
.ant-input-search > .ant-input-group > .ant-input-group-addon:last-child .ant-input-search-button{
  border-radius: 0;
  border-start-start-radius: 0;
  border-start-end-radius:  2px;
  border-end-end-radius:  2px;
  border-end-start-radius: 0;
}
.ant-btn-primary,.ant-btn-default{  
  border-radius: 2px;
}
.ant-table-title .ant-btn-primary {
  height: 28px;
  padding: 0 10px;
  border:none;
  background: #0421bc;
  color: #fff !important;
  border-radius: 2px;

}

.ant-table-title .ant-btn-primary:hover {
  padding: 0 10px;
  border:none;
  background: #2C45C7;
  color: #fff !important;
}

.vben-menu-dark.vben-menu-vertical .vben-menu-item-active:not(.vben-menu-submenu),
.vben-menu-dark.vben-menu-vertical .vben-menu-submenu-title-active:not(.vben-menu-submenu) {
  background: #E5ECF7 !important;
  color:#0421BC !important ;
  border-radius: 0;
}
.vben-menu-dark.vben-menu-vertical .vben-menu-submenu-title{
  color: #000;
}
.vben-menu-dark.vben-menu-vertical .vben-menu-child-item-active > .vben-menu-submenu-title{
  color: #0421bc;
}
.vben-menu-dark.vben-menu-vertical .vben-menu-submenu-title:hover{
  color: #0421bc;
}
.vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab-active {
  border:none !important;
  border-radius: 0;
  background: #0421bc !important;
  color:#fff  !important;

  span{
    color: #fff !important;
  }
}

.vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab {
  border-radius: 0;
  color: #000;
}

.css-dev-only-do-not-override-16ev87q.ant-btn-primary:not(:disabled):hover {
  background: #006BFF;
}

.css-dev-only-do-not-override-16ev87q.ant-btn-primary {
  box-shadow: none;
}

:deep(.ant-layout .vben-layout-header--light) {
  background: #fff !important;
  color: #333 !important;
}
.ant-layout .vben-layout-header-left h3{
  color: #000 !important;
  font-size: 21px;
  font-weight: 400;
}
.ant-layout .vben-layout-header-left{
  flex: 1;
}
.ant-layout .vben-layout-header-left .vben-layout-header-trigger.light:hover {
  background: none !important;
}

.ant-layout .vben-layout-header-left .vben-layout-header-trigger.light svg {
  fill: #333 !important;
}

.ant-layout .vben-layout-header--light .vben-layout-header-action-icon,
.ant-layout .vben-layout-header--light .vben-layout-header-action span[role='img'] {
  color: #fff;
}

.vben-layout-header-action .anticon svg {
  color: #000;
}

.ant-layout .vben-layout-header--light .vben-layout-header-action__item:hover {
  background: none;
}

.vben-header-user-dropdown--light:hover {
  background: none !important;
}

.css-dev-only-do-not-override-16ev87q.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab,
.css-dev-only-do-not-override-16ev87q.ant-tabs-card.ant-tabs-top
  > div
  > .ant-tabs-nav
  .ant-tabs-tab {
  background: none;
}

.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab,
.ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab {
  margin-bottom: 2px;
  border: solid 1px #ccc !important;
  border-bottom: none !important;
  border-radius: 0;
  background: none;
  font-weight: bold;
}

.ant-tabs .ant-tabs-tab{
  padding: 16px 0 7px;
}

.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active,
.ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab-active {
  border:solid 1px #0421bc !important;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  background: #0421bc !important;
}

.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active .ant-tabs-tab-btn,
.ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #fff !important;
  font-weight: 700;
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #333 !important;
  font-weight: 700;
}
.ant-table-wrapper .ant-table{
  margin: 0;
  background: #F6F7F9;
}
.vben-basic-table{
  display: flex;
  flex-direction: column;
}
.vben-basic-table .ant-table{
  width: 100% !important;
}
.ant-table-wrapper .ant-table-tbody > tr > td {
  padding: 15px;
}
.ant-table-wrapper .ant-table .ant-table-header{
  border-radius: 0;
}
 .ant-table-header
.ant-table-wrapper .ant-table-thead > tr > th {
  padding: 10px 15px;
}
.ant-table-wrapper .ant-table-thead > tr > th {
    background: #F6F8FA !important;
    border-bottom: solid 1px #ddd;
}
.ant-table-wrapper .ant-table .ant-table-title + .ant-table-container{
  box-shadow: 0 2px 3px 0 #bbb !important;
  margin: 0 5px;
  background: #fff;
}

.ant-tabs-top > .ant-tabs-nav,
.ant-tabs-bottom > .ant-tabs-nav,
.ant-tabs-top > div > .ant-tabs-nav,
.ant-tabs-bottom > div > .ant-tabs-nav {
  margin: 0 !important;
}
.ant-tabs-top > .ant-tabs-nav::before{
  border:none !important
}
.vben-layout-multiple-header--fixed{
  z-index: 560 !important;
}

.vben-basic-table .ant-table-wrapper{
 padding: 0 !important;
 background: none !important;
 flex: 1;
}
.vben-basic-table .ant-table-wrapper .ant-spin-nested-loading {
  height: 100%;
}
.vben-basic-table .ant-table-wrapper .ant-spin-nested-loading .ant-spin-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.vben-basic-table .ant-table-wrapper .ant-table.ant-table-bordered .ant-table-title{
  background: #F6F7F9!important;
}
.ant-layout{
  background: #F6F7F9!important;
}
.vben-basic-table-header__toolbar{
  justify-content:initial !important;
  padding-left: 5px;
}
.vben-basic-table-header__toolbar .table-settings{
  margin-right: 0;
}


// 二级菜单样式开始
.vben-layout-mix-sider.dark .vben-layout-mix-sider-menu-list{
  box-shadow: 0 0 10px #ccc;
  margin: 65px 0 0 15px !important;
  height: calc(100% - 80px) !important;
  width: 215px !important;
  border-radius: 2px;
  padding-top: 3px!important;
}

// 二级菜单样式结束


// .ant-form-item .ant-form-item-label{
//   text-align: left;
//   padding-left: 5px;
// }
.ant-form-item .ant-form-item-label{
  padding-left: 5px;
}
.ant-form-item .ant-form-item-control-input-content div{
  margin-right: 10px;
}
.ant-pagination .ant-pagination-item,.ant-select-single.ant-select-sm .ant-select-selector{
  border-radius: 2px;
}

.ant-modal .ant-modal-content{
  border-radius: 2px;
}

.ant-table-wrapper .ant-table-container table > thead > tr:first-child > :first-child{
  border-start-start-radius:2px;
}
.ant-table-wrapper .ant-table-container table > thead > tr:first-child > :last-child {
  border-start-end-radius: 2px;
}
.ant-table-tbody > tr:first-child > td{
  background: #fff;
}