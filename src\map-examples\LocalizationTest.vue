<template>
  <div class="localization-test">
    <h2>智能本地化测试</h2>

    <div class="test-panel">
      <h3>检测结果</h3>
      <div class="result-item"> <strong>用户语言:</strong> {{ userLanguage || '未检测到' }} </div>
      <div class="result-item"> <strong>用户位置:</strong> {{ userLocation || '检测中...' }} </div>
      <div class="result-item"> <strong>国家代码:</strong> {{ countryCode || '未检测到' }} </div>
      <div class="result-item">
        <strong>Google Maps区域:</strong> {{ googleMapsRegion || '未映射' }}
      </div>
      <div class="result-item">
        <strong>最终配置:</strong>
        <pre>{{ JSON.stringify(finalMapConfig, null, 2) }}</pre>
      </div>
    </div>

    <div class="control-panel">
      <button @click="testDetection" :disabled="isLoading">重新检测</button>
      <button @click="testWithCustomOptions">使用自定义选项检测</button>
    </div>

    <div v-if="error" class="error"> 错误: {{ error }} </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import {
    getUserLanguage,
    getUserMapConfig,
    getGoogleMapsRegion,
    getCachedUserLocation,
    type GeoDetectionOptions,
  } from '@/utils/geoLocation';

  // 响应式数据
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const userLanguage = ref<string | undefined>();
  const userLocation = ref<string>('');
  const countryCode = ref<string | undefined>();
  const googleMapsRegion = ref<string | undefined>();
  const finalMapConfig = ref<any>({});

  // 检测用户信息
  async function testDetection() {
    try {
      isLoading.value = true;
      error.value = null;

      // 检测用户语言
      userLanguage.value = getUserLanguage();
      console.log('检测到的用户语言:', userLanguage.value);

      // 检测用户位置
      const location = await getCachedUserLocation();
      userLocation.value = location.country || '未知';
      countryCode.value = location.countryCode;
      console.log('检测到的用户位置:', location);

      // 获取Google Maps区域映射
      if (countryCode.value) {
        googleMapsRegion.value = getGoogleMapsRegion(countryCode.value);
        console.log('Google Maps区域映射:', googleMapsRegion.value);
      }

      // 获取最终的地图配置
      const mapConfig = await getUserMapConfig();
      finalMapConfig.value = mapConfig;
      console.log('最终地图配置:', mapConfig);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '检测失败';
      console.error('检测失败:', err);
    } finally {
      isLoading.value = false;
    }
  }

  // 使用自定义选项进行检测
  async function testWithCustomOptions() {
    try {
      isLoading.value = true;
      error.value = null;

      const customOptions: GeoDetectionOptions = {
        timeout: 3000,
        fallbackToTimezone: true,
        fallbackToLanguage: true,
        useMultipleMethods: true,
      };

      console.log('使用自定义选项:', customOptions);

      // 使用自定义选项获取地图配置
      const mapConfig = await getUserMapConfig(customOptions);
      finalMapConfig.value = { ...mapConfig, customOptions };
      console.log('使用自定义选项的地图配置:', mapConfig);
    } catch (err) {
      error.value = err instanceof Error ? err.message : '检测失败';
      console.error('自定义选项检测失败:', err);
    } finally {
      isLoading.value = false;
    }
  }

  // 组件挂载时自动检测
  onMounted(() => {
    testDetection();
  });
</script>

<style scoped>
  .localization-test {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
  }

  .test-panel {
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 8px;
    background-color: #f5f5f5;
  }

  .test-panel h3 {
    margin-top: 0;
    color: #333;
  }

  .result-item {
    margin-bottom: 15px;
    padding: 10px;
    border-left: 4px solid #1890ff;
    border-radius: 4px;
    background-color: white;
  }

  .result-item strong {
    margin-right: 10px;
    color: #333;
  }

  .result-item pre {
    margin: 10px 0 0;
    padding: 10px;
    overflow-x: auto;
    border-radius: 4px;
    background-color: #f8f8f8;
    font-size: 12px;
  }

  .control-panel {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
  }

  .control-panel button {
    padding: 10px 20px;
    transition: background-color 0.3s;
    border: none;
    border-radius: 4px;
    background-color: #1890ff;
    color: white;
    cursor: pointer;
  }

  .control-panel button:hover:not(:disabled) {
    background-color: #40a9ff;
  }

  .control-panel button:disabled {
    background-color: #d9d9d9;
    cursor: not-allowed;
  }

  .error {
    padding: 15px;
    border: 1px solid #ffccc7;
    border-radius: 4px;
    background-color: #fff2f0;
    color: #ff4d4f;
  }
</style>
