/**
 * 地理位置和网络环境检测工具
 */

export interface LocationInfo {
  country?: string;
  countryCode?: string;
  region?: string;
  city?: string;
  timezone?: string;
  ip?: string;
  isp?: string;
  isChina: boolean;
  confidence: number; // 置信度 0-1
  method: string; // 检测方法
}

export interface GeoDetectionOptions {
  timeout?: number; // 超时时间，默认5000ms
  fallbackToTimezone?: boolean; // 是否回退到时区检测，默认true
  fallbackToLanguage?: boolean; // 是否回退到语言检测，默认true
  useMultipleMethods?: boolean; // 是否使用多种方法综合判断，默认true
}

/**
 * 中国大陆的时区
 */
const CHINA_TIMEZONES = [
  'Asia/Shanghai',
  'Asia/Beijing',
  'Asia/Chongqing',
  'Asia/Harbin',
  'Asia/Kashgar',
  'Asia/Urumqi',
];

/**
 * 中国相关的语言代码
 */
const CHINA_LANGUAGES = ['zh-CN', 'zh', 'zh-Hans'];

/**
 * IP地理位置检测服务列表
 */
const GEO_SERVICES = [
  {
    name: 'ipapi',
    url: 'https://ipapi.co/json/',
    parser: (data: any): Partial<LocationInfo> => ({
      country: data.country_name,
      countryCode: data.country_code,
      region: data.region,
      city: data.city,
      timezone: data.timezone,
      ip: data.ip,
      isp: data.org,
      isChina: data.country_code === 'CN',
    }),
  },
  {
    name: 'ipinfo',
    url: 'https://ipinfo.io/json',
    parser: (data: any): Partial<LocationInfo> => ({
      country: data.country,
      countryCode: data.country,
      region: data.region,
      city: data.city,
      timezone: data.timezone,
      ip: data.ip,
      isp: data.org,
      isChina: data.country === 'CN',
    }),
  },
  {
    name: 'ip-api',
    url: 'http://ip-api.com/json/',
    parser: (data: any): Partial<LocationInfo> => ({
      country: data.country,
      countryCode: data.countryCode,
      region: data.regionName,
      city: data.city,
      timezone: data.timezone,
      ip: data.query,
      isp: data.isp,
      isChina: data.countryCode === 'CN',
    }),
  },
];

/**
 * 通过时区检测是否在中国
 */
function detectByTimezone(): Partial<LocationInfo> {
  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const isChina = CHINA_TIMEZONES.includes(timezone);

    return {
      timezone,
      isChina,
      confidence: isChina ? 0.7 : 0.3, // 时区可能被修改，置信度中等
      method: 'timezone',
    };
  } catch (error) {
    console.warn('Failed to detect timezone:', error);
    return {
      isChina: false,
      confidence: 0,
      method: 'timezone-failed',
    };
  }
}

/**
 * 通过语言设置检测是否在中国
 */
function detectByLanguage(): Partial<LocationInfo> {
  try {
    const languages = navigator.languages || [navigator.language];
    const hasChineseLanguage = languages.some((lang) =>
      CHINA_LANGUAGES.some((chinaLang) => lang.startsWith(chinaLang)),
    );

    return {
      isChina: hasChineseLanguage,
      confidence: hasChineseLanguage ? 0.5 : 0.2, // 语言设置可能不准确，置信度较低
      method: 'language',
    };
  } catch (error) {
    console.warn('Failed to detect language:', error);
    return {
      isChina: false,
      confidence: 0,
      method: 'language-failed',
    };
  }
}

/**
 * 通过IP地理位置服务检测
 */
async function detectByIP(timeout: number = 5000): Promise<Partial<LocationInfo>> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 尝试多个服务，使用第一个成功的
    for (const service of GEO_SERVICES) {
      try {
        const response = await fetch(service.url, {
          signal: controller.signal,
          headers: {
            Accept: 'application/json',
          },
        });

        if (!response.ok) {
          continue;
        }

        const data = await response.json();
        const result = service.parser(data);

        clearTimeout(timeoutId);
        return {
          ...result,
          confidence: 0.9, // IP检测置信度较高
          method: `ip-${service.name}`,
        };
      } catch (error) {
        console.warn(`Failed to detect location using ${service.name}:`, error);
        continue;
      }
    }

    throw new Error('All IP detection services failed');
  } catch (error) {
    clearTimeout(timeoutId);
    console.warn('Failed to detect location by IP:', error);
    return {
      isChina: false,
      confidence: 0,
      method: 'ip-failed',
    };
  }
}

/**
 * 综合多种方法的结果
 */
function combineResults(results: Partial<LocationInfo>[]): LocationInfo {
  const validResults = results.filter((r) => r.confidence && r.confidence > 0);

  if (validResults.length === 0) {
    return {
      isChina: false,
      confidence: 0,
      method: 'all-failed',
    };
  }

  // 计算加权平均
  let totalWeight = 0;
  let weightedChinaScore = 0;
  let bestResult: Partial<LocationInfo> = validResults[0];

  validResults.forEach((result) => {
    const weight = result.confidence || 0;
    totalWeight += weight;
    weightedChinaScore += (result.isChina ? 1 : 0) * weight;

    // 选择置信度最高的结果作为基础信息
    if ((result.confidence || 0) > (bestResult.confidence || 0)) {
      bestResult = result;
    }
  });

  const finalConfidence = totalWeight / validResults.length;
  const isChina = weightedChinaScore / totalWeight > 0.5;

  return {
    country: bestResult.country,
    countryCode: bestResult.countryCode,
    region: bestResult.region,
    city: bestResult.city,
    timezone: bestResult.timezone,
    ip: bestResult.ip,
    isp: bestResult.isp,
    isChina,
    confidence: finalConfidence,
    method: `combined-${validResults.map((r) => r.method).join(',')}`,
  };
}

/**
 * 检测用户是否在中国大陆
 */
export async function detectUserLocation(options: GeoDetectionOptions = {}): Promise<LocationInfo> {
  const {
    timeout = 5000,
    fallbackToTimezone = true,
    fallbackToLanguage = true,
    useMultipleMethods = true,
  } = options;

  const results: Partial<LocationInfo>[] = [];

  try {
    // 首先尝试IP检测（最准确）
    const ipResult = await detectByIP(timeout);
    results.push(ipResult);

    // 如果IP检测失败或置信度不高，使用其他方法
    if (useMultipleMethods || (ipResult.confidence || 0) < 0.8) {
      if (fallbackToTimezone) {
        const timezoneResult = detectByTimezone();
        results.push(timezoneResult);
      }

      if (fallbackToLanguage) {
        const languageResult = detectByLanguage();
        results.push(languageResult);
      }
    }

    return combineResults(results);
  } catch (error) {
    console.error('Failed to detect user location:', error);

    // 如果所有方法都失败，尝试回退方法
    if (fallbackToTimezone) {
      const timezoneResult = detectByTimezone();
      results.push(timezoneResult);
    }

    if (fallbackToLanguage) {
      const languageResult = detectByLanguage();
      results.push(languageResult);
    }

    return combineResults(results);
  }
}

/**
 * 缓存检测结果
 */
let cachedResult: LocationInfo | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 30 * 60 * 1000; // 30分钟

/**
 * 获取缓存的位置信息或重新检测
 */
export async function getCachedUserLocation(
  options: GeoDetectionOptions = {},
): Promise<LocationInfo> {
  const now = Date.now();

  if (cachedResult && now - cacheTimestamp < CACHE_DURATION) {
    return cachedResult;
  }

  cachedResult = await detectUserLocation(options);
  cacheTimestamp = now;

  return cachedResult;
}

/**
 * 清除缓存
 */
export function clearLocationCache(): void {
  cachedResult = null;
  cacheTimestamp = 0;
}

/**
 * 简化的中国检测函数
 */
export async function isInChina(options: GeoDetectionOptions = {}): Promise<boolean> {
  try {
    const location = await getCachedUserLocation(options);
    return location.isChina;
  } catch (error) {
    console.error('Failed to detect if user is in China:', error);
    // 默认返回false，使用国际版本
    return false;
  }
}
