<template>
  <!-- <LoginFormTitle v-show="getShow" class="enter-x" /> -->
  <Form
    class="p-4 enter-x"
    :model="formData"
    :rules="getFormRules"
    ref="formRef"
    v-show="getShow"
    @keypress.enter="handleLogin"
  >
    <!-- <FormItem class="enter-x">
      <Select
        size="large"
        v-model:value="formData.application"
        :placeholder="t('sys.login.application')"
        class="fix-auto-fill"
        @change="changeLift"
      >
        <Option value="special-lift">电梯物联网安全监控平台</Option>
        <Option value="special-forklift">叉车设备监控平台</Option>
      </Select>
    </FormItem> -->
    <FormItem name="account" class="enter-x">
      <Input
        size="large"
        v-model:value="formData.account"
        :placeholder="t('sys.login.userName')"
        class="fix-auto-fill"
      >
        <template #prefix>
          <Icon icon="tdesign:user-filled" style="font-size: 19px; color: #7E8792"/>
        </template>
      </Input>
    </FormItem>
    <FormItem name="password" class="enter-x">
      <InputPassword
        size="large"
        visibilityToggle
        v-model:value="formData.password"
        :placeholder="t('sys.login.password')"
      >
        <template #prefix>
          <Icon icon="mdi:password" style="font-size: 19px; color: #7E8792"/>
        </template>
      </InputPassword>
    </FormItem>

    <FormItem class="enter-x">
      <Button type="primary" size="large" block @click="handleLogin" :loading="loading" style="margin-top: 10px !important;">
        {{ t('sys.login.loginButton') }}
      </Button>
    </FormItem>
  </Form>
</template>
<script lang="ts" setup>
  import { reactive, ref, unref, computed } from 'vue';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';
  import { Form, Input, Button, Select } from 'ant-design-vue';
  import LoginFormTitle from './LoginFormTitle.vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';

  import { useUserStore } from '@/store/modules/user';
  import { LoginStateEnum, useLoginState, useFormRules, useFormValid } from './useLogin';
  import { useDesign } from '@/hooks/web/useDesign';
  import { onKeyStroke } from '@vueuse/core';

  const { Option } = Select;
  const FormItem = Form.Item;
  const InputPassword = Input.Password;
  const { t } = useI18n();
  const { notification, createErrorModal } = useMessage();
  const { prefixCls } = useDesign('login');
  const userStore = useUserStore();

  const { getLoginState } = useLoginState();
  const { getFormRules } = useFormRules();

  const formRef = ref();
  const loading = ref(false);

  const formData = reactive({
    // application: 'special-lift',
    account: '',
    password: '',
  });
  // const changeLift = () => {
  //   localStorage.setItem('application', formData.application);
  // };
  // changeLift();
  const { validForm } = useFormValid(formRef);

  onKeyStroke('Enter', handleLogin);

  const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN);

  async function handleLogin() {
    const data = await validForm();
    if (!data) return;
    try {
      loading.value = true;
      const userInfo = await userStore.login({
        password: data.password,
        loginName: data.account,
        application: formData.application,
      });
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.name}`,
          duration: 3,
        });
      }
    } catch (error) {
      createErrorModal({
        title: t('sys.api.errorTip'),
        content: (error as unknown as Error).message || t('sys.api.networkExceptionMsg'),
        getContainer: () => document.body.querySelector(`.${prefixCls}`) || document.body,
      });
    } finally {
      loading.value = false;
    }
  }
</script>
<style scoped lang="less">
  :deep(:where(.css-dev-only-do-not-override-ez24qu).ant-row) {
    display: block;
  }

  .ant-input-affix-wrapper,.ant-input {
    border: solid 1px #EAECEF;
    font-size: 14px;
  }

  .ant-form-item {
    :deep(.ant-input-affix-wrapper > input.ant-input) {
      color: #000 !important;
    }
  }
</style>
