<template>
  <!-- <LoginFormTitle v-show="getShow" class="enter-x" /> -->
  <Form
    class="p-4 enter-x"
    :model="formData"
    :rules="getFormRules"
    ref="formRef"
    v-show="getShow"
    @keypress.enter="handleLogin"
  >
    <!-- <FormItem class="enter-x">
      <Select
        size="large"
        v-model:value="formData.application"
        :placeholder="t('sys.login.application')"
        class="fix-auto-fill"
        @change="changeLift"
      >
        <Option value="special-lift">电梯物联网安全监控平台</Option>
        <Option value="special-forklift">叉车设备监控平台</Option>
      </Select>
    </FormItem> -->
    <div v-if="loginTypeActive === 'ACCOUNT'">
      <FormItem name="account" class="enter-x">
        <Input
          size="large"
          v-model:value="formData.account"
          :placeholder="t('sys.login.userAndMob')"
          class="fix-auto-fill"
        >
          <template #prefix>
            <Icon icon="tabler:user-filled" style="font-size: 19px; color: #7e8792" />
          </template>
        </Input>
      </FormItem>
      <FormItem name="password" class="enter-x">
        <InputPassword
          size="large"
          visibilityToggle
          v-model:value="formData.password"
          :placeholder="t('sys.login.password')"
        >
          <template #prefix>
            <Icon icon="mdi:password" style="font-size: 19px; color: #7e8792" />
          </template>
        </InputPassword>
      </FormItem>
    </div>

    <div v-else>
      <FormItem name="mobile" class="enter-x">
        <Input
          size="large"
          v-model:value="formData.mobile"
          :placeholder="t('sys.login.mobile')"
          class="fix-auto-fill"
        >
          <template #prefix>
            <Icon icon="icomoon-free:mobile" style="font-size: 19px; color: #7e8792" />
          </template>
        </Input>
      </FormItem>

      <FormItem name="code" class="enter-x">
        <Input
          size="large"
          v-model:value="formData.code"
          maxlength="4"
          :placeholder="t('sys.login.smsCode')"
        >
          <template #prefix>
            <Icon icon="tabler:message-circle-code" style="font-size: 19px; color: #7e8792" />
          </template>
          <template #suffix>
            <span v-if="countDown" style="margin-left: 10px; color: #7e8792">{{ countDown }}</span>
            <a
              v-else
              href="javascript: void(0)"
              style="margin-left: 10px; color: #7e8792"
              @click="handleGetCode"
              >{{ t('sys.login.getSmsCode') }}</a
            >
          </template>
        </Input>
      </FormItem>
    </div>

    <FormItem class="enter-x">
      <Button
        type="primary"
        size="large"
        block
        @click="handleLogin"
        :loading="loading"
        style="margin-top: 10px !important"
      >
        {{ t('sys.login.loginButton') }}
      </Button>
    </FormItem>
  </Form>
</template>
<script lang="ts" setup>
  import { reactive, ref, unref, computed, toRefs, watch } from 'vue';
  import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';
  import { Form, Input, Button, Select } from 'ant-design-vue';
  import LoginFormTitle from './LoginFormTitle.vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useI18n } from '@/hooks/web/useI18n';
  import { useMessage } from '@/hooks/web/useMessage';

  import { useUserStore } from '@/store/modules/user';
  import { LoginStateEnum, useLoginState, useFormRules, useFormValid } from './useLogin';
  import { useDesign } from '@/hooks/web/useDesign';
  import { onKeyStroke } from '@vueuse/core';

  const { Option } = Select;
  const FormItem = Form.Item;
  const InputPassword = Input.Password;
  const { t } = useI18n();
  const { notification, createErrorModal } = useMessage();
  const { prefixCls } = useDesign('login');
  const userStore = useUserStore();

  const { getLoginState } = useLoginState();
  const { getFormRules } = useFormRules();
  const countDown = ref(0);

  const props = defineProps({
    loginTypeActive: String,
  });

  const { loginTypeActive } = toRefs(props);

  const formRef = ref();
  const loading = ref(false);

  const formData = reactive({
    account: '',
    password: '',
    mobile: '',
    code: '',
    loginType: 'NAME',
  });

  watch(
    () => loginTypeActive?.value,
    (val) => {
      // console.log('loginTypeActive', val);
      if (val === 'MOBILE') {
        formData.account = '';
        formData.password = '';
        formData.loginType = 'CODE';
      } else {
        formData.mobile = '';
        formData.code = '';
        formData.loginType = 'NAME';
      }
    },
    { immediate: true },
  );

  // 处理短信发送
  const handleGetCode = () => {
    console.log('handleGetCode');
    const mobileRegex = /^1[3-9]\d{9}$/;
    if (mobileRegex.test(formData.mobile)) {
      console.log('验证码发送成功');
      // 帮我显示倒计时
      console.log('倒计时');
      countDown.value = 60;
      const timer = setInterval(() => {
        countDown.value--;
        if (countDown.value === 0) {
          clearInterval(timer);
        }
      }, 1000);
    } else {
      notification.error({
        message: t('sys.login.mobileErrorTitle'),
        description: t('sys.login.mobileErrorMsg'),
        duration: 3,
      });
    }
  };

  const { validForm } = useFormValid(formRef);

  onKeyStroke('Enter', handleLogin);

  const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN);

  async function handleLogin() {
    const data = await validForm();
    if (!data) return;
    // 手机号校验
    const mobileRegex = /^1[3-9]\d{9}$/.test(data.account);
    if (mobileRegex && loginTypeActive?.value === 'ACCOUNT') {
      formData.loginType = 'MOBILE';
      formData.mobile = data.account;
      data.account = '';
    }

    if (!mobileRegex && loginTypeActive?.value === 'ACCOUNT') {
      formData.loginType = 'NAME';
      formData.account = data.account;
      formData.mobile = '';
    }

    let params: any = {
      password: data.password,
      loginName: data.account,
      loginType: formData.loginType,
      mobile: formData.mobile,
      code: formData.code,
    };

    // console.log('data', mobileRegex, params);

    try {
      loading.value = true;
      const userInfo = await userStore.login(params);
      if (userInfo) {
        notification.success({
          message: t('sys.login.loginSuccessTitle'),
          description: `${t('sys.login.loginSuccessDesc')}: ${userInfo.name}`,
          duration: 3,
        });
      }
    } catch (error) {
      createErrorModal({
        title: t('sys.api.errorTip'),
        content: (error as unknown as Error).message || t('sys.api.networkExceptionMsg'),
        getContainer: () => document.body.querySelector(`.${prefixCls}`) || document.body,
      });
    } finally {
      loading.value = false;
    }
  }
</script>
<style scoped lang="less">
  :deep(:where(.css-dev-only-do-not-override-ez24qu).ant-row) {
    display: block;
  }

  .ant-input-affix-wrapper,
  .ant-input {
    border: solid 1px #eaecef;
    font-size: 14px;
  }

  .ant-form-item {
    :deep(.ant-input-affix-wrapper > input.ant-input) {
      color: #000 !important;
    }
  }
</style>
