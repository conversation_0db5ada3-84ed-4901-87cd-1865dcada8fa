<template>
  <div :id="'map' + info.id" ref="containerRef" class="map"></div>
</template>

<script lang="ts">
  import { watch, defineComponent } from 'vue';
  import { useMap } from '@/hooks/web/useMap';

  export default defineComponent({
    props: {
      info: {
        type: Object,
        default: null,
      },
    },
    setup(props) {
      const initMap = () => {
        clearMap();
        const data = props.info;

        if (data) {
          let json = JSON.parse(data.fenceJson || '[]');
          switch (data.fenceType) {
            case 'CIRCULAR':
              const circle = addCircle(json[0], json[1], {
                fillColor: '#00b0ff',
                fillOpacity: 0.3,
                strokeWeight: 1,
                strokeColor: '#80d8ff',
              });
              setFitView([circle]);
              break;
            case 'POLYGON':
              if (json) {
                const polygon = addPolygon(json, {
                  fillColor: '#00b0ff',
                  strokeColor: '#80d8ff',
                  strokeOpacity: 1,
                  fillOpacity: 0.5,
                  strokeWeight: 1,
                  amapOptions: {
                    strokeDasharray: [5, 5],
                    path: json,
                  },
                  gmapOptions: {
                    paths: json,
                  },
                });
                console.log('polygons fence : ', [polygon], json);
                setFitView([polygon]);
              }
              break;
            default:
              data.districts.forEach((item) => {
                districtSearch(item.name, {
                  level: 'district',
                  subdistrict: 0,
                  extensions: 'all',
                }).then((result) => {
                  if (result.status === 'complete' && result.info === 'OK') {
                    let bounds = result.districtList[0].boundaries;
                    if (bounds) {
                      let polygons: any[] = [];
                      for (let i = 0; i < bounds.length; i++) {
                        polygons.push(
                          addPolygon(bounds[i], {
                            fillColor: '#00b0ff',
                            strokeColor: '#80d8ff',
                            strokeOpacity: 1,
                            fillOpacity: 0.5,
                            strokeWeight: 1,
                            amapOptions: {
                              strokeDasharray: [5, 5],
                            },
                          }),
                        );
                      }
                      setFitView(polygons);
                    }
                  }
                });
              });
              break;
          }
        }
      };

      const { containerRef, clearMap, setFitView, addPolygon, addCircle, districtSearch } = useMap(
        {
          zoom: 7,
        },
        initMap,
      );

      watch(
        () => props.info,
        (newVal) => {
          if (newVal) {
            initMap();
          }
        },
      );

      return {
        initMap,
        containerRef,
      };
    },
  });
</script>

<style>
  .map {
    width: 296px;
    height: 150px;
  }
</style>
