/**
 * Marker 事件管理功能使用示例
 */
import { useMap } from '@/hooks/web/useMap';

export function markerEventsExample() {
  // 初始化地图
  const { 
    initializeMap, 
    addMarker, 
    addMarkerEvent, 
    removeMarkerEvent 
  } = useMap({});

  async function demo() {
    // 初始化地图
    await initializeMap('map-container', {
      center: [116.397428, 39.90923],
      zoom: 13,
    });

    // 添加一个 marker
    const marker = await addMarker([116.397428, 39.90923], {
      title: '测试标记',
      icon: 'https://example.com/marker-icon.png',
    });

    if (marker) {
      // 添加点击事件
      addMarkerEvent(marker, 'click', (event) => {
        console.log('Marker 被点击了:', event);
        alert('Marker 被点击了!');
      });

      // 添加鼠标悬停事件
      addMarkerEvent(marker, 'mouseover', (event) => {
        console.log('鼠标悬停在 Marker 上:', event);
      });

      // 添加鼠标离开事件
      addMarkerEvent(marker, 'mouseout', (event) => {
        console.log('鼠标离开了 Marker:', event);
      });

      // 添加双击事件
      addMarkerEvent(marker, 'dblclick', (event) => {
        console.log('Marker 被双击了:', event);
        // 移除点击事件
        removeMarkerEvent(marker, 'click');
        alert('双击事件触发，点击事件已移除!');
      });

      // 添加右键点击事件
      addMarkerEvent(marker, 'rightclick', (event) => {
        console.log('Marker 被右键点击了:', event);
        // 移除所有鼠标事件
        removeMarkerEvent(marker, 'mouseover');
        removeMarkerEvent(marker, 'mouseout');
        alert('右键点击事件触发，鼠标悬停事件已移除!');
      });
    }
  }

  return {
    demo,
  };
}

/**
 * 使用方式：
 * 
 * 1. 在 Vue 组件中使用：
 * ```vue
 * <template>
 *   <div id="map-container" style="width: 100%; height: 400px;"></div>
 *   <button @click="runDemo">运行 Marker 事件示例</button>
 * </template>
 * 
 * <script setup>
 * import { markerEventsExample } from '@/examples/marker-events-example';
 * 
 * const { demo } = markerEventsExample();
 * 
 * const runDemo = () => {
 *   demo();
 * };
 * </script>
 * ```
 * 
 * 2. 支持的事件类型：
 * - 'click': 点击事件
 * - 'dblclick': 双击事件  
 * - 'rightclick': 右键点击事件
 * - 'mouseover': 鼠标悬停事件
 * - 'mouseout': 鼠标离开事件
 * - 'mousedown': 鼠标按下事件
 * - 'mouseup': 鼠标释放事件
 * - 'drag': 拖拽事件 (Google Maps)
 * - 'dragstart': 开始拖拽事件
 * - 'dragend': 结束拖拽事件
 * 
 * 3. 高德地图特有事件：
 * - 'dragging': 拖拽中事件
 * - 'moving': 移动中事件
 * - 'moveend': 移动结束事件
 * - 'movealong': 沿路径移动事件
 * - 'touchstart': 触摸开始事件
 * - 'touchmove': 触摸移动事件
 * - 'touchend': 触摸结束事件
 */
