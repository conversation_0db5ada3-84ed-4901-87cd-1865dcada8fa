<script lang="ts" setup name="role-list-form">
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { Select, SelectOption, Checkbox, InputSearch, Table, Input, Tree } from 'ant-design-vue';
  import { getsimList } from '@/api/data/simCard';
  import { getvehicleList } from '@/api/vehicle/vehlist';
  import { getOrgTreeOptions } from '@/api/passport/org';
  import { useMessage } from '@/hooks/web/useMessage';
  import { saleDevice } from '@/api/vehicle/vehlist';
  import { TeamOutlined } from '@ant-design/icons-vue';
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  import dayjs from 'dayjs';

  const checked = ref(false);
  const expireTime = ref('');
  const selectDevice: any = ref([]);
  const deviseList = ref([]);
  const dataSource: any = ref([]);
  const getList = async (deviceSn: any = '') => {
    let params = {
      pageIndex: 1,
      pageSize: 20,
      order: { property: 'id', direction: 'ASC' },
      criteria: {
        deviceSn,
      },
    };
    const res = await getvehicleList(params);
    deviseList.value = res.data || [];
  };
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const emit = defineEmits(['success', 'register']);
  //赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    dataSource.value = [data.record];
    selectDevice.value = [data.record.id];
    getList(data.record.deviceSn);
    getCardList();
    getOrgTree();
  });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function changeDevice(val: any, option: any) {
    dataSource.value = option;
  }
  const columns = ref([
    {
      title: '设备名称',
      dataIndex: 'name',
      slots: { customRender: 'name' },
      width: 150,
    },
    {
      title: 'SIM卡号',
      dataIndex: 'simNo',
      width: 150,
      slots: { customRender: 'simNo' },
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '设备类型',
      dataIndex: 'deviceModelName',
    },
    {
      title: '激活时间',
      dataIndex: 'activateTime',
    },
    {
      title: '所属机构',
      dataIndex: 'orgName',
    },
  ]);
  const simCardList = ref([]);
  const getCardList = async (simNo: any = '') => {
    const params = {
      pageIndex: 1,
      pageSize: 10,
      order: { property: 'id', direction: 'ASC' },
      criteria: {
        simNo,
      },
    };
    const res = await getsimList(params);
    simCardList.value = res.data || [];
  };
  //获取机构树
  const orgTreeData = ref([]);
  const getOrgTree = async () => {
    const res = await getOrgTreeOptions();
    orgTreeData.value = res || [];
  };
  const saleName = ref('');
  const saleId = ref('');
  const selectOrg = (keys,e) => {
    saleId.value = keys[0];
    saleName.value = e.node.name;
  };
  const { createMessage } = useMessage();
  async function handleSubmit() {
    try {
      if (!saleId.value) {
        createMessage.warning('请选择销售对象');
        return;
      }
      if (!dataSource.value.length) {
        createMessage.warning('请选择设备');
        return;
      }
      //到期时间
      let params = {
        editList: dataSource.value.map((v) => ({ id: v.id, name: v.name, simId: v.simId })),
        expireTime: expireTime.value,
        orgId: saleId.value,
        isChange: checked.value,
      };
      await saleDevice(params);
      closeModal();
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<template>
  <BasicModal
    width="1000px"
    @register="registerModal"
    v-bind="$attrs"
    title="销售"
    okText="销售"
    @ok="handleSubmit"
  >
    <div class="sale_box">
      <div class="device_list">
        <div class="total" style="
    padding-left: 5px;"
          >已选设备：<span>{{ dataSource.length }}</span></div
        >
        <div class="device">
          <div class="search">
            <Select
              v-model:value="selectDevice"
              @search="getList"
              mode="multiple"
              :filter-option="false"
              :options="deviseList"
              @change="changeDevice"
              :fieldNames="{ label: 'name', value: 'id' }"
              placeholder="请选择"
            />
          </div>
          <div class="list">
            <Table :columns="columns" :data-source="dataSource" :scroll="{ x: 800 }">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'name'">
                  <Input v-model:value="record.name" placeholder="请输入" />
                </template>
                <template v-if="column.key === 'simNo'">
                  <Select v-model:value="record.simId" placeholder="请选择" @change="getCardList">
                    <SelectOption :value="item.id" v-for="item in simCardList" :key="item.id">{{
                      item.simNo
                    }}</SelectOption>
                  </Select>
                </template>
              </template>
            </Table>
          </div>
        </div>
      </div>
      <div class="org_list">
        <div class="total"
          >销售给：<span>{{ saleName }}</span></div
        >
        <div class="org">
          <Tree
            :tree-data="orgTreeData"
            show-icon
            :fieldNames="{ title: 'name', key: 'uid' }"
            @select="selectOrg"
          >
            <template #icon="{ key, selected }">
              <TeamOutlined />
            </template>
          </Tree>
        </div>
      </div>
    </div>
    <div class="expireTime" style="padding-left: 10px;">
      客户到期时间：
      <Select v-model:value="expireTime">
        <SelectOption value="">请选择</SelectOption>
        <SelectOption value="1">一个月</SelectOption>
        <SelectOption value="2">两个月</SelectOption>
        <SelectOption value="3">三个月</SelectOption>
        <SelectOption value="6">半年</SelectOption>
        <SelectOption value="12">一年</SelectOption>
        <SelectOption value="24">两年</SelectOption>
        <SelectOption value="36">三年</SelectOption>
        <!-- <SelectOption value="8">不限制</SelectOption> -->
      </Select>
      <Checkbox v-model:checked="checked">不改变销售时间</Checkbox>
    </div>
  </BasicModal>
</template>
<style scoped lang="less">
  .sale_box {
    width: 100%;
    height: 425px;
    overflow: auto;
    display: flex;
    align-items: flex-start;
    gap: 20px;
    .device_list {
      display: flex;
      height: 400px;
      flex-direction: column;
      flex: 2;
      width: 200px;
      .device {
        flex: 1;
        width: calc(100% - 5px);
        background: #fff;
        box-shadow: 0 2px 4px 0 #bbb;
        margin:10px 0 0 5px;
        padding: 0 10px;
        .search {
          padding: 10px 0;
          box-sizing: border-box;
          width: 100%;
          .ant-select {
            width: 100%;
          }
        }
        .list {
          width: 100%;
        }
      }
    }
    .org_list {
      flex: 1;
      height: 400px;
      display: flex;
      flex-direction: column;
      .org {
        flex: 1;
        width:calc(100% - 5px);        
        background: #fff;
        box-shadow: 0 2px 4px 0 #bbb;
        margin: 10px 5px 0 0;
        padding: 12px;
        box-sizing: border-box;
        :deep(.ant-tree) {
          height: 100%;
          background: none !important;
        }
      }
    }
  }
  .expireTime {
    display: flex;
    align-items: center;
    gap: 5px;
    .ant-select {
      width: 100px;
    }
  }
  .total {
    span {
      color: red;
    }
  }
  :deep(.ant-table-wrapper .ant-table-container table > thead > tr:first-child > :first-child){
    border-start-start-radius: 2px !important;
  }
  :deep(.ant-table-wrapper .ant-table-container table > thead > tr:first-child > :last-child) {
  border-start-end-radius: 2px;
}
</style>
