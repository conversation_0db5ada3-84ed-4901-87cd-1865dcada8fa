<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增配置 </a-button>
      </template>
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer" @click="handleSettingVideo(record)">{{
          record.settingCount
        }}</Tag>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '是否确认删除',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <!-- <AddFormModal @register="registerFormModal" @success="handleSuccess" /> -->
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { Tag } from 'ant-design-vue';

  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { removevideoStreamSetting, getvideoStreamSettingList } from '@/api/data/supplier';
  import { useMessage } from '@/hooks/web/useMessage';

  import { useModal } from '@/components/Modal';
  import AddFormModal from './AddFormModal.vue';

  import { useDrawer } from '@/components/Drawer';

  import { columns, searchFormSchema } from './role.data';
  import { useRoute } from 'vue-router';
  import { useDynamicModal } from '@/hooks/component/useModal';

  export default defineComponent({
    name: 'role-list',
    components: { BasicTable, TableAction, Tag },
    setup() {
      const { params } = useRoute();
      const [registerTable, { reload }] = useTable({
        title: '视频配置列表',
        api: getvideoStreamSettingList,
        columns,
        formConfig: {
          labelAlign: 'left',
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        beforeFetch: async (searchInfo: any) => {
          searchInfo.criteria.vendorId = params.vendorId;
          return searchInfo;
          console.log('before fetch : ', searchInfo);
        },
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
          slot: 'action',
          fixed: undefined,
        },
      });

      const [registerFormModal, { openModal: openFormModal }] = useModal();

      const [registerSettingVideo, { openModal: openSettingVideoModal }] = useModal();

      const [registerDrawer, { openDrawer }] = useDrawer();

      const modal = useDynamicModal();

      function handleCreate() {
        modal.open(AddFormModal, {
          props: {
            title: '新增',
          },
          onDone: reload,
          data: {
            modalType: 'add',
            vendorId: params.vendorId,
          },
        });
      }

      function handleEdit(record: Recordable) {
        console.log(record);
        modal.open(AddFormModal, {
          props: {
            title: '编辑',
          },
          onDone: reload,
          data: {
            ...record,
            vendorId: params.vendorId,
            modalType: 'edit',
          },
        });
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        const { createMessage } = useMessage();
        removevideoStreamSetting(record?.id).then((res) => {
          createMessage.success('删除成功');
          reload();
        });
      }

      function handleSettingVideo(record: Recordable) {
        openSettingVideoModal(true, {
          record,
        });
      }

      function handleSuccess() {
        reload();
      }

      return {
        registerTable,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        registerFormModal,
        openSettingVideoModal,
        handleSettingVideo,
        registerSettingVideo,
        registerDrawer,
      };
    },
  });
</script>
