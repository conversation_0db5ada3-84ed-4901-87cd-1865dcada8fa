import { defHttp } from '@/utils/http/axios';
import { MicroServiceEnum } from '../model/baseModel';

/**
 * @description: 获取设备总览
 */
export function getDeviceOverview(data) {
  return defHttp.put({
    url: `/deviceStatistics/overView`,
    data,
  });
}

/**
 * @description: 获取设备下级库存
 */
export function getDeviceStock(data) {
  return defHttp.put({
    url: `/deviceStatistics/stock`,
    data,
  });
}

/**
 * @description: 获取设备增长激活
 */
export function getDeviceGrowthActive(data) {
  return defHttp.put({
    url: `/deviceStatistics/addActive`,
    data,
  });
}
