@import 'transition/index.less';
@import 'var/index.less';
@import 'public.less';
@import 'ant/index.less';
@import './theme.less';
@import './entry.css';
@import './dark.less';

input:-webkit-autofill {
  box-shadow: 0 0 0 1000px white inset !important;
}

:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s !important;
}

html {
  overflow: hidden;
  text-size-adjust: 100%;
}

html,
body {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: visible;
  overflow-x: hidden;

  &.color-weak {
    filter: invert(80%);
  }

  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }
}

a:focus,
a:active,
button,
div,
svg,
span {
  outline: none;
}

// 保持 和 windi 一样的全局样式，减少升级带来的影响
ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.vben-menu-dark.vben-menu-vertical .vben-simple-menu__parent {
  margin: 0 0 10px;
  border-radius: 3px;
  background: none !important;
}

.vben-menu-dark.vben-menu-vertical .vben-simple-menu__parent > .vben-menu-submenu-title {
  border-radius: 3px 3px 0 0;
  background: none !important;
}

.vben-menu-vertical .vben-menu-submenu .vben-menu-item {
  margin: 0;
  border-radius: 0;
}

.vben-menu-dark.vben-menu-vertical .vben-menu-item,
.vben-menu-dark.vben-menu-vertical .vben-menu-submenu-title {
  background: none !important;
}
.vben-menu-dark.vben-menu-vertical .vben-menu-item{
  color: #000 !important;
}
.vben-menu-dark.vben-menu-vertical .vben-simple-menu__children,
.vben-menu-dark.vben-menu-popup .vben-simple-menu__children {
  background: none !important;
}

.css-dev-only-do-not-override-zu87i.ant-layout.ant-layout-has-sider > .ant-layout,
.css-dev-only-do-not-override-zu87i.ant-layout.ant-layout-has-sider > .ant-layout-content {
  margin-left: -1px;
}
.vben-layout-content{
  height: 100px;
}
.vben-layout-sideBar.ant-layout-sider-dark {
  background: #0421bc !important;
}

.vben-layout-header--light {
  border: none !important;
}

.vben-multiple-tabs {
  height: 40px;
  border: none !important;
  position: relative;
  top:50px;
  
}

.vben-basic-table-form-container .ant-form {
  margin-bottom: 0 !important;
  background: none !important;
  padding: 10px 0 !important;
}

.vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav {
  height: 40px !important;
  background: #EAF2FF !important;
}

.ant-layout .vben-layout-header {
  height: 50px !important;
}

.css-dev-only-do-not-override-zu87i.ant-tabs-card.ant-tabs-small.ant-tabs-top
  > .ant-tabs-nav
  .ant-tabs-tab {
  border-radius: 3px;
}

.vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab {
  height: 28px !important;
  margin-top: 3px !important;
}

.vben-basic-table-form-container {
  padding:15px 11px !important;
}
.vben-basic-table-form-container {
  // padding: 20px !important;
  // padding-bottom: 0px !important;
  background: #F6F7F9 !important;
  display: flex;
  flex-direction: column;
}
.vben-basic-table-form-container .ant-table-wrapper {
  flex: 1;
}
.vben-layout-menu-logo {
  height: 50px !important;
}

.css-dev-only-do-not-override-zu87i.ant-table-wrapper
  .ant-table
  .ant-table-title
  + .ant-table-container {
  padding: 0 14px;
}

.vben-menu-vertical .vben-menu-item,
.vben-menu-vertical .vben-menu-submenu-title {
  height: 40px !important;
}

.vben-basic-title {
  // margin-left: 15px;
  // padding: 0 5px 2px;
  // border-top: solid 2px #273352;
  // border-right: solid 5px #273352;
  // border-left: solid 5px #273352;
  border-radius: 15px;
  font-size: 14px !important;
  font-weight: 700 !important;
  text-align: center;
  padding:7px 0 0 5px;
}

/* .vben-menu-dark.vben-menu-vertical .vben-simple-menu__children, .vben-menu-dark.vben-menu-popup .vben-simple-menu__children{
  text-indent: 16px;
} */

.vben-multiple-tabs .ant-tabs-tab:not(.ant-tabs-tab-active) .anticon-close {
  color: #000 !important;
}

.vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab .ant-tabs-tab-remove {
  opacity: initial !important;
}

.css-dev-only-do-not-override-1yiuavr.ant-tree
  .ant-tree-node-content-wrapper.ant-tree-node-selected,
.css-dev-only-do-not-override-1yiuavr.ant-tree .ant-tree-checkbox + span.ant-tree-node-selected {
  padding: 5px 8px;
  background-color: #eee !important;
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected,
.ant-tree .ant-tree-checkbox + span.ant-tree-node-selected {
  background-color: #E5ECF7 !important;
  color: #000;
  border-radius: 2px;
}

.css-dev-only-do-not-override-1yiuavr.ant-table-wrapper .ant-table-thead > tr > th,
.css-dev-only-do-not-override-1yiuavr.ant-table-wrapper .ant-table-tbody > tr > td,
.css-dev-only-do-not-override-1yiuavr.ant-table-wrapper tfoot > tr > th,
.css-dev-only-do-not-override-1yiuavr.ant-table-wrapper tfoot > tr > td {
  padding: 8px 10px;
}

.css-dev-only-do-not-override-1yiuavr.ant-tabs-top > .ant-tabs-nav {
  margin: 0 0 4px;
}

.vben-app-logo {
  margin-bottom: 10px;
  background: #fff;
}

.ant-layout .vben-layout-header--light {
  background: #2b0e05 !important;
  color: #000 !important;
  box-shadow: 0 0 10px #CDD8ED;
  position: absolute;
  width: 100%;
  z-index: 99;
}
.vben-basic-modal-wrap .ant-modal .ant-modal-body > .scrollbar{
  padding-top: 6px !important;
}