<template>
  <div>
    <BasicTable @register="registerTable">
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>
      <TableAction
        :actions="[
          // {
          //   icon: 'flowbite:link-break-outline',
          //   color: 'error',
          //   popConfirm: {
          //     title: '是否确认解绑该设备',
          //     confirm: handleDelete.bind(null, record),
          //   },
          // },
        ]"
      />
    </BasicTable>

    <AlarmHistory @register="registerModal" />
  </div>
</template>
<script lang="tsx" setup>
  import { onMounted, ref } from 'vue';
  import { Tag } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getTrip } from '@/api/vehicle/vehlist';
  import { useModal } from '@/components/Modal';
  import { getOrgTreeOptions } from '@/api/passport/org';
  import { getDeviceList } from '@/api/vehicle/vehlist';
  import { useAMap } from '@/hooks/web/useAMap';

  const { getAddress } = useAMap({});
  const [registerModal, { openModal }] = useModal();
  const deviceOptions = ref([]);

  const orgBranch = ref('');


  async function getDevice() {
    deviceOptions.value = [];
    const params = {
      pageIndex: 1,
      pageSize: 10000,
      order: { property: 'id', direction: 'ASC' },
      criteria: {
        orgBranch: orgBranch.value,
      },
    };
    const { data } = await getDeviceList(params);
    deviceOptions.value = data;
  }

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '设备型号',
      dataIndex: 'deviceName',
    },
    {
      title: '总里程(km)',
      dataIndex: 'mileage',
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
    },
    {
      title: '起点',
      dataIndex: 'startAddress',
      width: 250,
    },
    {
      title: '终点',
      dataIndex: 'endAddress',
      width: 250,
    },
  ];

  const [registerTable, { reload, getDataSource }] = useTable({
    api: getTrip,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 90,
      schemas: [
        {
          field: 'criteria.dateRange',
          component: 'RangePicker',
          label: '时间范围',
          colProps: { span: 6 },
          componentProps: {
            format: 'YYYY-MM-DD HH:mm:ss',
            showTime: true,
            presets: [
              { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
              {
                label: '昨天',
                value: [
                  dayjs().subtract(1, 'day').startOf('day'),
                  dayjs().subtract(1, 'day').endOf('day'),
                ],
              },
              {
                label: '本周',
                value: [dayjs().startOf('week'), dayjs().endOf('week')],
              },
              {
                label: '本月',
                value: [dayjs().startOf('month'), dayjs().endOf('month')],
              },
              {
                label: '上月',
                value: [
                  dayjs().subtract(1, 'month').startOf('month'),
                  dayjs().subtract(1, 'month').endOf('month'),
                ],
              },
            ],
          },
        },
        {
          field: 'criteria.deviceId',
          component: 'ApiSelect',
          label: '设备',
          colProps: { span: 6 },
          componentProps: {
            options: deviceOptions,
            fieldNames: { label: 'name', value: 'id' },
          },
        },
      ],
    },
    showTableSetting: true,
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    maxHeight: 500,
    rowKey: 'id',
    beforeFetch: ({ criteria, order }) => {
      if (criteria.dateRange && criteria.dateRange.length) {
        criteria.dateTimeRange = {
          begin: criteria.dateRange[0],
          end: criteria.dateRange[1],
        };
        delete criteria.dateRange;
      }
      order.property = 'id';
    },
    afterFetch: async (data) => {
      const addressPromises = data.map(async (item) => {
        let startAddress = item.startLng && item.startLat ? await getAddress([item.startLng, item.startLat]) : '';
        let endAddress = item.endLng && item.endLat ? await getAddress([item.endLng, item.endLat]) : '';
        return {
          ...item,
          startAddress: startAddress,
          endAddress: endAddress,
        };
      });

      return Promise.all(addressPromises);
    }
  });

  onMounted(async () => {
    await getDevice();
  });
</script>
