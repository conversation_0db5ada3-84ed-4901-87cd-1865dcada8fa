/**
 * 地图对象事件触发功能使用示例
 * 替代原有的 AMap.Event.trigger 用法
 */
import { useMap } from '@/hooks/web/useMap';

export function triggerEventExample() {
  // 初始化地图
  const { 
    initializeMap, 
    addMarker,
    triggerEvent,
    addMarkerEvent,
    getMapInstance,
    provider
  } = useMap({
    center: [116.397428, 39.90923],
    zoom: 13,
  });

  let testMarker: any = null;

  async function demo() {
    // 初始化地图
    await initializeMap('map-container', {
      center: [116.397428, 39.90923],
      zoom: 13,
    });

    console.log('当前地图提供商:', provider.value);

    // 添加一个测试 marker
    testMarker = addMarker([116.397428, 39.90923], {
      title: '测试标记',
      extData: { id: 'test-marker', name: '天安门' }
    });

    if (testMarker) {
      // 为 marker 添加点击事件监听器
      addMarkerEvent(testMarker, 'click', (event) => {
        console.log('Marker 被点击了:', {
          type: event.type,
          position: event.lnglat,
          extData: event.extData,
          timestamp: event.timestamp
        });
        alert(`Marker 被点击了!\n位置: [${event.lnglat[0]}, ${event.lnglat[1]}]\n数据: ${JSON.stringify(event.extData)}`);
      });

      // 为 marker 添加鼠标悬停事件
      addMarkerEvent(testMarker, 'mouseover', (event) => {
        console.log('鼠标悬停在 Marker 上:', event);
      });

      // 为 marker 添加双击事件
      addMarkerEvent(testMarker, 'dblclick', (event) => {
        console.log('Marker 被双击了:', event);
        alert('双击事件触发!');
      });

      console.log('测试 marker 已添加，事件监听器已设置');
    }
  }

  /**
   * 手动触发 marker 点击事件的示例
   * 替代原有的 AMap.Event.trigger(marker, 'click', data) 用法
   */
  function triggerMarkerClick(customData?: any) {
    if (!testMarker) {
      console.error('测试 marker 不存在');
      return false;
    }

    console.log('手动触发 marker 点击事件');
    
    // 使用统一的 triggerEvent 方法
    const result = triggerEvent(testMarker, 'click', customData);
    
    if (result) {
      console.log('事件触发成功');
    } else {
      console.error('事件触发失败');
    }
    
    return result;
  }

  /**
   * 手动触发 marker 鼠标悬停事件
   */
  function triggerMarkerMouseover() {
    if (!testMarker) {
      console.error('测试 marker 不存在');
      return false;
    }

    console.log('手动触发 marker 鼠标悬停事件');
    return triggerEvent(testMarker, 'mouseover');
  }

  /**
   * 手动触发 marker 双击事件
   */
  function triggerMarkerDoubleClick() {
    if (!testMarker) {
      console.error('测试 marker 不存在');
      return false;
    }

    console.log('手动触发 marker 双击事件');
    return triggerEvent(testMarker, 'dblclick');
  }

  /**
   * 手动触发地图点击事件
   */
  function triggerMapClick(position?: [number, number]) {
    const mapInstance = getMapInstance();
    if (!mapInstance) {
      console.error('地图实例不存在');
      return false;
    }

    console.log('手动触发地图点击事件');
    
    const eventData = position ? {
      lnglat: position,
      pixel: [100, 100] // 模拟像素坐标
    } : undefined;

    return triggerEvent(mapInstance, 'click', eventData);
  }

  /**
   * 批量触发事件的示例
   */
  function triggerMultipleEvents() {
    if (!testMarker) {
      console.error('测试 marker 不存在');
      return;
    }

    console.log('开始批量触发事件...');

    // 依次触发不同事件
    setTimeout(() => {
      console.log('1. 触发鼠标悬停事件');
      triggerEvent(testMarker, 'mouseover');
    }, 1000);

    setTimeout(() => {
      console.log('2. 触发点击事件（带自定义数据）');
      triggerEvent(testMarker, 'click', { 
        source: 'programmatic',
        timestamp: Date.now(),
        customData: '这是程序化触发的点击事件'
      });
    }, 2000);

    setTimeout(() => {
      console.log('3. 触发双击事件');
      triggerEvent(testMarker, 'dblclick');
    }, 3000);
  }

  /**
   * 模拟业务场景：选中车辆时触发 marker 点击事件
   * 这是替代原有 dashboard/index.vue 中 AMap.Event.trigger 用法的示例
   */
  function selectVehicle(vehicleData: any) {
    if (!testMarker) {
      console.error('车辆 marker 不存在');
      return false;
    }

    console.log('选中车辆，触发 marker 点击事件:', vehicleData);

    // 使用统一的 triggerEvent 方法替代 AMap.Event.trigger
    return triggerEvent(testMarker, 'click', vehicleData);
  }

  return {
    demo,
    triggerMarkerClick,
    triggerMarkerMouseover,
    triggerMarkerDoubleClick,
    triggerMapClick,
    triggerMultipleEvents,
    selectVehicle,
    triggerEvent, // 导出通用方法
    getTestMarker: () => testMarker,
  };
}

/**
 * 使用方式：
 * 
 * 1. 在 Vue 组件中使用：
 * ```vue
 * <template>
 *   <div>
 *     <div id="map-container" style="width: 100%; height: 400px;"></div>
 *     <div class="controls">
 *       <button @click="runDemo">初始化演示</button>
 *       <button @click="triggerClick">触发点击</button>
 *       <button @click="triggerHover">触发悬停</button>
 *       <button @click="triggerDoubleClick">触发双击</button>
 *       <button @click="triggerMapClick">触发地图点击</button>
 *       <button @click="triggerMultiple">批量触发</button>
 *     </div>
 *   </div>
 * </template>
 * 
 * <script setup>
 * import { triggerEventExample } from '@/examples/trigger-event-example';
 * 
 * const {
 *   demo,
 *   triggerMarkerClick,
 *   triggerMarkerMouseover,
 *   triggerMarkerDoubleClick,
 *   triggerMapClick,
 *   triggerMultipleEvents,
 *   selectVehicle
 * } = triggerEventExample();
 * 
 * const runDemo = () => demo();
 * const triggerClick = () => triggerMarkerClick({ source: 'button' });
 * const triggerHover = () => triggerMarkerMouseover();
 * const triggerDoubleClick = () => triggerMarkerDoubleClick();
 * const triggerMultiple = () => triggerMultipleEvents();
 * 
 * // 模拟业务场景
 * const handleVehicleSelect = (vehicleData) => {
 *   selectVehicle(vehicleData);
 * };
 * </script>
 * ```
 * 
 * 2. 替代原有的 AMap.Event.trigger 用法：
 * 
 * **原有用法（仅支持高德地图）：**
 * ```javascript
 * // 在 dashboard/index.vue 中
 * AMap.Event.trigger(markMap[data.id], 'click', data);
 * ```
 * 
 * **新的统一用法（支持高德地图和 Google Maps）：**
 * ```javascript
 * // 使用统一的 triggerEvent 方法
 * const { triggerEvent } = useMap({});
 * triggerEvent(markMap[data.id], 'click', data);
 * ```
 * 
 * 3. 支持的事件类型：
 * - **Marker 事件**: 'click', 'dblclick', 'rightclick', 'mouseover', 'mouseout', 'mousedown', 'mouseup', 'dragstart', 'dragend'
 * - **地图事件**: 'click', 'dblclick', 'rightclick', 'mousemove', 'zoomchange', 'moveend' 等
 * - **其他对象事件**: Polyline, Polygon, Circle 等的相关事件
 * 
 * 4. 特性：
 * - 🎯 **统一接口**: 自动适配高德地图和 Google Maps
 * - 🔄 **向后兼容**: 完全替代 AMap.Event.trigger 用法
 * - 📦 **类型安全**: TypeScript 支持，提供完整的类型检查
 * - 🛡️ **错误处理**: 内置错误处理和日志记录
 * - 📱 **跨平台**: 支持所有地图对象的事件触发
 * 
 * 5. 迁移指南：
 * ```javascript
 * // 旧代码
 * AMap.Event.trigger(marker, 'click', eventData);
 * 
 * // 新代码
 * const { triggerEvent } = useMap({});
 * triggerEvent(marker, 'click', eventData);
 * ```
 */
