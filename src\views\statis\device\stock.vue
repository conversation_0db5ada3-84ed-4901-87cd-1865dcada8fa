<script lang="ts" setup>
  import { ref, watch, onMounted, toRefs } from 'vue';
  import ECharts from '@/components/ECharts/src/ECharts.vue';
  import { getDeviceStock } from '@/api/statis/deviceovw';

  const props = defineProps({
    criteria: Object,
  });

  const { criteria }: any = toRefs(props);
  let options = ref({
    title: {
      text: '下级客户库存统计',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: ['总数', '库存'],
      top: 30,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: ['统计'],
    },
    series: [
      {
        name: '总数',
        type: 'bar',
        data: [],
        itemStyle: {
          color: '#5470C6',
        },
        label: {
          show: true,
          position: 'right',
        },
      },
      {
        name: '库存',
        type: 'bar',
        data: [],
        itemStyle: {
          color: '#91CC75',
        },
        label: {
          show: true,
          position: 'right',
        },
      },
    ],
  });

  watch(
    criteria,
    (newValue, oldValue) => {
      const params = {
        orgId: newValue.orgId,
      };
      handleSearch(params);
    },
    {
      deep: true,
    },
  );

  async function handleSearch(params) {
    const res = await getDeviceStock(params);
    options.value.series[0].data = [res.all];
    options.value.series[1].data = [res.unSales];
  }

  onMounted(() => {
    handleSearch({});
  });
</script>

<template>
  <div class="w-full h-80 flex">
    <ECharts :options="options" />
  </div>
</template>

<style scoped>
  .boxleft {
    margin: 0 0 25px 30px;
    padding: 35px 30px 0;
    float: left;
    border-radius: 5px;
    box-shadow: 0 0 10px #ddd;
  }
</style>
