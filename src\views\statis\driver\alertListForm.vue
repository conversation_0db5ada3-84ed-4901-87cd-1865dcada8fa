<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-27 14:29:54
 * @LastEditors  : chen
 * @LastEditTime : 2024-10-14 14:32:51
 * @FilePath     : \special-front\src\views\statis\driver\alertListForm.vue
 * Copyright (C) 2024 chen. All rights reserved.
-->
<script lang="ts" setup name="role-list-form">
  import { ref } from 'vue';
  import { BasicTable, useTable } from '@/components/Table';
  import { useModalInner, BasicModal } from '@/components/Modal';
  import { Tag } from 'ant-design-vue';
  import { getAlarmHistory } from '@/api/vehicle/vehlist';
  import { columns, searchFormSchema } from './vehicle.data';
  import dayjs from 'dayjs';
  import { useGeoCoder } from '@/hooks/web/useMap';

  const { getAddress, manualInitializeGeocoder, isGeocoderReady } = useGeoCoder({});
  function getLocations(list, num) {
    if (num >= list.length) {
      return Promise.resolve(list);
    }
    if (list[num].gcj02Lat && list[num].gcj02Lng) {
      return new Promise((resolve) => {
        getAddress([list[num].gcj02Lng, list[num].gcj02Lat]).then((res) => {
          list[num].gpsPosition = res;
          resolve(getLocations(list, num + 1));
        });
      });
    } else {
      return getLocations(list, num + 1);
    }
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const emit = defineEmits(['success']);

  const driverDetail: any = ref(null);
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });
    driverDetail.value = data?.record;
    if (!isGeocoderReady()) {
      await manualInitializeGeocoder();
    }
    reload();
  });

  const [registerTable, { reload }] = useTable({
    title: '报警列表',
    api: (params) => {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        try {
          let res = await getAlarmHistory(params);
          res.data = await getLocations(res.data, 0);
          resolve(res);
        } catch (error) {
          reject(error);
        }
      });
    },
    columns,
    formConfig: {
      labelAlign: 'left',
      schemas: searchFormSchema,
    },
    maxHeight: 500,
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: false,
    beforeFetch: async (searchInfo: any) => {
      searchInfo.criteria.driverId = driverDetail.value.driverNo;
      if (searchInfo.criteria.range && searchInfo.criteria.range.length) {
        searchInfo.criteria.begin = dayjs(searchInfo.criteria.range[0]).format(
          'YYYY-MM-DD 00:00:00',
        );
        searchInfo.criteria.end = dayjs(searchInfo.criteria.range[1]).format('YYYY-MM-DD 23:59:59');
      } else {
        searchInfo.criteria.begin = dayjs().subtract(7, 'day').format('YYYY-MM-DD 00:00:00');
        searchInfo.criteria.end = dayjs().format('YYYY-MM-DD 23:59:59');
      }
    },
  });
  async function handleSubmit() {
    closeModal();
  }
</script>

<template>
  <BasicModal
    width="1200px"
    v-bind="$attrs"
    title="报警信息"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <BasicTable @register="registerTable">
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'range'">
          <span v-if="record.beginTime && record.endTime">{{
            record.beginTime + '~' + record.endTime
          }}</span>
        </template>
      </template>
    </BasicTable>
  </BasicModal>
</template>
