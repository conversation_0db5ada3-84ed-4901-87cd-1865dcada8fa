<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, onMounted, nextTick } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { FormSchema } from '@/components/Table';
  import { useMessage } from '@/hooks/web/useMessage';
  import { addFence, editFence } from '@/api/vehicle/fence';
  import { getDeviceList } from '@/api/vehicle/vehlist';
  import { getDistrict } from '@/api/district/district';
  import { getOrgTreeOptions } from '@/api/passport/org';
  // 移除了 useAMap
  import { Transfer } from 'ant-design-vue';
  import { debounce } from 'lodash-es';
  import gisOnlinePng from '@/assets/images/online.png';

  const type = ref('AREA');
  let mouseTool: any = null;
  let overlays: any = [];
  let fenceJson = [];
  const instance = ref<any>();
  const city = ref('');
  const carList = ref<any[]>([]);
  let deviceDetail: any = {};
  const { createMessage } = useMessage();

  let myMap: any = null;

  const mapContainerRef = ref<HTMLElement | null>(null);

  onMounted(() => {
    getDeviceList({
      pageIndex: 1,
      pageSize: 10000,
      order: { property: 'id', direction: 'ASC' },
      criteria: {},
    }).then((res) => {
      carList.value = res.data.map((v) => ({ ...v, key: v.id }));
    });
  });

  const formSchema: FormSchema[] = [
    {
      field: 'name',
      label: '围栏名称',
      colProps: { span: 12 },
      component: 'Input',
    },
    {
      field: 'orgId',
      label: '所属机构',
      component: 'ApiTreeSelect',
      colProps: { span: 12 },
      componentProps: {
        api: getOrgTreeOptions,
        labelField: 'name',
        valueField: 'uid',
        getPopupContainer: () => document.body,
      },
    },
    {
      field: 'alertEvent',
      label: '触发方式',
      colProps: { span: 12 },
      component: 'Select',
      defaultValue: 'OUT',
      componentProps: {
        options: [
          { label: '驶入', value: 'IN' },
          { label: '驶出', value: 'OUT' },
        ],
      },
    },
    {
      field: 'status',
      label: '启用状态',
      colProps: { span: 12 },
      component: 'Switch',
      defaultValue: 'NO',
      componentProps: {
        checkedChildren: '启用',
        unCheckedChildren: '禁用',
        checkedValue: 'YES',
        unCheckedValue: 'NO',
      },
    },
    {
      field: 'fenceType',
      label: '围栏类型',
      colProps: { span: 12 },
      component: 'Select',
      defaultValue: 'AREA',
      componentProps: {
        options: [
          { label: '行政区域', value: 'AREA' },
          { label: '圆形', value: 'CIRCULAR' },
          { label: '多边形', value: 'POLYGON' },
        ],
        onChange: async (e, v) => {
          type.value = e;
          overlays = [];
          await draw(e);
        },
      },
    },
    {
      field: 'districtCodes',
      label: '策略区域',
      component: 'ApiTreeSelect',
      colProps: { span: 12 },
      componentProps: {
        api: getDistrict,
        labelField: 'name',
        valueField: 'code',
        filterTreeNode: (inputValue: string, treeNode: any) => {
          return treeNode.name.indexOf(inputValue) !== -1;
        },
        multiple: true,
        onChange: debounce((e) => {
          if (type.value !== 'AREA') return;
          drawDistricts(e);
        }, 500),
        getPopupContainer: () => document.body,
      },
      ifShow: () => type.value == 'AREA',
    },
  ];

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref<number | undefined>(undefined);

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    type.value = 'AREA';
    city.value = '';
    deviceDetail = data.record;
    overlays = [];
    fenceJson = [];
    instance.value = undefined;
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    // 销毁地图实例
    if (myMap) {
      myMap.destroy();
      myMap = null;
    }
    // 回显数据
    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      instance.value = data.record;
      type.value = data.record.fenceType;
      setFieldsValue({
        ...data.record,
        districtCodes: data.record.districtCodes ? data.record.districtCodes.split(',') : [],
      });
      fenceJson = JSON.parse(data.record.fenceJson || '[]');
    }
    await nextTick();

    // 初始化地图
    if (mapContainerRef.value) {
      myMap = new AMap.Map(mapContainerRef.value, {
        center: [data.record.lng, data.record.lat],
        zoom: 14,
        mapStyle: 'amap://styles/normal',
      });
      // 地图加载完成后，调用 initMap 绘制图形和加载插件
      myMap.on('complete', () => {
        initMap();
      });
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增围栏' : '编辑围栏'));

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      let params: any = {
        ...values,
        districtCodes: values.districtCodes ? values.districtCodes.join(',') : '',
        deviceIds: [deviceDetail.id],
      };

      if (params.fenceType === 'POLYGON') {
        params.fenceJson = JSON.stringify(overlays.map((v: any) => (v.getPath ? v.getPath() : v)));
      }
      if (params.fenceType === 'CIRCULAR') {
        let { lng, lat } = overlays.map((v: any) => v.getCenter())[0];
        let radius = overlays.map((v: any) => v.getRadius())[0];
        params.fenceJson = JSON.stringify([[lng, lat], radius]);
      }

      if (unref(isUpdate)) {
        await editFence(rowId.value, params);
      } else {
        await addFence(params);
      }
      closeModal();
      emit('success', {
        isUpdate: unref(isUpdate),
        values: { ...values, id: rowId.value },
      });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  function initMap() {
    console.log('initMap', { fenceJson, type: type.value });
    if (!myMap) return;

    myMap.clearMap();
    const marker = new AMap.Marker({
      position: [deviceDetail.lng, deviceDetail.lat], // 基点位置
      icon: new AMap.Icon({
        image: gisOnlinePng,
        size: new AMap.Size(50, 50), // 图标大小
        imageSize: new AMap.Size(50, 50),
      }),
      offset: new AMap.Pixel(0, -5), // 相对于基点的偏移位置
    });
    marker.setMap(myMap);
    // 根据数据显示已保存的围栏
    if (fenceJson.length && type.value === 'POLYGON') {
      fenceJson = fenceJson.filter((v) => Array.isArray(v));
      console.log('fenceJson', fenceJson);
      let polygon = new AMap.Polygon({
        path: fenceJson,
        fillColor: '#00b0ff',
        strokeColor: '#80d8ff',
        strokeOpacity: 1,
        fillOpacity: 0.5,
        strokeWeight: 1,
        strokeDasharray: [5, 5],
      });
      myMap.add(polygon);
      myMap.setFitView([polygon]);
      overlays.push(polygon);
      fenceJson = [];
    } else if (fenceJson.length && type.value === 'CIRCULAR') {
      let circleRadius = fenceJson.filter((v) => typeof v === 'number');
      let circle = new AMap.Circle({
        center: fenceJson[0],
        radius: circleRadius[0] || 0,
        fillColor: '#00b0ff',
        strokeColor: '#80d8ff',
      });
      myMap.add(circle);
      myMap.setFitView([circle]);
      overlays.push(circle);
      fenceJson = [];
    } else if (type.value === 'AREA' && instance.value?.districtCodes) {
      drawDistricts(instance.value.districtCodes);
    }

    // 加载鼠标工具插件
    AMap.plugin(['AMap.MouseTool'], function () {
      mouseTool = new AMap.MouseTool(myMap);
    });
    mouseTool.on('draw', function (event: any) {
      // 除旧的图形
      myMap.remove(overlays);
      // 保存新的图形
      overlays = [event.obj];
    });
  }

  // 绘制行政区
  function drawDistricts(districtCodes: any) {
    if (!myMap) return;
    myMap.clearMap();
    if (!districtCodes) return;
    let codesArray = Array.isArray(districtCodes) ? districtCodes : districtCodes.split(',');

    if (codesArray.length) {
      AMap.plugin('AMap.DistrictSearch', function () {
        let districtSearch = new AMap.DistrictSearch({
          level: 'district',
          subdistrict: 0,
          extensions: 'all',
        });
        codesArray.forEach((code) => {
          districtSearch.search(code, function (status, result) {
            if (status === 'complete' && result.info === 'OK') {
              let bounds = result.districtList[0].boundaries;
              if (bounds) {
                let polygons = [];
                for (let i = 0; i < bounds.length; i++) {
                  polygons.push(
                    new AMap.Polygon({
                      map: myMap,
                      path: bounds[i],
                      strokeWeight: 2,
                      strokeOpacity: 0.5,
                      strokeColor: '#527fff',
                      fillColor: '#2b866d',
                      fillOpacity: 0.35,
                    }),
                  );
                }
                myMap.setFitView(polygons);
              }
            }
          });
        });
      });
    }
  }

  // 激活鼠标工具进行绘制
  function draw(type: string) {
    if (!mouseTool) {
      console.error('地图工具尚未初始化！');
      return;
    }
    mouseTool.close(true);//关闭，并清除覆盖物
    // 开始绘制前，清空地图上已有的可编辑图形
    // myMap.remove(overlays);
    overlays = [];
    switch (type) {
      case 'CIRCULAR':
        mouseTool?.circle({
          fillColor: '#00b0ff',
          strokeColor: '#80d8ff',
        });
        break;
      case 'POLYGON':
        console.log(mouseTool)
        mouseTool?.polygon({
          fillColor: '#00b0ff',
          strokeColor: '#80d8ff',
        });
        break;
      default:
        break;
    }
  }

  function setCity() {
    if (myMap && city.value) {
      myMap.setCity(city.value);
    }
  }
</script>

<template>
  <BasicModal
    width="50%"
    v-bind="$attrs"
    :title="getTitle"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />

    <div class="map_box">
      <div ref="mapContainerRef" class="mapRef"></div>

      <a-input
        v-model:value="city"
        v-if="type !== 'AREA'"
        placeholder="城市名"
        class="city_input"
        @press-enter="setCity"
      />

      <a-button type="primary" class="clear_btn" @click="draw(type)">重新绘制</a-button>
    </div>

    <!-- <div class="transForm">
      <div class="label">关联设备：</div>
      <Transfer
        v-if="carList.length"
        v-model:target-keys="targetKeys"
        v-model:selected-keys="selectedKeys"
        :data-source="carList"
        :titles="['设备列表', '已选设备']"
        :render="(item) => item.name"
      />
    </div> -->
  </BasicModal>
</template>
<style scoped lang="less">
  .mapRef {
    width: 100%;
    height: 300px;
  }
  .map_box {
    position: relative;
    box-shadow: 0 2px 4px 0 #bbb;
    margin: 0 5px;
    .city_input {
      position: absolute;
      top: 20px;
      left: 20px;
      background: #fff;
      width: 150px;
      color: #000;
    }
    .clear_btn {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
  .transForm {
    display: flex;
    margin-top: 20px;
    .label {
      color: #ffffff;
      width:80px;
      text-align: left;
      color: #000;
      padding-left: 5px;
      box-sizing: border-box;
    }
  }
</style>
