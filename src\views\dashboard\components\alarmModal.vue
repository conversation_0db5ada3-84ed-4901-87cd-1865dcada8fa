<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-30 16:51:28
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-30 17:18:40
 * @FilePath     : \tzlink-gps-web\src\views\dashboard\components\alarmModal.vue
 * Copyright (C) 2025 chen. All rights reserved.
-->
<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, h } from 'vue';
  import { BasicTable, useTable } from '@/components/Table';
  import { useModalInner, BasicModal } from '@/components/Modal';
  import { getDeviceAlarmList } from '@/api/vehicle/vehlist';
  import dayjs from 'dayjs';
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const emit = defineEmits(['success']);

  const [registerModal] = useModalInner(async (data) => {
  });

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'name',
    },
    {
      title: '报警类型',
      dataIndex: 'typeName',
    },
    {
      title: '报警编码',
      dataIndex: 'typeCode',
    },
    {
      title: '报警位置',
      dataIndex: 'gpsPosition',
    },
    {
      title: '开始时间',
      dataIndex: 'alertStart',
    },
    {
      title: '结束时间',
      dataIndex: 'alertEnd',
    },
    {
      title: '时长',
      dataIndex: 'content',
    },
    {
      title: '报警摘要',
      dataIndex: 'summary',
    },
  ];
  const [registerTable] = useTable({
    api: (params) => {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        try {
          let res = await getDeviceAlarmList(params);
          res.data = await getLocations(res.data, 0);
          resolve(res);
        } catch (error) {
          reject(error);
        }
      });
    },
    immediate: true,
    columns,
    useSearchForm: false,
    showTableSetting: false,
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    maxHeight: 500,
    rowKey: 'id',
  });
  const geocoder = new AMap.Geocoder({
    city: '010', // 城市设为北京，默认：“全国”
    radius: 1000, // 范围，默认：500
  });
  function getLocations(list, num) {
    if (num >= list.length) {
      return Promise.resolve(list);
    }
    list[num].alertEnd = dayjs(list[num].alertEnd).format('YYYY-MM-DD HH:mm:ss');
    list[num].alertStart = dayjs(list[num].alertStart).format('YYYY-MM-DD HH:mm:ss');
    if (list[num].alertEnd)
      list[num].content =
        `报警持续了` +
        Math.ceil((dayjs(list[num].alertEnd).unix() - dayjs(list[num].alertStart).unix()) / 60) +
        `分钟`;
    if (list[num].gcj02Lat && list[num].gcj02Lng) {
      return new Promise((resolve) => {
        geocoder.getAddress(
          new AMap.LngLat(list[num].gcj02Lng, list[num].gcj02Lat),
          (status, result) => {
            if (status === 'complete' && result.regeocode) {
              list[num].gpsPosition = result.regeocode.formattedAddress;
            } else {
              list[num].gpsPosition = '';
            }
            resolve(getLocations(list, num + 1));
          },
        );
      });
    } else {
      return getLocations(list, num + 1);
    }
  }
</script>

<template>
  <BasicModal width="800px" v-bind="$attrs" title="报警信息" @register="registerModal">
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>