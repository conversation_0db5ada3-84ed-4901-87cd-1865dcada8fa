<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增SIM卡 </a-button>
        <Upload
          v-model:file-list="fileList"
          :showUploadList="false"
          :headers="headers"
          name="file"
          :beforeUpload="beforeUpload"
          @change="handleChange"
          action="/basic-api/sim/import"
        >
          <a-button type="primary"> 批量导入 </a-button>
        </Upload>
        <a-button type="primary" @click="downloadFile"> 下载模板 </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                color: 'error',
                popConfirm: {
                  title: '是否确认删除',
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <AddFormModal @register="registerFormModal" @success="handleSuccess" />
    <UploadErrorForm @register="registerErrorModal" />
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getsimList, removeSim, importValidate } from '@/api/data/simCard';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useModal } from '@/components/Modal';
  import { Upload } from 'ant-design-vue';
  import UploadErrorForm from './uploadErrorForm.vue';
  import AddFormModal from './addFormModal.vue';
  import { columns, searchFormSchema } from './simCard.data';
  import axios from 'axios';
  import { useUserStoreWithOut } from '@/store/modules/user';
  const { createMessage } = useMessage();
  const userStore = useUserStoreWithOut();
  export default defineComponent({
    name: 'SimCardList',
    components: {
      BasicTable,
      TableAction,
      AddFormModal,
      Upload,
      UploadErrorForm,
    },
    setup() {
      const [registerTable, { reload }] = useTable({
        title: 'SIM卡列表',
        api: getsimList,
        columns,
        formConfig: {
          labelAlign: 'left',
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
          slot: 'action',
          fixed: 'right',
        },
      });

      const [registerFormModal, { openModal: openFormModal }] = useModal();
      function handleCreate() {
        openFormModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        console.log(record);
        openFormModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        const { createMessage } = useMessage();
        removeSim(record?.id).then((res) => {
          createMessage.success('删除成功');
          reload();
        });
      }

      function handleSuccess() {
        reload();
      }
      //下载模板
      function downloadFile() {
        axios.get(`/public/file/批量导入SIM卡.xlsx`, { responseType: 'blob' }).then((response) => {
          const blobUrl = URL.createObjectURL(response.data);
          const link = document.createElement('a');
          link.href = blobUrl;
          link.download = '批量导入SIM卡.xlsx';
          link.click();
          URL.revokeObjectURL(blobUrl); // 释放内存
        });
      }
      const fileList = ref([]);
      const [registerErrorModal, { openModal: openErrorModal }] = useModal();
      const beforeUpload = async (file, fileList) => {
        const formData = new FormData();
        formData.append('file', file);
        const res = await importValidate(formData);
        if (res.length) {
          createMessage.error('格式错误');
          openErrorModal(true, {
            record: res,
          });
          return false;
        }
      };
      const handleChange = (info: UploadChangeParam) => {
        if (info.file.status !== 'uploading') {
          console.log(info.file, info.fileList);
        }
        if (info.file.status === 'done') {
          createMessage.success(`${info.file.name} 文件上传成功`);
          reload();
        } else if (info.file.status === 'error') {
          createMessage.error(`${info.file.name} 文件上传失败`);
        }
      };
      const headers = ref({
        authorization: `Bearer ${userStore.getToken}`,
        'x-application-code': localStorage.getItem('application'),
        'x-authority': 'NONE',
      })
      return {
        downloadFile,
        registerTable,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        registerFormModal,
        fileList,
        registerErrorModal,
        beforeUpload,
        handleChange,
        headers
      };
    },
  });
</script>
<style scoped lang="less">
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    cursor: pointer;
  }

  .icon_box.active {
    color: #0FCEFD;
  }
</style>
./simCard.data