<template>
  <div class="p-4 h-full container_box">
    <div class="title">充值续费</div>
    <div class="device">
      <div class="search">
        <Select
          v-model:value="selectDevice"
          @search="getList"
          mode="multiple"
          :filter-option="false"
          :options="deviseList"
          @change="changeDevice"
          :fieldNames="{ label: 'name', value: 'id' }"
          placeholder="请输入IMEI"
        />
      </div>
      <div class="list">
        <Table :columns="columns" :data-source="dataSource" :pagination="false" />
      </div>

      <div class="title mt-4">充值套餐</div>
      <div class="pay_list">
        <div
          class="item"
          @click="activeKey = index"
          :class="{ actived: activeKey == index }"
          v-for="(item, index) in packageItems"
          :key="item.priceCode"
        >
          <dl>
            <dt>
              {{ item.priceCode }}
            </dt>
            <dd><i><span>￥</span>{{ item.priceValue }}<span>元</span></i></dd>
          </dl>
        </div>
      </div>

      <div class="title mt-4"
        >总计金额：<span
          >￥<span style="font-size:30px">{{ totalPrice }}</span>元</span
        ></div
      >

      <div class="actions mt-6">
        <Button type="primary">提交订单</Button>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
  import { computed, defineComponent, onMounted, ref } from 'vue';
  import { getDeviceList } from '@/api/vehicle/vehlist';
  import { getRechargeList } from '@/api/passport/recharge';
  import { Table, Select, Button } from 'ant-design-vue';

  export default defineComponent({
    name: 'VehicleList',
    components: { Table, Select, Button },
    setup() {
      function handleDelete(record: Recordable) {
        console.log(record);
      }
      const selectDevice: any = ref([]);
      const deviseList = ref([]);
      const dataSource: any = ref([]);
      const getList = async (deviceSn: any = '') => {
        let params = {
          pageIndex: 1,
          pageSize: 20,
          order: { property: 'id', direction: 'ASC' },
          criteria: {
            deviceSn,
          },
        };
        const res = await getDeviceList(params);
        deviseList.value = res.data || [];
      };
      const packageItems = ref([]);
      const getPackage = async () => {
        let params = {
          pageIndex: 1,
          pageSize: 20,
          order: { property: 'id', direction: 'ASC' },
          criteria: {
            isCommon: 'YES',
          },
        };
        const res = await getRechargeList(params);
        if (res.data && res.data.length) packageItems.value = res.data[0].params || [];
      };
      const columns = ref([
        {
          title: '设备名称',
          dataIndex: 'name',
          width: 150,
        },
        {
          title: 'SIM卡号',
          dataIndex: 'simNo',
          width: 150,
        },
        {
          title: 'IMEI',
          dataIndex: 'deviceSn',
        },
        {
          title: '设备类型',
          dataIndex: 'deviceModelName',
        },
        {
          title: '激活时间',
          dataIndex: 'activateTime',
        },
        {
          title: '所属机构',
          dataIndex: 'orgName',
        },
      ]);
      function changeDevice(val: any, option: any) {
        dataSource.value = option;
      }
      const activeKey = ref(0);
      const totalPrice = computed(() => {
        if (!packageItems.value.length) return '0.00';
        let price = parseInt(packageItems.value[activeKey.value].priceValue * 100);
        let len = dataSource.value.length;
        return ((price * len) / 100).toFixed(2);
      });
      onMounted(() => {
        getList();
        getPackage();
      });
      return {
        selectDevice,
        deviseList,
        getList,
        dataSource,
        columns,
        handleDelete,
        changeDevice,
        packageItems,
        activeKey,
        totalPrice,
      };
    },
  });
</script>
<style lang="less" scoped>
  .container_box {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .title {
      flex-shrink: 0;
      padding: 7px 0 0 5px;
      font-weight: bold;
      span {
        font-size: 14px;
        color: #FF5216;
      }
    }
    .device {
      flex: 1;
      height: 100px;
      background: #fff;
      box-shadow: 0 2px 4px 0 #bbb;
      margin: 10px 0 0 5px;
      overflow-y: auto;
      padding: 0 10px;
      padding-bottom: 20px;
      .search {
        padding: 10px 0;
        box-sizing: border-box;
        width: 100%;
        .ant-select {
          width: 300px;
        }
      }
      .list {
        width: 100%;
      }
    }
    .pay_list {
      display: flex;
      margin-top: 20px;
      align-items: center;
      flex-wrap: wrap;
      gap: 20px;
      .item {
        margin-bottom: 20px;
        border: 2px solid #D8DFE6;
        cursor: pointer;
        width: 120px;
        height: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        box-shadow: 0 0 10px #eee;
        border-radius: 5px;
        gap: 30px;
        
        dl,dt,dd{
          width: 100%;
          text-align: center;
          font-weight: bold;
        }
        dt{
          font-size: 14px;
          padding: 25px 0 15px;
        }
        dd{
          font-size: 40px;
          color: #FF5216;

          span {
          font-size: 16px;
        }
        }
      }
      .item:hover {
        border: 1px solid #0421BC;
      }
      .item.actived {
        border: 2px solid #5B66B1;
      }
    }
  }
</style>
