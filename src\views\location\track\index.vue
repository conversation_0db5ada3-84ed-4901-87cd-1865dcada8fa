<template>
  <div class="home_div">
    <div class="left">
      <div class="title_parent" style="margin: 0;"><span class="ml-2">轨迹回放</span></div>
      <div class="top">
        <Tabs v-model:activeKey="activeKey" @change="stateChange">
          <TabPane key="1" :tab="'全部(' + (VehData.all || 0) + ')'">
            <div class="scrollbox">
              <Tree
                style="width: 100%"
                v-if="List.length > 0"
                :defaultExpandAll="true"
                :selectedKeys="selectedKeys"
                :fieldNames="fieldNames"
                :treeData="List"
                @select="select"
                checkable
              >
                <template #title="{ dataRef }">
                  <div class="tree_box">
                    <Icon
                      :icon="iconMap[dataRef.useWay]"
                      v-if="dataRef.useWay"
                      :title="dataRef.useWay"
                      style="margin-right: 5px; color: #0421bc"
                    />
                    <div class="tree_box_name">{{ dataRef.name }}</div>
                    <div
                      class="speed"
                      :class="{ online: dataRef.online == '在线' }"
                      v-if="dataRef.id"
                    >
                      {{ dataRef.gpsSpeed || '0.00' }} km/h
                      <EnvironmentOutlined />
                    </div>
                  </div>
                </template>
              </Tree>
            </div>
          </TabPane>
          <TabPane key="2" :tab="'在线(' + (VehData.online || 0) + ')'">
            <div class="scrollbox">
              <Tree
                style="width: 100%"
                v-if="List.length > 0"
                :defaultExpandAll="true"
                v-bind="selectedVeh"
                :fieldNames="fieldNames"
                :treeData="List"
                @check="select"
                @select="select"
              >
                <template #title="{ dataRef }">
                  <div class="tree_box">
                    <Icon
                      :icon="iconMap[dataRef.useWay]"
                      v-if="dataRef.useWay"
                      :title="dataRef.useWay"
                      style="margin-right: 5px; color: #0421bc"
                    />
                    <div class="tree_box_name">{{ dataRef.name }}</div>
                    <div
                      class="speed"
                      :class="{ online: dataRef.online == '在线' }"
                      v-if="dataRef.id"
                    >
                      {{ dataRef.gpsSpeed || '0.00' }} km/h
                      <EnvironmentOutlined />
                    </div>
                  </div>
                </template>
              </Tree>
            </div>
          </TabPane>
          <TabPane key="3" :tab="'离线(' + (VehData.offline || 0) + ')'">
            <div class="scrollbox">
              <Tree
                style="width: 100%"
                v-if="List.length > 0"
                :defaultExpandAll="true"
                v-bind="selectedVeh"
                :fieldNames="fieldNames"
                :treeData="List"
                @check="select"
                @select="select"
              >
                <template #title="{ dataRef }">
                  <div class="tree_box">
                    <Icon
                      :icon="iconMap[dataRef.useWay]"
                      v-if="dataRef.useWay"
                      :title="dataRef.useWay"
                      style="margin-right: 5px; color: #0421bc"
                    />
                    <div class="tree_box_name">{{ dataRef.name }}</div>
                    <div
                      class="speed"
                      :class="{ online: dataRef.online == '在线' }"
                      v-if="dataRef.id"
                    >
                      {{ dataRef.gpsSpeed || '0.00' }} km/h
                      <EnvironmentOutlined />
                    </div>
                  </div>
                </template>
              </Tree>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
    <div class="right">
      <div class="absolute top-3 left-70 z-9" style="top: 16px">
        <RangePicker v-model:value="dateRange" show-time :allow-clear="false" class="w-96" />
        <Button type="primary" @click="query" class="ml-4">查询</Button>
      </div>
      <div class="absolute bg-white flex items-center w-82% left-70 bottom-4 pt-2 pb-2 pl-4 pr-4 z-9 gap-4" style="box-shadow: 0 0 10px #ccc; border-radius: 2px">
        <!-- 播放轨迹 -->
        <Icon
          icon="octicon:play-24"
          class="cursor-pointer hover:text-blue-500"
          size="20"
          @click="play"
        />
        <Select
          v-model:value="currentSpeed"
          :options="speedOptions"
          size="small"
          style="width: 100px"
          @change="handleSpeedChange"
        />
      </div>
      <div ref="mapRef" class="h-full"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import type { Dayjs } from 'dayjs';
  import { Tree, RangePicker, Button, Select, Tabs, TabPane } from 'ant-design-vue';
  import { useAMap } from '@/hooks/web/useAMap';
  import dayjs from 'dayjs';
  import onlineImage from '@/assets/images/equip/gis-online.png';
  import offlineImage from '@/assets/images/equip/gis-offline.png';
  import pointImage from '@/assets/images/equip/trace.png';
  import { ref, onMounted } from 'vue';
  import {
    getDeviceList,
    queryTrack,
    getTrack,
    getDeviceGroupTree,
    getVehData,
  } from '@/api/vehicle/vehlist';
  import { PoweroffOutlined, EnvironmentOutlined } from '@ant-design/icons-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useRoute } from 'vue-router';
  import { useMessage } from '@/hooks/web/useMessage';

  const {
    mapRef,
    addMarker,
    addPolyline,
    getAddress,
    getMapInstance,
    createInfoWindow,
    setMapCenterFitView,
    clearMap,
    startMoveAnimation,
    stopMoveAnimation,
  } = useAMap(
    {
      center: [116.33, 39.9],
      zoom: 5,
      mapStyle: 'amap://styles/normal', // 使用标准色样式
    },
    initMap,
  );

  type RangeValue = [Dayjs, Dayjs];
  const dateFmt = 'YYYY-MM-DD HH:mm:ss';
  const dateRange = ref<RangeValue>([dayjs().subtract(1, 'day'), dayjs()]);
  let infoWindow: AMap.InfoWindow | undefined;
  const fieldNames = {
    key: 'id',
    title: 'name',
    children: 'devices',
  };
  const { createMessage } = useMessage();
  const iconMap = ref({
    轿车: 'emojione-monotone:oncoming-automobile',
    货车: 'fa:truck',
    客车: 'fa:bus',
    出租车: 'fa:taxi',
    摩托车: 'fa:motorcycle',
    人: 'fa:user',
    挖掘机: 'mdi:excavator',
    其他: 'material-symbols-light:devices-other-outline',
  });

  const selectedKeys = ref([] as string[]);
  let selectedVeh = ref(null as any);
  let onlinestate = ref(null as any);

  let VehData = ref({
    total: 0,
    online: 0,
    offline: 0,
  });

  type Point = AMap.LngLat;
  function initMap() {
    infoWindow = createInfoWindow();
  }

  function getTree(state: any = null) {
    //TODO: 需替换成tree
    getDeviceGroupTree({ online: state }).then((res) => {
      List.value = transData(res);
    });
  }

  function getvehdata() {
    getVehData().then((res) => {
      VehData.value = res;
    });
  }

  function stateChange(key) {
    if (key == '3') {
      onlinestate.value = 'OFFLINE';
    } else if (key == '2') {
      onlinestate.value = 'ONLINE';
    } else {
      onlinestate.value = null;
    }
    selectedVeh.value = null;
    selectedKeys.value = [];
    clearMap();
    getTree(onlinestate.value);
  }

  function transData(list) {
    list = (list || []).map((v, k) => ({
      ...v,
      name: v.groupName,
      isGroup: true,
    }));
    console.log('list', list);
    return list;
  }
  function query() {
    console.log('query');
    if (!selectedVeh.value) return createMessage.warning('请选择设备');
    const [beginDate, endDate] = dateRange.value;
    let params = {
      pageIndex: 1,
      pageSize: 1000,
      order: {
        property: 'id',
        direction: 'ASC',
      },
      criteria: {
        deviceId: selectedVeh.value,
        dateTimeRange: {
          begin: dayjs(beginDate.format(dateFmt)).format('YYYY-MM-DD HH:mm:ss'),
          end: dayjs(endDate.format(dateFmt)).format('YYYY-MM-DD HH:mm:ss'),
        },
      },
    };
    getTrack(params).then((res) => {
      console.log('query track : ', res);
      if (res?.length) {
        const points: Point[] = [];

        for (let i = 0; i < res.length; i++) {
          const item = res[i];
          let icon: AMap.Icon = new AMap.Icon({
            image: pointImage,
            size: [10, 13],
          });
          let addedMarker: AMap.Marker;
          const point = new AMap.LngLat(item.lng, item.lat);
          if (i === 0) {
            icon = new AMap.Icon({
              image: onlineImage,
              size: [37, 41],
            });
            addedMarker = addMarker(point, { icon, offset: [-18.5, -20], extData: item });
            // 起点额外添加小车图标
            // addMarker(point, { icon: carIcon, offset: [-15, -15] });
            // let center = [point.lat, point.lng];
            // setMapCenterFitView([point.lat, point.lng]);
          } else if (i === res.length - 1) {
            icon = new AMap.Icon({
              image: offlineImage,
              size: [37, 41],
            });
            addedMarker = addMarker(point, { icon, offset: [-18.5, -20], extData: item });
          } else {
            addedMarker = addMarker(point, { icon, offset: [-5, -6.5], extData: item });
          }

          addedMarker.on('click', async (e: any) => {
            console.log('extraData : ', e.target.getExtData, e);
            const extData = e.target.getExtData();
            console.log('extData : ', extData);
            let address = '--';
            try {
              address = await getAddress([e.lnglat.lng, e.lnglat.lat]);
              console.log('address', address);
            } catch (error) {
              console.log('get address error : ', error);
            }
            infoWindow?.setContent(`
                <div style="font-size:14px;margin-top: 6px;">Acc状态：${
                  extData.acc == 'ON' ? '开' : '关'
                }</div>
                <div style="font-size:14px;margin-top: 6px;">速度：${extData.speed}</div>
                <div style="font-size:14px;margin-top: 10px;">
                  时间：${extData.gpsTime || '--'}
                </div>
                <div style="font-size:14px;margin-top: 6px;">位置：${address}</div>
              `);
            infoWindow?.open(getMapInstance()!, [item.lng, item.lat]);
          });

          points.push(point);
        }

        // 保存轨迹点
        trackPoints.value = points;

        //缩放地图中心
        setMapCenterFitView([points[0].lat, points[0].lng]);
        // 轨迹线
        addPolyline(points);
      } else {
        createMessage.warning('该时间段未查询到轨迹');
      }
    });
  }

  let List = ref([] as any);
  let activeKey = ref('');
  const isPlaying = ref(false);
  const trackPoints = ref<AMap.LngLat[]>([]);
  const currentSpeed = ref(1);
  const speedOptions = ref([
    { value: 0.5, label: '0.5x' },
    { value: 1, label: '1x' },
    { value: 2, label: '2x' },
    { value: 5, label: '5x' },
  ]);

  function onSearch(keword) {
    getDeviceList({
      pageIndex: 1,
      pageSize: 100,
      order: { property: 'id', direction: 'ASC' },
      criteria: { vehicleNo: keword },
    }).then((res) => {
      List.value = res.data;
    });
  }
  function select(id) {
    console.log('select', id);
    selectedKeys.value = id;
    selectedVeh.value = id[0];
    console.log('id', selectedVeh.value);
    query();
  }

  function play() {
    if (!trackPoints.value.length) {
      createMessage.warning('请先查询轨迹');
      return;
    }

    if (isPlaying.value) {
      stopMoveAnimation();
      isPlaying.value = false;
    } else {
      startMoveAnimation(trackPoints.value, {
        speed: currentSpeed.value * 100,
        onStart: () => console.log('动画开始'),
        onMoving: (e) => console.log('动画中', e),
        onEnd: () => console.log('动画结束'),
      });
      isPlaying.value = true;
    }
  }

  function handleSpeedChange() {
    if (isPlaying.value) {
      stopMoveAnimation();
      startMoveAnimation(trackPoints.value, {
        speed: currentSpeed.value * 100,
        onStart: () => console.log('动画开始'),
        onMoving: (e) => console.log('动画中', e),
        onEnd: () => console.log('动画结束'),
      });
    }
  }

  onMounted(async () => {
    const route = useRoute();
    activeKey.value = '1';
    await getTree();
    await getvehdata();
    if (route.query?.code) {
      let code = Number(route.query?.code);
      select([code]);
    }
  });
</script>

<style scoped lang="less">
  :deep(.ant-picker .ant-picker-input > input) {
    color: #000;
  }
  :deep(:where(.css-dev-only-do-not-override-ez24qu).ant-picker .ant-picker-suffix) {
    color: #000;
  }
  .home_div {
    position: relative;
    display: flex;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: none;
  }

  .left {
    width: 260px;
    height: calc(100% - 32px);
    position: absolute;
    z-index: 999;
    background: #fff;
    top: 17px;
    left: 10px;
    box-shadow: 0 0 10px #ccc;
     border-radius: 2px;
      padding-top: 3px!important;
      overflow: hidden;
  }

  .scrollbox {
    width: 100%;
    height: calc(100vh - 145px);
    max-height: calc(100vh - 145px);
    margin-top: 10px;
    padding-bottom: 100px;
    overflow-y: auto;
  }

  .top {
    padding:0 10px 10px;
  }

  .vehbox {
    display: flex;
    padding: 15px;
    border-bottom: solid 1px #536884;
    cursor: pointer;
  }

  .vehbox:hover {
    background-color: #f2faff;
    color: #273352;
    font-weight: bold;
  }

  .selected {
    background-color: #f2faff;
    color: #273352;
    font-weight: bold;
  }

  .right {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }

  .width_new {
    width: 100%;
    height: 100%;
  }
  .tree_box {
    display: flex;
    align-items: center;
    overflow: hidden;
    &_name {
      flex: 1;
      width: 100px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    .speed {
      flex-shrink: 0;
      margin-left: 5px;
      color: #666;
    }
    .speed.online {
      color: #418F03;
    }
  }
  /* 以下是marker点弹框的样式 */
</style>
