<template>
  <div class="flex flex-col h-full p-4">
    <BasicForm @register="registerForm" @submit="handleSubmit" @reset="handleReset" />
    <div class="flex-1">
      <div class="topbar">
        <a-button type="primary" @click="handleCreate"> 新增 </a-button>
        <a-button @click="open"> 启用 </a-button>
        <a-button @click="stop"> 停用 </a-button>
      </div>

      <Modal v-model:open="isOp" title="提示" @ok="open" @cancel="!isOp">
        <div class="flex gap-4">
          <Icon icon="fe:warning" style="color: #ffba00" size="30" />
          <p class="pt-3">是否启用选中围栏？</p>
        </div>
      </Modal>
      <Modal v-model:open="isSp" title="提示" @ok="stop" @cancel="!isSp">
        <div class="flex gap-4">
          <Icon icon="fe:warning" style="color: #ffba00" />
          <p class="pt-3">是否停用选中围栏？</p>
        </div>
      </Modal>

      <!-- 围栏信息 -->
      <AddFormModal @register="registerModal" @success="handleReset" />
      <!-- 设备信息 -->
      <AuthCarListForm @register="registerCarModal" />
      <!-- 关联报警 -->
      <AlertListForm @register="registerAlertModal" />

      <CheckboxGroup v-model:value="checkList" class="flex flex-wrap">
        <div v-for="(item, index) in data" :key="index" class="item">
          <div class="topsjd">
            <Checkbox class="check" :value="item.id">
              {{ '' }}
            </Checkbox>
            <Dropdown :trigger="['click']" class="more" placement="bottomLeft">
              <template #overlay>
                <Menu>
                  <MenuItem>
                    <Badge class="mark">
                      <div @click="handleEdit(item)">修改</div>
                    </Badge>
                  </MenuItem>
                  <MenuItem>
                    <Badge class="mark">
                      <div @click="del(item.id)">删除</div>
                    </Badge>
                  </MenuItem>
                </Menu>
              </template>
              <a class="el-dropdown-link">
                <Icon icon="ri:more-line" style="font-size: 22px" />
              </a>
            </Dropdown>
          </div>

          <div style="display: flex; justify-content: space-between">
            <h3>{{ item.name }}</h3>
          </div>
          <mapItem :key="'map-item-' + item.id" :info="item" class="item-top" />
          <div class="item-middle">
            <dl>
              <dt style="letter-spacing: 28px">触发</dt>
              <dd>
                ：<span v-if="item.alertEventName == '驶入'" class="normal"> 驶入 </span>
                <span v-else-if="item.alertEventName == '驶出'" class="normal"> 驶出 </span>
              </dd>
            </dl>
            <dl>
              <dt>所属机构</dt>
              <dd
                >：<span>{{ item.orgName }}</span></dd
              >
            </dl>
            <dl>
              <dt>查看信息</dt>
              <dd>
                ：<span
                  style="padding: 3px 5px; color: #1890ff; font-size: 14px; cursor: pointer"
                  @click="relVehicle({ item })"
                >
                  {{ item.deviceCount }}
                </span>
              </dd>
            </dl>
            <dl>
              <dt>关联预警</dt>
              <dd>
                ：<span
                  style="padding: 3px 5px; color: #1890ff; font-size: 14px; cursor: pointer"
                  @click="relAlarm(item)"
                >
                  {{ item.alertCount }}
                </span>
              </dd>
            </dl>
          </div>
        </div>
      </CheckboxGroup>
    </div>
    <div class="flex justify-start gap-3">
      <Pagination
        v-model:current="searchParams.pageIndex"
        v-model:page-size="searchParams.pageSize"
        :total="total"
        size="small"
        :show-total="(total) => `共&nbsp;${total}&nbsp;条`"
        show-size-changer
        :pageSizeOptions="['8', '10', '15', '20']"
        @show-size-change="handleSizeChange"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import {
    CheckboxGroup,
    Checkbox,
    Dropdown,
    Menu,
    MenuItem,
    Badge,
    Modal,
    Pagination,
  } from 'ant-design-vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import Icon from '/@/components/Icon/Icon.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { getFenceList, removeFence, unUseFence, useFence } from '@/api/vehicle/fence';
  import { useRoute, useRouter } from 'vue-router';
  import { useModal } from '@/components/Modal';
  import { searchFormSchema } from './fence.data';
  import mapItem from './districtMap.vue';
  import AddFormModal from './AddFormModal.vue';
  import AuthCarListForm from './authCarListForm.vue';
  import AlertListForm from './alertListForm.vue';
  // import { LinkOutlined, AlertOutlined } from '@ant-design/icons-vue';
  const { createMessage } = useMessage();
  const [registerModal, { openModal }] = useModal();
  const [registerCarModal, { openModal: openCarModal }] = useModal();
  const [registerAlertModal, { openModal: openAlertModal }] = useModal();

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelAlign: 'left',
    schemas: searchFormSchema,
    showActionButtonGroup: true,
  });

  const isOp = ref(false);
  const isSp = ref(false);

  const data = ref([]);
  const checkList = ref([]);

  const total = ref(0);
  const searchParams = ref({
    column: 'createTime',
    order: {
      property: 'id',
      direction: 'DESC',
    },
    pageIndex: 1,
    pageSize: 8,
  });
  const route = useRoute();

  // 查询
  const handleSubmit = async () => {
    const form = await validate();
    if (route.query && route.query.code) {
      // console.log('route.query', route.query);
      form.criteria.deviceId = route.query.code;
    }
    const params = Object.assign({}, form, searchParams.value);
    const res = await getFenceList(params);
    data.value = res.data;
    total.value = res.total;
    searchParams.value.pageSize = res.pageSize;
  };

  // 重置
  const handleReset = async () => {
    setFieldsValue({});
    searchParams.value.pageIndex = 1;
    searchParams.value.pageSize = 10;
    await handleSubmit();
  };

  const handleSizeChange = (current, size) => {
    searchParams.value.pageIndex = 1;
    handleSubmit();
  };

  // 新增
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record) {
    // console.log('handleEdit', record);
    openModal(true, {
      isUpdate: true,
      record,
    });
  }

  // 启用
  async function open() {
    console.log('open', checkList.value);
    if (checkList.value.length === 0) {
      createMessage.warning('请选择围栏');
      return;
    }
    await useFence(checkList.value);
    isOp.value = false;
    await handleSubmit();
  }

  // 停用
  async function stop() {
    console.log('stop', checkList.value);
    if (checkList.value.length === 0) {
      createMessage.warning('请选择围栏');
      return;
    }
    await unUseFence(checkList.value);
    isSp.value = false;
    await handleSubmit();
  }

  async function del(id) {
    await removeFence(id);
    await handleSubmit();
  }

  function relVehicle(item) {
    console.log('relVehicle', item);
    openCarModal(true, {
      ...item,
    });
  }

  function relAlarm(item) {
    openAlertModal(true, {
      ...item,
    });
  }

  onMounted(() => {
    handleSubmit();
  });
</script>
<style lang="less" scoped>
  .topbar {
    display: flex;
    justify-content: flex-start;
    margin-top: 10px;
    gap: 10px;
  }

  .item {
    position: relative;
    box-sizing: border-box;
    width: 299px;
    height: 295px;
    margin-top: 15px;
    margin-right: 10px;
    padding: 10px 10px 0;
    border-radius: 2px;
    background: linear-gradient(
      to bottom,
      rgb(240 249 255 / 100%),
      rgb(255 255 255 / 100%),
      rgb(255 255 255 / 100%),
      rgb(255 255 255 / 100%),
      rgb(255 255 255 / 100%),
      rgb(255 255 255 / 100%),
      rgb(255 255 255 / 100%)
    );
    box-shadow: 0 2px 4px 0 #bbb;

    h3 {
      padding: 0 0 3px;
      font-size: 14px;
      font-weight: 700;
    }
  }

  .item-top {
    width: 100%;
    height: 150px;
    border: solid 1px #eee;
  }

  .item-middle {
    width: 100%;
    padding: 5px 0 0;
    text-align: center;

    dl {
      width: 50%;
      margin: 0;
      padding: 5px 0 0;
      float: left;

      dt,
      dd {
        margin: 0;
        padding: 0;
        float: left;
        color: #666;
        text-align: left;
      }

      dt {
        width: 65px;
      }

      dd {
        width: calc(100% - 65px);

        span {
          color: #000;
        }

        .lan {
          width: 40px;
          height: 18px;
          border-radius: 2px;
          background: #1890ff;
          color: white;
          line-height: 18px;
          text-align: center;
        }

        .normal {
          display: inline-block;
          width: 40px;
          height: 18px;
          border-radius: 2px;
          background: #e6f7ff;
          color: #390;
          line-height: 18px;
          text-align: center;
        }
      }
    }
  }

  .check {
    float: left;
  }

  .topsjd {
    position: absolute;
    z-index: 99;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 3px 10px;
    border-top: dotted 1px #999;
    color: #000;
  }

  .more {
    float: right;
    cursor: pointer;
  }

  :deep(.ant-pagination-options) {
    margin-left: 10px !important;
  }
</style>
