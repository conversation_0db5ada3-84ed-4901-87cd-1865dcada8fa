import { ref } from 'vue';
import { useAMap, useGeoCoder as useAGeoCoder, type AMapPoint, type MapBoundary } from './useAMap';
import { useGMap, useGGeoCoder, type GMapPoint } from './useGMap';
import { isInChina, type GeoDetectionOptions } from '@/utils/geoLocation';
import { loadGoogleMaps, loadAMap } from '@/utils/mapLoader';

// 统一的地图点类型
export type MapPoint = [number, number] | AMap.LngLat | google.maps.LatLng;

// 地图提供商枚举
export enum MapProvider {
  AMAP = 'amap',
  GOOGLE = 'google',
}

// 统一的地图选项接口
export interface UnifiedMapOptions {
  center?: [number, number];
  zoom?: number;
  mapStyle?: string;
  // 高德地图特有选项
  amapOptions?: AMap.MapOptions;
  // Google地图特有选项
  gmapOptions?: google.maps.MapOptions;
}

// 统一的标记选项接口
export interface UnifiedMarkerOptions {
  title?: string;
  icon?: string;
  // 高德地图特有选项
  amapOptions?: AMap.MarkerOptions;
  // Google地图特有选项
  gmapOptions?: google.maps.MarkerOptions;
}

// 统一的折线选项接口
export interface UnifiedPolylineOptions {
  strokeColor?: string;
  strokeWeight?: number;
  strokeOpacity?: number;
  // 高德地图特有选项
  amapOptions?: Partial<AMap.PolylineOptions>;
  // Google地图特有选项
  gmapOptions?: Partial<google.maps.PolylineOptions>;
}

// 统一的信息窗口选项接口
export interface UnifiedInfoWindowOptions {
  content?: string;
  // 高德地图特有选项
  amapOptions?: any;
  // Google地图特有选项
  gmapOptions?: google.maps.InfoWindowOptions;
}

/**
 * 地图提供商检测和选择
 */
class MapProviderManager {
  private static instance: MapProviderManager;
  private currentProvider: MapProvider | null = null;
  private detectionPromise: Promise<MapProvider> | null = null;

  static getInstance(): MapProviderManager {
    if (!MapProviderManager.instance) {
      MapProviderManager.instance = new MapProviderManager();
    }
    return MapProviderManager.instance;
  }

  /**
   * 检测并选择地图提供商
   */
  async detectProvider(options: GeoDetectionOptions = {}): Promise<MapProvider> {
    if (this.currentProvider) {
      return this.currentProvider;
    }

    if (this.detectionPromise) {
      return this.detectionPromise;
    }

    this.detectionPromise = this.performDetection(options);
    this.currentProvider = await this.detectionPromise;
    return this.currentProvider;
  }

  private async performDetection(options: GeoDetectionOptions): Promise<MapProvider> {
    try {
      const inChina = await isInChina({
        timeout: 3000,
        ...options,
      });

      const provider = inChina ? MapProvider.AMAP : MapProvider.GOOGLE;
      console.log(
        `Map provider detection: ${inChina ? 'China' : 'International'} region detected, using ${provider}`,
      );

      // 预加载对应的地图API
      try {
        if (provider === MapProvider.AMAP) {
          await loadAMap();
        } else {
          await loadGoogleMaps();
        }
      } catch (loadError) {
        console.warn(`Failed to load ${provider} API:`, loadError);
        // 如果加载失败，尝试使用另一个提供商
        const fallbackProvider =
          provider === MapProvider.AMAP ? MapProvider.GOOGLE : MapProvider.AMAP;
        console.log(`Falling back to ${fallbackProvider}`);

        try {
          if (fallbackProvider === MapProvider.AMAP) {
            await loadAMap();
          } else {
            await loadGoogleMaps();
          }
          return fallbackProvider;
        } catch (fallbackError) {
          console.error('Both map providers failed to load:', fallbackError);
          throw new Error('Failed to load any map provider');
        }
      }

      return provider;
    } catch (error) {
      console.warn('Failed to detect location, defaulting to Google Maps:', error);

      // 尝试加载Google Maps作为默认选项
      try {
        await loadGoogleMaps();
        return MapProvider.GOOGLE;
      } catch (loadError) {
        console.warn('Failed to load Google Maps, trying AMap:', loadError);
        try {
          await loadAMap();
          return MapProvider.AMAP;
        } catch (amapError) {
          console.error('Failed to load any map provider:', amapError);
          throw new Error('No map provider available');
        }
      }
    }
  }

  /**
   * 手动设置地图提供商
   */
  setProvider(provider: MapProvider): void {
    this.currentProvider = provider;
    this.detectionPromise = Promise.resolve(provider);
  }

  /**
   * 获取当前提供商
   */
  getCurrentProvider(): MapProvider | null {
    return this.currentProvider;
  }

  /**
   * 重置检测状态
   */
  reset(): void {
    this.currentProvider = null;
    this.detectionPromise = null;
  }
}

/**
 * 转换统一选项到高德地图选项
 */
function convertToAMapOptions(options: UnifiedMapOptions): AMap.MapOptions {
  const amapOptions: AMap.MapOptions = {
    center: options.center,
    zoom: options.zoom,
    ...options.amapOptions,
  };

  // 处理地图样式
  if (options.mapStyle && !options.amapOptions?.mapStyle) {
    amapOptions.mapStyle = options.mapStyle;
  }

  return amapOptions;
}

/**
 * 转换统一选项到Google地图选项
 */
function convertToGMapOptions(options: UnifiedMapOptions): google.maps.MapOptions {
  const gmapOptions: google.maps.MapOptions = {
    center: options.center ? { lat: options.center[1], lng: options.center[0] } : undefined,
    zoom: options.zoom,
    ...options.gmapOptions,
  };

  // 处理地图样式
  if (options.mapStyle && !options.gmapOptions?.mapId) {
    // Google Maps 使用 mapId 或 styles 来设置样式
    // 这里可以根据需要进行转换
  }

  return gmapOptions;
}

/**
 * 转换标记选项
 */
function convertMarkerOptions(
  options: UnifiedMarkerOptions,
  provider: MapProvider,
): AMap.MarkerOptions | google.maps.MarkerOptions {
  if (provider === MapProvider.AMAP) {
    return {
      title: options.title,
      icon: options.icon,
      ...options.amapOptions,
    };
  } else {
    return {
      title: options.title,
      icon: options.icon,
      ...options.gmapOptions,
    };
  }
}

/**
 * 转换折线选项
 */
function convertPolylineOptions(
  options: UnifiedPolylineOptions,
  provider: MapProvider,
): Partial<AMap.PolylineOptions> | Partial<google.maps.PolylineOptions> {
  if (provider === MapProvider.AMAP) {
    return {
      strokeColor: options.strokeColor,
      borderWeight: options.strokeWeight,
      strokeOpacity: options.strokeOpacity,
      ...options.amapOptions,
    };
  } else {
    return {
      strokeColor: options.strokeColor,
      strokeWeight: options.strokeWeight,
      strokeOpacity: options.strokeOpacity,
      ...options.gmapOptions,
    };
  }
}

/**
 * 统一的地图Hook
 */
export function useMap(
  options: UnifiedMapOptions,
  callback?: (map: AMap.Map | google.maps.Map) => void,
  detectionOptions?: GeoDetectionOptions,
) {
  const provider = ref<MapProvider | null>(null);
  const isLoading = ref(true);
  const error = ref<string | null>(null);

  // 高德地图实例
  let amapInstance: ReturnType<typeof useAMap> | null = null;
  // Google地图实例
  let gmapInstance: ReturnType<typeof useGMap> | null = null;

  const manager = MapProviderManager.getInstance();

  /**
   * 初始化地图
   */
  async function initializeMap(): Promise<void> {
    try {
      isLoading.value = true;
      error.value = null;

      const detectedProvider = await manager.detectProvider(detectionOptions);
      provider.value = detectedProvider;

      if (detectedProvider === MapProvider.AMAP) {
        const amapOptions = convertToAMapOptions(options);
        amapInstance = useAMap(amapOptions, callback as any);
      } else {
        const gmapOptions = convertToGMapOptions(options);
        gmapInstance = useGMap(gmapOptions, callback as any);
      }

      isLoading.value = false;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to initialize map';
      isLoading.value = false;
      console.error('Map initialization failed:', err);
    }
  }

  // 自动初始化
  initializeMap();

  /**
   * 获取当前地图实例
   */
  function getMapInstance(): AMap.Map | google.maps.Map | undefined {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getMapInstance();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getMapInstance();
    }
    return undefined;
  }

  /**
   * 获取地图容器引用
   */
  function getMapRef() {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.mapRef;
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.mapRef;
    }
    return ref<HTMLDivElement | undefined>();
  }

  /**
   * 设置地图中心点
   */
  function setMapCenter(lngLat: [number, number]): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.setMapCenter(lngLat);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.setMapCenter(lngLat);
    }
    return false;
  }

  /**
   * 设置地图中心点并自适应视野
   */
  function setMapCenterFitView(lngLat: [number, number]): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.setMapCenterFitView(lngLat);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.setMapCenterFitView(lngLat);
    }
    return false;
  }

  /**
   * 添加标记点
   */
  function addMarker(
    position: MapPoint,
    options: UnifiedMarkerOptions = {},
  ): AMap.Marker | google.maps.Marker | null {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      const amapOptions = convertMarkerOptions(options, MapProvider.AMAP) as AMap.MarkerOptions;
      return amapInstance.addMarker(position as AMapPoint, amapOptions);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      const gmapOptions = convertMarkerOptions(
        options,
        MapProvider.GOOGLE,
      ) as google.maps.MarkerOptions;
      return gmapInstance.addMarker(position as GMapPoint, gmapOptions);
    }
    return null;
  }

  /**
   * 添加折线
   */
  function addPolyline(
    points: MapPoint[],
    options: UnifiedPolylineOptions = {},
  ): AMap.Polyline | google.maps.Polyline | null {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      const amapOptions = convertPolylineOptions(
        options,
        MapProvider.AMAP,
      ) as Partial<AMap.PolylineOptions>;
      return amapInstance.addPolyline(points as AMapPoint[], amapOptions);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      const gmapOptions = convertPolylineOptions(
        options,
        MapProvider.GOOGLE,
      ) as Partial<google.maps.PolylineOptions>;
      return gmapInstance.addPolyline(points as GMapPoint[], gmapOptions);
    }
    return null;
  }

  /**
   * 清空地图
   */
  function clearMap(): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.clearMap();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.clearMap();
    }
    return false;
  }

  /**
   * 获取地图边界
   */
  function getMapBoundary(): MapBoundary | null {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getMapBoundary();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getMapBoundary();
    }
    return null;
  }

  /**
   * 地理编码 - 根据坐标获取地址
   */
  function getAddress(lngLat: MapPoint): Promise<string> {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getAddress(lngLat as AMapPoint);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getAddress(lngLat as GMapPoint);
    }
    return Promise.reject(new Error('Map instance not available'));
  }

  /**
   * 创建信息窗口
   */
  function createInfoWindow(
    options: UnifiedInfoWindowOptions = {},
  ): AMap.InfoWindow | google.maps.InfoWindow | null {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.createInfoWindow(options.amapOptions);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.createInfoWindow(options.gmapOptions);
    }
    return null;
  }

  /**
   * 销毁地图
   */
  function destroy(): void {
    if (amapInstance) {
      amapInstance.destroy();
      amapInstance = null;
    }
    if (gmapInstance) {
      gmapInstance.destroy();
      gmapInstance = null;
    }
    provider.value = null;
  }

  /**
   * 手动切换地图提供商
   */
  async function switchProvider(newProvider: MapProvider): Promise<void> {
    if (provider.value === newProvider) {
      return;
    }

    // 销毁当前地图实例
    destroy();

    // 设置新的提供商
    manager.setProvider(newProvider);

    // 重新初始化
    await initializeMap();
  }

  return {
    provider,
    isLoading,
    error,
    mapRef: getMapRef(),
    getMapInstance,
    initializeMap,
    setMapCenter,
    setMapCenterFitView,
    addMarker,
    addPolyline,
    clearMap,
    getMapBoundary,
    getAddress,
    createInfoWindow,
    destroy,
    switchProvider,
  };
}

/**
 * 统一的地理编码Hook
 */
export function useGeoCoder(detectionOptions?: GeoDetectionOptions) {
  const provider = ref<MapProvider | null>(null);
  const isLoading = ref(true);
  const error = ref<string | null>(null);

  let amapGeocoderInstance: ReturnType<typeof useAGeoCoder> | null = null;
  let gmapGeocoderInstance: ReturnType<typeof useGGeoCoder> | null = null;

  const manager = MapProviderManager.getInstance();

  /**
   * 初始化地理编码器
   */
  async function initializeGeocoder(): Promise<void> {
    try {
      isLoading.value = true;
      error.value = null;

      const detectedProvider = await manager.detectProvider(detectionOptions);
      provider.value = detectedProvider;

      if (detectedProvider === MapProvider.AMAP) {
        amapGeocoderInstance = useAGeoCoder();
      } else {
        gmapGeocoderInstance = useGGeoCoder();
      }

      isLoading.value = false;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to initialize geocoder';
      isLoading.value = false;
      console.error('Geocoder initialization failed:', err);
    }
  }

  // 自动初始化
  initializeGeocoder();

  /**
   * 地理编码 - 根据坐标获取地址
   */
  function getAddress(lngLat: [number, number]): Promise<string> {
    if (provider.value === MapProvider.AMAP && amapGeocoderInstance) {
      return amapGeocoderInstance.getAddress(lngLat);
    } else if (provider.value === MapProvider.GOOGLE && gmapGeocoderInstance) {
      return gmapGeocoderInstance.getAddress(lngLat);
    }
    return Promise.reject(new Error('Geocoder instance not available'));
  }

  return {
    provider,
    isLoading,
    error,
    getAddress,
    initializeGeocoder,
  };
}

// 导出地图提供商管理器，供外部使用
export { MapProviderManager };
