import { onMounted, ref } from 'vue';
import {
  useAMap,
  useGeoCoder as useAGeoCoder,
  type AMapPoint,
  type MapBoundary,
  type AMapControlConfig,
  AMapControlType,
} from './useAMap';
import {
  useGMap,
  useGGeoCoder,
  type GMapPoint,
  type GMapControlConfig,
  GMapControlType,
} from './useGMap';
import { isInChina, type GeoDetectionOptions } from '@/utils/geoLocation';
import { loadGoogleMaps, loadAMap } from '@/utils/mapLoader';

// 统一的地图点类型
export type MapPoint = [number, number] | AMap.LngLat | google.maps.LatLng;

// 地图提供商枚举
export enum MapProvider {
  AMAP = 'amap',
  GOOGLE = 'google',
}

// 统一的 Marker 事件类型
export type UnifiedMarkerEventType =
  | 'click'
  | 'dblclick'
  | 'rightclick'
  | 'mouseover'
  | 'mouseout'
  | 'mousedown'
  | 'mouseup'
  | 'drag'
  | 'dragstart'
  | 'dragend';

// 统一的 Marker 类型
// @ts-ignore - 使用旧版本的 google.maps.Marker
export type UnifiedMarker = AMap.Marker | google.maps.Marker;

// 统一的地图对象类型（可触发事件的对象）
export type UnifiedMapObject =
  | AMap.Map
  | AMap.Marker
  | AMap.Polyline
  | AMap.Polygon
  | AMap.Circle
  | google.maps.Map
  | google.maps.Marker
  | google.maps.Polyline
  | google.maps.Polygon
  | google.maps.Circle
  | any;

// 统一的事件参数接口（适用于所有地图对象）
export interface UnifiedMapEvent {
  // 事件类型
  type: string;
  // 触发事件的对象（地图、marker、polyline、polygon、circle、infowindow 等）
  target: UnifiedMapObject | UnifiedInfoWindow;
  // 事件发生的地理位置 [lng, lat]（如果适用）
  lnglat: [number, number] | null;
  // 事件发生的像素位置 [x, y]（如果适用）
  pixel: [number, number] | null;
  // 原始事件对象（用于特殊需求）
  originalEvent: any;
  // 对象的扩展数据（如果适用）
  extData?: any;
  // 事件的时间戳
  timestamp: number;
  // 原始的 DOM 事件（如果有）
  domEvent?: Event;
  // 对象类型标识
  objectType: 'map' | 'marker' | 'polyline' | 'polygon' | 'circle' | 'infowindow' | 'unknown';
}

// 统一的事件处理函数类型
export type UnifiedMapEventHandler = (event: UnifiedMapEvent) => void;

// 保持向后兼容的 Marker 事件接口
export interface UnifiedMarkerEvent extends UnifiedMapEvent {
  target: UnifiedMarker;
  lnglat: [number, number];
  pixel: [number, number];
  objectType: 'marker';
}

// 保持向后兼容的 Marker 事件处理函数类型
export type UnifiedMarkerEventHandler = (event: UnifiedMarkerEvent) => void;

// 统一的地图选项接口
export interface UnifiedMapOptions {
  center?: [number, number];
  zoom?: number;
  mapStyle?: string;
  // 高德地图特有选项
  amapOptions?: AMap.MapOptions;
  // Google地图特有选项
  gmapOptions?: google.maps.MapOptions;
}

// 统一的图标配置接口
export interface UnifiedIconOptions {
  // 图标 URL 或 base64 字符串
  url: string;
  // 图标显示尺寸（容器大小）
  size?: UnifiedSize;
  // 图片实际尺寸（用于 base64 图片缩放）
  imageSize?: UnifiedSize;
  // 偏移量
  offset?: UnifiedOffset;
  // 锚点
  anchor?: string | UnifiedOffset;
}

// 统一的标记选项接口
export interface UnifiedMarkerOptions {
  title?: string;
  // 图标可以是字符串（URL）或配置对象
  icon?: string | UnifiedIconOptions;
  // 统一的尺寸设置（当 icon 为字符串时使用）
  size?: UnifiedSize;
  // 统一的偏移设置（当 icon 为字符串时使用）
  offset?: UnifiedOffset;
  // 统一的锚点设置（当 icon 为字符串时使用）
  anchor?: string;
  // 统一的扩展数据
  extData?: any;
  // 高德地图特有选项
  amapOptions?: AMap.MarkerOptions;
  // Google地图特有选项
  gmapOptions?: google.maps.MarkerOptions;
}

// 统一的折线选项接口
export interface UnifiedPolylineOptions {
  strokeColor?: string;
  strokeWeight?: number;
  strokeOpacity?: number;
  // 高德地图特有选项
  amapOptions?: Partial<AMap.PolylineOptions>;
  // Google地图特有选项
  gmapOptions?: Partial<google.maps.PolylineOptions>;
}

// 统一的多边形选项接口
export interface UnifiedPolygonOptions {
  // 填充颜色
  fillColor?: string;
  // 填充透明度
  fillOpacity?: number;
  // 边框颜色
  strokeColor?: string;
  // 边框宽度
  strokeWeight?: number;
  // 边框透明度
  strokeOpacity?: number;
  // 是否可编辑
  editable?: boolean;
  // 是否可拖拽
  draggable?: boolean;
  // 层级
  zIndex?: number;
  // 统一的扩展数据
  extData?: any;
  // 高德地图特有选项
  amapOptions?: Partial<AMap.PolygonOptions>;
  // Google地图特有选项
  gmapOptions?: Partial<google.maps.PolygonOptions>;
}

// 统一的圆形选项接口
export interface UnifiedCircleOptions {
  // 填充颜色
  fillColor?: string;
  // 填充透明度
  fillOpacity?: number;
  // 边框颜色
  strokeColor?: string;
  // 边框宽度
  strokeWeight?: number;
  // 边框透明度
  strokeOpacity?: number;
  // 是否可编辑
  editable?: boolean;
  // 是否可拖拽
  draggable?: boolean;
  // 层级
  zIndex?: number;
  // 统一的扩展数据
  extData?: any;
  // 高德地图特有选项
  amapOptions?: Partial<AMap.CircleOptions>;
  // Google地图特有选项
  gmapOptions?: Partial<google.maps.CircleOptions>;
}

// 统一的尺寸类型
export type UnifiedSize = [number, number] | { width: number; height: number };

// 统一的偏移量类型
export type UnifiedOffset = [number, number] | { x: number; y: number };

// 统一的信息窗口选项接口
export interface UnifiedInfoWindowOptions {
  content?: string;
  // 统一的偏移量（会自动转换为对应平台格式）
  offset?: UnifiedOffset;
  // 统一的尺寸（会自动转换为对应平台格式）
  size?: UnifiedSize;
  // 锚点位置
  anchor?: string;
  // 高德地图特有选项
  amapOptions?: any;
  // Google地图特有选项
  gmapOptions?: google.maps.InfoWindowOptions;
}

// 统一的 InfoWindow 事件参数接口
export interface UnifiedInfoWindowEvent {
  // 事件类型
  type: string;
  // 触发事件的 InfoWindow 对象
  target: UnifiedInfoWindow;
  // 事件发生的地理位置 [lng, lat]
  lnglat: [number, number] | null;
  // 事件发生的像素位置 [x, y]
  pixel: [number, number] | null;
  // 原始事件对象（用于特殊需求）
  originalEvent: any;
  // 事件的时间戳
  timestamp: number;
  // 原始的 DOM 事件（如果有）
  domEvent?: Event;
}

// 统一的 InfoWindow 事件处理函数类型
export type UnifiedInfoWindowEventHandler = (event: UnifiedInfoWindowEvent) => void;

// 统一的信息窗口接口
export interface UnifiedInfoWindow {
  open(mapOrPosition?: any, position?: any): void;
  close(): void;
  setContent(content: string | HTMLElement): void;
  getContent(): string | HTMLElement | null;
  setPosition(position: MapPoint): void;
  getPosition(): MapPoint | null;
  setOffset(offset: UnifiedOffset): void;
  getOffset(): UnifiedOffset | null;
  setSize(size: UnifiedSize): void;
  getSize(): UnifiedSize | null;
  getRawInstance(): AMap.InfoWindow | google.maps.InfoWindow | null;
}

/**
 * 转换统一偏移量为 AMap 格式
 */
export function convertOffsetToAMap(offset: UnifiedOffset): AMap.Pixel {
  if (Array.isArray(offset)) {
    return new AMap.Pixel(offset[0], offset[1]);
  } else {
    return new AMap.Pixel(offset.x, offset.y);
  }
}

/**
 * 转换统一偏移量为 Google Maps 格式
 */
export function convertOffsetToGoogleMaps(offset: UnifiedOffset): google.maps.Size {
  if (Array.isArray(offset)) {
    return new google.maps.Size(offset[0], offset[1]);
  } else {
    return new google.maps.Size(offset.x, offset.y);
  }
}

/**
 * 转换统一尺寸为 AMap 格式
 */
export function convertSizeToAMap(size: UnifiedSize): AMap.Size {
  if (Array.isArray(size)) {
    return new AMap.Size(size[0], size[1]);
  } else {
    return new AMap.Size(size.width, size.height);
  }
}

/**
 * 转换统一尺寸为 Google Maps 格式
 */
export function convertSizeToGoogleMaps(size: UnifiedSize): google.maps.Size {
  if (Array.isArray(size)) {
    return new google.maps.Size(size[0], size[1]);
  } else {
    return new google.maps.Size(size.width, size.height);
  }
}

/**
 * 从 AMap 格式转换为统一偏移量
 */
export function convertOffsetFromAMap(offset: AMap.Pixel): UnifiedOffset {
  return [offset.x, offset.y];
}

/**
 * 从 Google Maps 格式转换为统一偏移量
 */
export function convertOffsetFromGoogleMaps(offset: google.maps.Size): UnifiedOffset {
  return [offset.width, offset.height];
}

/**
 * 从 AMap 格式转换为统一尺寸
 */
export function convertSizeFromAMap(size: AMap.Size): UnifiedSize {
  return [size.width, size.height];
}

/**
 * 从 Google Maps 格式转换为统一尺寸
 */
export function convertSizeFromGoogleMaps(size: google.maps.Size): UnifiedSize {
  return [size.width, size.height];
}

/**
 * 检测地图对象类型
 */
function detectObjectType(target: any): UnifiedMapEvent['objectType'] {
  if (!target) return 'unknown';

  // 检测 AMap 对象类型
  if (target.CLASS_NAME) {
    if (target.CLASS_NAME.includes('Map')) return 'map';
    if (target.CLASS_NAME.includes('Marker')) return 'marker';
    if (target.CLASS_NAME.includes('Polyline')) return 'polyline';
    if (target.CLASS_NAME.includes('Polygon')) return 'polygon';
    if (target.CLASS_NAME.includes('Circle')) return 'circle';
    if (target.CLASS_NAME.includes('InfoWindow')) return 'infowindow';
  }

  // 检测 Google Maps 对象类型
  if (target.constructor && target.constructor.name) {
    const constructorName = target.constructor.name.toLowerCase();
    if (constructorName.includes('map')) return 'map';
    if (constructorName.includes('marker')) return 'marker';
    if (constructorName.includes('polyline')) return 'polyline';
    if (constructorName.includes('polygon')) return 'polygon';
    if (constructorName.includes('circle')) return 'circle';
    if (constructorName.includes('infowindow')) return 'infowindow';
  }

  // 通过方法检测
  if (typeof target.getCenter === 'function' && typeof target.setZoom === 'function') return 'map';
  if (typeof target.getPosition === 'function' && typeof target.setPosition === 'function')
    return 'marker';
  if (typeof target.getPath === 'function') return 'polyline';
  if (typeof target.getPaths === 'function') return 'polygon';
  if (typeof target.getRadius === 'function') return 'circle';
  if (typeof target.getContent === 'function' && typeof target.open === 'function')
    return 'infowindow';

  return 'unknown';
}

/**
 * 转换 AMap 事件参数为统一格式（通用方法）
 */
export function convertAMapEvent(
  originalEvent: any,
  target: any,
  eventType: string,
): UnifiedMapEvent {
  const objectType = detectObjectType(target);
  let lnglat: [number, number] | null = null;
  let pixel: [number, number] | null = null;

  // AMap 事件对象通常包含 lnglat 和 pixel 属性
  if (originalEvent.lnglat) {
    if (Array.isArray(originalEvent.lnglat)) {
      lnglat = originalEvent.lnglat;
    } else if (originalEvent.lnglat.lng !== undefined && originalEvent.lnglat.lat !== undefined) {
      lnglat = [originalEvent.lnglat.lng, originalEvent.lnglat.lat];
    }
  }

  if (originalEvent.pixel) {
    if (Array.isArray(originalEvent.pixel)) {
      pixel = originalEvent.pixel;
    } else if (originalEvent.pixel.x !== undefined && originalEvent.pixel.y !== undefined) {
      pixel = [originalEvent.pixel.x, originalEvent.pixel.y];
    }
  }

  return {
    type: eventType,
    target,
    lnglat,
    pixel,
    originalEvent,
    extData: originalEvent.extData || target.getExtData?.(),
    timestamp: Date.now(),
    domEvent: originalEvent.domEvent,
    objectType,
  };
}

/**
 * 转换 Google Maps 事件参数为统一格式（通用方法）
 */
export function convertGoogleMapsEvent(
  originalEvent: any,
  target: any,
  eventType: string,
): UnifiedMapEvent {
  const objectType = detectObjectType(target);
  let lnglat: [number, number] | null = null;
  let pixel: [number, number] | null = null;

  // Google Maps 事件对象结构不同，通常包含 latLng 属性
  if (originalEvent && originalEvent.latLng) {
    lnglat = [originalEvent.latLng.lng(), originalEvent.latLng.lat()];
  }

  // Google Maps 通常没有直接的 pixel 信息
  if (originalEvent && originalEvent.pixel) {
    pixel = [originalEvent.pixel.x, originalEvent.pixel.y];
  }

  return {
    type: eventType,
    target,
    lnglat,
    pixel,
    originalEvent,
    extData: undefined, // Google Maps 没有 extData 概念
    timestamp: Date.now(),
    domEvent: originalEvent?.domEvent || originalEvent,
    objectType,
  };
}

/**
 * 转换 AMap InfoWindow 事件参数为统一格式
 */
export function convertAMapInfoWindowEvent(
  originalEvent: any,
  infoWindow: UnifiedInfoWindow,
  eventType: string,
): UnifiedInfoWindowEvent {
  let lnglat: [number, number] | null = null;
  let pixel: [number, number] | null = null;

  // AMap 事件对象通常包含 lnglat 和 pixel 属性
  if (originalEvent.lnglat) {
    if (Array.isArray(originalEvent.lnglat)) {
      lnglat = originalEvent.lnglat;
    } else if (originalEvent.lnglat.lng !== undefined && originalEvent.lnglat.lat !== undefined) {
      lnglat = [originalEvent.lnglat.lng, originalEvent.lnglat.lat];
    }
  }

  if (originalEvent.pixel) {
    if (Array.isArray(originalEvent.pixel)) {
      pixel = originalEvent.pixel;
    } else if (originalEvent.pixel.x !== undefined && originalEvent.pixel.y !== undefined) {
      pixel = [originalEvent.pixel.x, originalEvent.pixel.y];
    }
  }

  return {
    type: eventType,
    target: infoWindow,
    lnglat,
    pixel,
    originalEvent,
    timestamp: Date.now(),
    domEvent: originalEvent.domEvent,
  };
}

/**
 * 转换 Google Maps InfoWindow 事件参数为统一格式
 */
export function convertGoogleMapsInfoWindowEvent(
  originalEvent: any,
  infoWindow: UnifiedInfoWindow,
  eventType: string,
): UnifiedInfoWindowEvent {
  let lnglat: [number, number] | null = null;
  let pixel: [number, number] | null = null;

  // Google Maps 事件对象结构不同，通常包含 latLng 属性
  if (originalEvent && originalEvent.latLng) {
    lnglat = [originalEvent.latLng.lng(), originalEvent.latLng.lat()];
  }

  // Google Maps 的像素位置需要通过其他方式获取，这里暂时设为 null
  // 实际使用中可能需要通过 map.getProjection() 来转换
  pixel = null;

  return {
    type: eventType,
    target: infoWindow,
    lnglat,
    pixel,
    originalEvent,
    timestamp: Date.now(),
    domEvent: originalEvent.domEvent || originalEvent,
  };
}

// 统一的控件类型枚举
export enum UnifiedControlType {
  // 通用控件
  ZOOM = 'zoom',
  MAPTYPE = 'maptype',
  SCALE = 'scale',
  // 高德地图特有控件
  TOOLBAR = 'toolbar',
  CONTROLBAR = 'controlbar',
  HAWKEYE = 'hawkeye',
  // Google地图特有控件
  STREETVIEW = 'streetview',
  FULLSCREEN = 'fullscreen',
  ROTATE = 'rotate',
}

// 统一的控件配置接口
export interface UnifiedControlConfig {
  type: UnifiedControlType;
  position?: any; // 位置配置，根据地图提供商不同而不同
  options?: any; // 控件特定选项
  // 高德地图特有配置
  amapConfig?: AMapControlConfig;
  // Google地图特有配置
  gmapConfig?: GMapControlConfig;
}

// 预定义的统一控件配置
export const DEFAULT_UNIFIED_CONTROL_CONFIGS: Record<string, UnifiedControlConfig> = {
  zoom: {
    type: UnifiedControlType.ZOOM,
    amapConfig: {
      type: AMapControlType.TOOLBAR,
      position: { top: '160px', right: '40px' },
    },
    gmapConfig: {
      type: GMapControlType.ZOOM,
      // position: google.maps?.ControlPosition?.RIGHT_CENTER,
    },
  },
  maptype: {
    type: UnifiedControlType.MAPTYPE,
    amapConfig: {
      type: AMapControlType.MAPTYPE,
      options: { defaultType: 0 },
    },
    gmapConfig: {
      type: GMapControlType.MAPTYPE,
      // position: google.maps?.ControlPosition?.TOP_RIGHT,
    },
  },
  scale: {
    type: UnifiedControlType.SCALE,
    amapConfig: {
      type: AMapControlType.SCALE,
    },
    gmapConfig: {
      type: GMapControlType.SCALE,
    },
  },
  toolbar: {
    type: UnifiedControlType.TOOLBAR,
    amapConfig: {
      type: AMapControlType.TOOLBAR,
      position: { top: '160px', right: '40px' },
    },
  },
  controlbar: {
    type: UnifiedControlType.CONTROLBAR,
    amapConfig: {
      type: AMapControlType.CONTROLBAR,
      position: { top: '20px', right: '150px' },
    },
  },
  hawkeye: {
    type: UnifiedControlType.HAWKEYE,
    amapConfig: {
      type: AMapControlType.HAWKEYE,
    },
  },
  streetview: {
    type: UnifiedControlType.STREETVIEW,
    gmapConfig: {
      type: GMapControlType.STREETVIEW,
      // position: google.maps?.ControlPosition?.RIGHT_BOTTOM,
    },
  },
  fullscreen: {
    type: UnifiedControlType.FULLSCREEN,
    gmapConfig: {
      type: GMapControlType.FULLSCREEN,
      // position: google.maps?.ControlPosition?.TOP_RIGHT,
    },
  },
  rotate: {
    type: UnifiedControlType.ROTATE,
    gmapConfig: {
      type: GMapControlType.ROTATE,
      // position: google.maps?.ControlPosition?.RIGHT_CENTER,
    },
  },
};

/**
 * 地图提供商检测和选择
 */
class MapProviderManager {
  private static instance: MapProviderManager;
  private currentProvider: MapProvider | null = null;
  private detectionPromise: Promise<MapProvider> | null = null;

  static getInstance(): MapProviderManager {
    if (!MapProviderManager.instance) {
      MapProviderManager.instance = new MapProviderManager();
    }
    return MapProviderManager.instance;
  }

  /**
   * 检测并选择地图提供商
   */
  async detectProvider(options: GeoDetectionOptions = {}): Promise<MapProvider> {
    if (this.currentProvider) {
      return this.currentProvider;
    }

    if (this.detectionPromise) {
      return this.detectionPromise;
    }

    this.detectionPromise = this.performDetection(options);
    this.currentProvider = await this.detectionPromise;
    return this.currentProvider;
  }

  private async performDetection(options: GeoDetectionOptions): Promise<MapProvider> {
    try {
      const inChina = await isInChina({
        timeout: 3000,
        ...options,
      });
      // TODO: for test, need remove
      // const inChina = false;

      const provider = inChina ? MapProvider.AMAP : MapProvider.GOOGLE;
      console.log(
        `Map provider detection: ${inChina ? 'China' : 'International'} region detected, using ${provider}`,
      );

      // 预加载对应的地图API
      try {
        if (provider === MapProvider.AMAP) {
          await loadAMap();
        } else {
          await loadGoogleMaps(undefined, options);
        }
      } catch (loadError) {
        console.warn(`Failed to load ${provider} API:`, loadError);
        // 如果加载失败，尝试使用另一个提供商
        const fallbackProvider =
          provider === MapProvider.AMAP ? MapProvider.GOOGLE : MapProvider.AMAP;
        console.log(`Falling back to ${fallbackProvider}`);

        try {
          if (fallbackProvider === MapProvider.AMAP) {
            await loadAMap();
          } else {
            await loadGoogleMaps(undefined, options);
          }
          return fallbackProvider;
        } catch (fallbackError) {
          console.error('Both map providers failed to load:', fallbackError);
          throw new Error('Failed to load any map provider');
        }
      }

      return provider;
    } catch (error) {
      console.warn('Failed to detect location, defaulting to Google Maps:', error);

      // 尝试加载Google Maps作为默认选项
      try {
        await loadGoogleMaps(undefined, options);
        return MapProvider.GOOGLE;
      } catch (loadError) {
        console.warn('Failed to load Google Maps, trying AMap:', loadError);
        try {
          await loadAMap();
          return MapProvider.AMAP;
        } catch (amapError) {
          console.error('Failed to load any map provider:', amapError);
          throw new Error('No map provider available');
        }
      }
    }
  }

  /**
   * 手动设置地图提供商
   */
  setProvider(provider: MapProvider): void {
    this.currentProvider = provider;
    this.detectionPromise = Promise.resolve(provider);
  }

  /**
   * 获取当前提供商
   */
  getCurrentProvider(): MapProvider | null {
    return this.currentProvider;
  }

  /**
   * 重置检测状态
   */
  reset(): void {
    this.currentProvider = null;
    this.detectionPromise = null;
  }
}

/**
 * 转换统一选项到高德地图选项
 */
function convertToAMapOptions(options: UnifiedMapOptions): AMap.MapOptions {
  const amapOptions: AMap.MapOptions = {
    center: options.center,
    zoom: options.zoom,
    ...options.amapOptions,
  };

  // 处理地图样式
  if (options.mapStyle && !options.amapOptions?.mapStyle) {
    amapOptions.mapStyle = options.mapStyle;
  }

  return amapOptions;
}

/**
 * 转换统一选项到Google地图选项
 */
function convertToGMapOptions(options: UnifiedMapOptions): google.maps.MapOptions {
  const gmapOptions: google.maps.MapOptions = {
    center: options.center ? { lat: options.center[1], lng: options.center[0] } : undefined,
    zoom: options.zoom,
    ...options.gmapOptions,
  };

  // 处理地图样式
  if (options.mapStyle && !options.gmapOptions?.mapId) {
    // Google Maps 使用 mapId 或 styles 来设置样式
    // 这里可以根据需要进行转换
  }

  return gmapOptions;
}

/**
 * 转换标记选项
 */
function convertMarkerOptions(
  options: UnifiedMarkerOptions,
  provider: MapProvider,
): AMap.MarkerOptions | google.maps.MarkerOptions {
  if (provider === MapProvider.AMAP) {
    const amapOptions: AMap.MarkerOptions = {
      title: options.title,
      ...options.amapOptions,
    };

    // 处理图标配置
    if (options.icon) {
      if (typeof options.icon === 'string') {
        // 简单字符串图标
        if (options.size || options.offset || options.anchor) {
          // 需要创建 AMap.Icon 对象来支持尺寸和偏移
          const iconConfig: AMap.IconOpts = {
            image: options.icon,
          };

          if (options.size) {
            iconConfig.size = convertSizeToAMap(options.size);
            iconConfig.imageSize = convertSizeToAMap(options.size);
          }

          if (options.offset) {
            iconConfig.imageOffset = convertOffsetToAMap(options.offset);
          }

          amapOptions.icon = new AMap.Icon(iconConfig);
        } else {
          amapOptions.icon = options.icon;
        }
      } else {
        // UnifiedIconOptions 对象
        const iconConfig: AMap.IconOpts = {
          image: options.icon.url,
        };

        // 优先使用 icon 对象中的配置
        if (options.icon.size) {
          iconConfig.size = convertSizeToAMap(options.icon.size);
        }

        if (options.icon.imageSize) {
          iconConfig.imageSize = convertSizeToAMap(options.icon.imageSize);
        }

        if (options.icon.offset) {
          iconConfig.imageOffset = convertOffsetToAMap(options.icon.offset);
        }

        amapOptions.icon = new AMap.Icon(iconConfig);
      }
    }

    // 处理统一的扩展数据
    if (options.extData) {
      amapOptions.extData = options.extData;
    }

    return amapOptions;
  } else {
    const gmapOptions: google.maps.MarkerOptions & { extData?: any } = {
      title: options.title,
      ...options.gmapOptions,
    };

    // 处理图标配置
    if (options.icon) {
      if (typeof options.icon === 'string') {
        // 简单字符串图标
        if (options.size || options.offset || options.anchor) {
          // 需要创建 Icon 对象来支持尺寸和偏移
          const iconConfig: google.maps.Icon = {
            url: options.icon,
          };

          if (options.size) {
            iconConfig.size = convertSizeToGoogleMaps(options.size);
            iconConfig.scaledSize = convertSizeToGoogleMaps(options.size);
          }

          if (options.offset) {
            const [x, y] = Array.isArray(options.offset)
              ? options.offset
              : [options.offset.x, options.offset.y];
            iconConfig.anchor = new google.maps.Point(x, Math.abs(y));
          }

          gmapOptions.icon = iconConfig;
        } else {
          gmapOptions.icon = options.icon;
        }
      } else {
        // UnifiedIconOptions 对象
        const iconConfig: google.maps.Icon = {
          url: options.icon.url,
        };

        // 优先使用 icon 对象中的配置
        if (options.icon.size) {
          iconConfig.size = convertSizeToGoogleMaps(options.icon.size);
          iconConfig.scaledSize = convertSizeToGoogleMaps(options.icon.size);
        }

        if (options.icon.imageSize) {
          // Google Maps 使用 scaledSize 来控制实际显示大小
          iconConfig.scaledSize = convertSizeToGoogleMaps(options.icon.imageSize);
        }

        if (options.icon.offset) {
          const [x, y] = Array.isArray(options.icon.offset)
            ? options.icon.offset
            : [options.icon.offset.x, options.icon.offset.y];
          iconConfig.anchor = new google.maps.Point(x, Math.abs(y));
        }

        gmapOptions.icon = iconConfig;
      }
    }

    // 处理统一的扩展数据 - 对于 Google Maps，将 extData 传递给 addMarker 方法
    if (options.extData !== undefined) {
      gmapOptions.extData = options.extData;
    }

    return gmapOptions;
  }
}

/**
 * 转换折线选项
 */
function convertPolylineOptions(
  options: UnifiedPolylineOptions,
  provider: MapProvider,
): Partial<AMap.PolylineOptions> | Partial<google.maps.PolylineOptions> {
  if (provider === MapProvider.AMAP) {
    return {
      strokeColor: options.strokeColor,
      borderWeight: options.strokeWeight,
      strokeOpacity: options.strokeOpacity,
      ...options.amapOptions,
    };
  } else {
    return {
      strokeColor: options.strokeColor,
      strokeWeight: options.strokeWeight,
      strokeOpacity: options.strokeOpacity,
      ...options.gmapOptions,
    };
  }
}

/**
 * 转换多边形选项
 */
function convertPolygonOptions(
  options: UnifiedPolygonOptions,
  provider: MapProvider,
): Partial<AMap.PolygonOptions> | Partial<google.maps.PolygonOptions> {
  if (provider === MapProvider.AMAP) {
    const amapOptions: Partial<AMap.PolygonOptions> = {
      fillColor: options.fillColor,
      fillOpacity: options.fillOpacity,
      strokeColor: options.strokeColor,
      strokeWeight: options.strokeWeight,
      strokeOpacity: options.strokeOpacity,
      zIndex: options.zIndex,
      extData: options.extData,
      ...options.amapOptions,
    };
    return amapOptions;
  } else {
    const gmapOptions: Partial<google.maps.PolygonOptions> & { extData?: any } = {
      fillColor: options.fillColor,
      fillOpacity: options.fillOpacity,
      strokeColor: options.strokeColor,
      strokeWeight: options.strokeWeight,
      strokeOpacity: options.strokeOpacity,
      zIndex: options.zIndex,
      editable: options.editable,
      draggable: options.draggable,
      extData: options.extData,
      ...options.gmapOptions,
    };
    return gmapOptions;
  }
}

/**
 * 转换圆形选项
 */
function convertCircleOptions(
  options: UnifiedCircleOptions,
  provider: MapProvider,
): Partial<AMap.CircleOptions> | Partial<google.maps.CircleOptions> {
  if (provider === MapProvider.AMAP) {
    const amapOptions: Partial<AMap.CircleOptions> = {
      fillColor: options.fillColor,
      fillOpacity: options.fillOpacity,
      strokeColor: options.strokeColor,
      strokeWeight: options.strokeWeight,
      strokeOpacity: options.strokeOpacity,
      zIndex: options.zIndex,
      extData: options.extData,
      ...options.amapOptions,
    };
    return amapOptions;
  } else {
    const gmapOptions: Partial<google.maps.CircleOptions> & { extData?: any } = {
      fillColor: options.fillColor,
      fillOpacity: options.fillOpacity,
      strokeColor: options.strokeColor,
      strokeWeight: options.strokeWeight,
      strokeOpacity: options.strokeOpacity,
      zIndex: options.zIndex,
      editable: options.editable,
      draggable: options.draggable,
      extData: options.extData,
      ...options.gmapOptions,
    };
    return gmapOptions;
  }
}

/**
 * 通用事件处理函数映射管理器
 */
class UniversalEventHandlerManager {
  private handlerMap = new WeakMap<
    any, // 支持任何地图对象
    Map<string, Map<UnifiedMapEventHandler, Function>>
  >();

  /**
   * 设置包装后的事件处理函数
   */
  setWrappedHandler(
    target: any,
    event: string,
    originalHandler: UnifiedMapEventHandler,
    wrappedHandler: Function,
  ): void {
    if (!this.handlerMap.has(target)) {
      this.handlerMap.set(target, new Map());
    }

    const targetEvents = this.handlerMap.get(target)!;
    if (!targetEvents.has(event)) {
      targetEvents.set(event, new Map());
    }

    const eventHandlers = targetEvents.get(event)!;
    eventHandlers.set(originalHandler, wrappedHandler);
  }

  /**
   * 获取包装后的事件处理函数
   */
  getWrappedHandler(
    target: any,
    event: string,
    originalHandler: UnifiedMapEventHandler,
  ): Function | undefined {
    const targetEvents = this.handlerMap.get(target);
    if (!targetEvents) return undefined;

    const eventHandlers = targetEvents.get(event);
    if (!eventHandlers) return undefined;

    return eventHandlers.get(originalHandler);
  }

  /**
   * 移除包装后的事件处理函数
   */
  removeWrappedHandler(target: any, event: string, originalHandler: UnifiedMapEventHandler): void {
    const targetEvents = this.handlerMap.get(target);
    if (!targetEvents) return;

    const eventHandlers = targetEvents.get(event);
    if (!eventHandlers) return;

    eventHandlers.delete(originalHandler);

    // 清理空的映射
    if (eventHandlers.size === 0) {
      targetEvents.delete(event);
    }
    if (targetEvents.size === 0) {
      this.handlerMap.delete(target);
    }
  }
}

// 全局通用事件处理函数管理器实例
const universalEventHandlerManager = new UniversalEventHandlerManager();

/**
 * 统一的地图Hook
 */
export function useMap(
  options: UnifiedMapOptions,
  callback?: (map: AMap.Map | google.maps.Map) => void,
  detectionOptions?: GeoDetectionOptions,
) {
  const provider = ref<MapProvider | null>(null);
  const isLoading = ref(true);
  const error = ref<string | null>(null);
  const containerRef = ref<HTMLDivElement | undefined>();

  // 高德地图实例
  let amapInstance: ReturnType<typeof useAMap> | null = null;
  // Google地图实例
  let gmapInstance: ReturnType<typeof useGMap> | null = null;

  const manager = MapProviderManager.getInstance();

  /**
   * 初始化地图
   */
  async function initializeMap(): Promise<void> {
    try {
      isLoading.value = true;
      error.value = null;

      const detectedProvider = await manager.detectProvider(detectionOptions);
      console.log('detected provider : ', detectedProvider);
      provider.value = detectedProvider;

      if (detectedProvider === MapProvider.AMAP) {
        const amapOptions = convertToAMapOptions(options);
        amapInstance = useAMap(amapOptions, callback as any);
        amapInstance.init(containerRef.value!);
      } else {
        const gmapOptions = convertToGMapOptions(options);
        gmapInstance = useGMap(gmapOptions, callback as any);
        gmapInstance.init(containerRef.value!);
      }

      isLoading.value = false;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to initialize map';
      isLoading.value = false;
      console.error('Map initialization failed:', err);
    }
  }

  /**
   * 获取当前地图实例
   */
  function getMapInstance(): AMap.Map | google.maps.Map | undefined {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getMapInstance();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getMapInstance();
    }
    return undefined;
  }

  /**
   * 开始绘制多边形
   * @param options 多边形样式选项
   */
  function startDrawPolygon(options: any = {}): void {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      amapInstance.startDrawPolygon(options);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      gmapInstance.startDrawPolygon(options);
    } else {
      console.error('Map provider is not initialized');
    }
  }

  /**
   * 开始绘制圆形
   * @param options 圆形样式选项
   */
  function startDrawCircle(options: any = {}): void {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      amapInstance.startDrawCircle(options);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      gmapInstance.startDrawCircle(options);
    } else {
      console.error('Map provider is not initialized');
    }
  }

  /**
   * 停止绘制
   */
  function stopDrawing(): void {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      amapInstance.stopDrawing();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      gmapInstance.stopDrawing();
    } else {
      console.error('Map provider is not initialized');
    }
  }

  /**
   * 清除绘制的图形
   */
  function clearDrawingOverlays(): void {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      amapInstance.clearDrawingOverlays();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      gmapInstance.clearDrawingOverlays();
    } else {
      console.error('Map provider is not initialized');
    }
  }

  /**
   * 获取当前绘制的图形
   */
  function getDrawingOverlays(): any[] {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getDrawingOverlays();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getDrawingOverlays();
    } else {
      console.error('Map provider is not initialized');
      return [];
    }
  }

  /**
   * 获取绘制图形的几何数据
   */
  function getDrawingData(): Array<{
    type: 'polygon' | 'circle';
    data: any;
  }> {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getDrawingData();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getDrawingData();
    } else {
      console.error('Map provider is not initialized');
      return [];
    }
  }

  /**
   * 设置地图显示的城市
   * @param cityName 城市名称，如"北京"、"上海"等
   * @returns Promise<boolean> 设置是否成功
   */
  function setCity(cityName: string): Promise<boolean> {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.setCity(cityName);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.setCity(cityName);
    } else {
      console.error('Map provider is not initialized');
      return Promise.resolve(false);
    }
  }

  /**
   * 手动初始化绘制工具
   * 用于在 onMounted 自动初始化失败时，在业务代码中手动调用
   * @returns boolean 初始化是否成功
   */
  function initializeDrawingTool(): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      // 检查 AMap 实例是否有 initializeDrawingTool 方法
      if (typeof amapInstance.initializeDrawingTool === 'function') {
        try {
          amapInstance.initializeDrawingTool();
          console.log('AMap drawing tool initialized successfully');
          return true;
        } catch (error) {
          console.error('Failed to initialize AMap drawing tool:', error);
          return false;
        }
      } else {
        console.error('AMap instance does not have initializeDrawingTool method');
        return false;
      }
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      // 检查 Google Maps 实例是否有 initializeDrawingTool 方法
      if (typeof gmapInstance.initializeDrawingTool === 'function') {
        try {
          gmapInstance.initializeDrawingTool();
          console.log('Google Maps drawing tool initialized successfully');
          return true;
        } catch (error) {
          console.error('Failed to initialize Google Maps drawing tool:', error);
          return false;
        }
      } else {
        console.error('Google Maps instance does not have initializeDrawingTool method');
        return false;
      }
    } else {
      console.error('Map provider is not initialized');
      return false;
    }
  }

  /**
   * 开始轨迹回放动画
   * @param points 轨迹点数组（GCJ-02 坐标系）
   * @param options 动画配置选项
   */
  function startMoveAnimation(
    points: MapPoint[],
    options: {
      speed?: number;
      loop?: boolean;
      onStart?: () => void;
      onMoving?: (e: any) => void;
      onEnd?: () => void;
    } = {},
  ): void {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      // 检查 AMap 实例是否有 startMoveAnimation 方法
      if (typeof amapInstance.startMoveAnimation === 'function') {
        try {
          amapInstance.startMoveAnimation(points as AMapPoint[], options);
          console.log('AMap trajectory animation started');
        } catch (error) {
          console.error('Failed to start AMap trajectory animation:', error);
        }
      } else {
        console.error('AMap instance does not have startMoveAnimation method');
      }
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      // 检查 Google Maps 实例是否有 startMoveAnimation 方法
      if (typeof gmapInstance.startMoveAnimation === 'function') {
        try {
          gmapInstance.startMoveAnimation(points as GMapPoint[], options);
          console.log('Google Maps trajectory animation started');
        } catch (error) {
          console.error('Failed to start Google Maps trajectory animation:', error);
        }
      } else {
        console.error('Google Maps instance does not have startMoveAnimation method');
      }
    } else {
      console.error('Map provider is not initialized');
    }
  }

  /**
   * 停止轨迹回放动画
   */
  function stopMoveAnimation(): void {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      // 检查 AMap 实例是否有 stopMoveAnimation 方法
      if (typeof amapInstance.stopMoveAnimation === 'function') {
        try {
          amapInstance.stopMoveAnimation();
          console.log('AMap trajectory animation stopped');
        } catch (error) {
          console.error('Failed to stop AMap trajectory animation:', error);
        }
      } else {
        console.error('AMap instance does not have stopMoveAnimation method');
      }
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      // 检查 Google Maps 实例是否有 stopMoveAnimation 方法
      if (typeof gmapInstance.stopMoveAnimation === 'function') {
        try {
          gmapInstance.stopMoveAnimation();
          console.log('Google Maps trajectory animation stopped');
        } catch (error) {
          console.error('Failed to stop Google Maps trajectory animation:', error);
        }
      } else {
        console.error('Google Maps instance does not have stopMoveAnimation method');
      }
    } else {
      console.error('Map provider is not initialized');
    }
  }

  /**
   * 获取车辆图标配置
   * @returns 当前地图提供商的车辆图标配置
   */
  function getCarIcon(): any {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.CAR_ICON;
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.CAR_ICON;
    } else {
      console.error('Map provider is not initialized');
      return null;
    }
  }

  /**
   * 获取高德地图实例（类型安全）
   * @returns AMap.Map实例，如果当前不是高德地图则返回undefined
   */
  function getAMapInstance(): AMap.Map | undefined {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getMapInstance();
    }
    return undefined;
  }

  /**
   * 获取Google Maps实例（类型安全）
   * @returns google.maps.Map实例，如果当前不是Google Maps则返回undefined
   */
  function getGoogleMapInstance(): google.maps.Map | undefined {
    if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getMapInstance();
    }
    return undefined;
  }

  /**
   * 获取原始地图实例和提供商信息
   * @returns 包含地图实例、提供商类型和相关工具的对象
   */
  function getRawMapInstance(): {
    instance: AMap.Map | google.maps.Map | undefined;
    provider: MapProvider | null;
    isAMap: boolean;
    isGoogleMap: boolean;
    amapInstance?: AMap.Map;
    googleMapInstance?: google.maps.Map;
  } {
    const currentProvider = provider.value;
    const instance = getMapInstance();

    return {
      instance,
      provider: currentProvider,
      isAMap: currentProvider === MapProvider.AMAP,
      isGoogleMap: currentProvider === MapProvider.GOOGLE,
      amapInstance: currentProvider === MapProvider.AMAP ? (instance as AMap.Map) : undefined,
      googleMapInstance:
        currentProvider === MapProvider.GOOGLE ? (instance as google.maps.Map) : undefined,
    };
  }

  /**
   * 执行特定于地图提供商的操作
   * @param amapCallback 高德地图回调函数
   * @param googleMapCallback Google Maps回调函数
   * @returns 回调函数的返回值
   */
  function executeProviderSpecific<T>(
    amapCallback: (map: AMap.Map) => T,
    googleMapCallback: (map: google.maps.Map) => T,
  ): T | undefined {
    const rawMap = getRawMapInstance();

    if (rawMap.isAMap && rawMap.amapInstance) {
      return amapCallback(rawMap.amapInstance);
    } else if (rawMap.isGoogleMap && rawMap.googleMapInstance) {
      return googleMapCallback(rawMap.googleMapInstance);
    }

    return undefined;
  }

  /**
   * 获取地图容器引用
   */
  function getMapRef() {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.mapRef;
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.mapRef;
    }
    return ref<HTMLDivElement | undefined>();
  }

  /**
   * 设置地图中心点
   */
  function setMapCenter(lngLat: [number, number]): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.setMapCenter(lngLat);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.setMapCenter(lngLat);
    }
    return false;
  }

  /**
   * 设置地图中心点并自适应视野
   */
  function setMapCenterFitView(lngLat: [number, number]): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.setMapCenterFitView(lngLat);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.setMapCenterFitView(lngLat);
    }
    return false;
  }

  /**
   * 统一的自适应视野方法
   * @param overlays 覆盖物数组，如果不传则自适应所有覆盖物
   * @param immediately 是否立即执行，默认 false（有动画）
   * @param avoid 四周边距 [上, 下, 左, 右]，默认 [60, 60, 60, 60]
   * @param maxZoom 最大缩放级别，默认 18
   */
  function setFitView(
    overlays?: any[],
    immediately: boolean = false,
    avoid: number[] = [60, 60, 60, 60],
    maxZoom: number = 18,
  ): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.setFitView(overlays, immediately, avoid, maxZoom);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.setFitView(overlays, immediately, avoid, maxZoom);
    }
    return false;
  }

  /**
   * 设置地图缩放级别
   * @param zoom 缩放级别
   * @param immediately 是否立即过渡到目标位置（仅高德地图支持）
   * @param duration 动画过渡时长，单位 ms（仅高德地图支持）
   */
  function setZoom(zoom: number, immediately: boolean = false, duration?: number): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.setZoom(zoom, immediately, duration);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.setZoom(zoom, immediately, duration);
    }
    return false;
  }

  /**
   * 添加标记点
   */
  function addMarker(position: MapPoint, options: UnifiedMarkerOptions = {}): UnifiedMarker | null {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      const amapOptions = convertMarkerOptions(options, MapProvider.AMAP) as AMap.MarkerOptions;
      return amapInstance.addMarker(position as AMapPoint, amapOptions);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      const gmapOptions = convertMarkerOptions(
        options,
        MapProvider.GOOGLE,
      ) as google.maps.MarkerOptions;
      return gmapInstance.addMarker(position as GMapPoint, gmapOptions);
    }
    return null;
  }

  /**
   * 添加折线
   */
  function addPolyline(
    points: MapPoint[],
    options: UnifiedPolylineOptions = {},
  ): AMap.Polyline | google.maps.Polyline | null {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      const amapOptions = convertPolylineOptions(
        options,
        MapProvider.AMAP,
      ) as Partial<AMap.PolylineOptions>;
      return amapInstance.addPolyline(points as AMapPoint[], amapOptions);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      const gmapOptions = convertPolylineOptions(
        options,
        MapProvider.GOOGLE,
      ) as Partial<google.maps.PolylineOptions>;
      return gmapInstance.addPolyline(points as GMapPoint[], gmapOptions);
    }
    return null;
  }

  /**
   * 添加多边形
   */
  function addPolygon(
    points: MapPoint[] | MapPoint[][] | MapPoint[][][],
    options: UnifiedPolygonOptions = {},
  ): AMap.Polygon | google.maps.Polygon | null {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      const amapOptions = convertPolygonOptions(options, MapProvider.AMAP) as AMap.PolygonOptions;
      return amapInstance.addPolygon(
        points as AMapPoint[] | AMapPoint[][] | AMapPoint[][][],
        amapOptions,
      );
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      const gmapOptions = convertPolygonOptions(
        options,
        MapProvider.GOOGLE,
      ) as Partial<google.maps.PolygonOptions> & { extData?: any };
      return gmapInstance.addPolygon(points as GMapPoint[] | GMapPoint[][], gmapOptions);
    }
    return null;
  }

  /**
   * 添加圆形
   */
  function addCircle(
    center: MapPoint,
    radius: number,
    options: UnifiedCircleOptions = {},
  ): AMap.Circle | google.maps.Circle | null {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      const amapOptions = convertCircleOptions(options, MapProvider.AMAP) as AMap.CircleOptions;
      return amapInstance.addCircle(center as AMapPoint, radius, amapOptions);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      const gmapOptions = convertCircleOptions(
        options,
        MapProvider.GOOGLE,
      ) as Partial<google.maps.CircleOptions> & { extData?: any };
      return gmapInstance.addCircle(center as GMapPoint, radius, gmapOptions);
    }
    return null;
  }

  /**
   * 移除多边形
   */
  function removePolygon(
    polygon: AMap.Polygon | google.maps.Polygon | (AMap.Polygon | google.maps.Polygon)[],
  ): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.removePolygon(polygon as AMap.Polygon | AMap.Polygon[]);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.removePolygon(polygon as google.maps.Polygon | google.maps.Polygon[]);
    }
    return false;
  }

  /**
   * 移除圆形
   */
  function removeCircle(
    circle: AMap.Circle | google.maps.Circle | (AMap.Circle | google.maps.Circle)[],
  ): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.removeCircle(circle as AMap.Circle | AMap.Circle[]);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.removeCircle(circle as google.maps.Circle | google.maps.Circle[]);
    }
    return false;
  }

  /**
   * 移除所有多边形
   */
  function removeAllPolygons(): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.removeAllPolygons();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.removeAllPolygons();
    }
    return false;
  }

  /**
   * 移除所有圆形
   */
  function removeAllCircles(): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.removeAllCircles();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.removeAllCircles();
    }
    return false;
  }

  /**
   * 移除所有多边形和圆形
   */
  function removeAllPolygonsAndCircles(): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.removeAllPolygonsAndCircles();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.removeAllPolygonsAndCircles();
    }
    return false;
  }

  /**
   * 获取所有多边形
   */
  function getAllPolygons(): AMap.Polygon[] | google.maps.Polygon[] {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getAllPolygons();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getAllPolygons();
    }
    return [];
  }

  /**
   * 获取所有圆形
   */
  function getAllCircles(): AMap.Circle[] | google.maps.Circle[] {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getAllCircles();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getAllCircles();
    }
    return [];
  }

  /**
   * 行政区域搜索（统一接口）
   * @param keyword 搜索关键词（行政区域名称或代码）
   * @param options 搜索选项
   */
  async function districtSearch(
    keyword: string,
    options: {
      level?: 'country' | 'province' | 'city' | 'district';
      subdistrict?: number;
      extensions?: 'base' | 'all';
    } = {},
  ): Promise<{
    status: string;
    info: string;
    districtList: Array<{
      name: string;
      center: MapPoint;
      boundaries?: MapPoint[][];
      adcode?: string;
      level: string;
    }>;
  }> {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.districtSearch(keyword, options);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      const result = await gmapInstance.districtSearch(keyword, options);
      // 转换 Google Maps 结果格式以匹配统一接口
      return {
        ...result,
        districtList: result.districtList.map((district) => ({
          ...district,
          center: district.center as MapPoint,
          boundaries: district.boundaries as MapPoint[][],
        })),
      };
    }

    return {
      status: 'error',
      info: 'Map provider not available',
      districtList: [],
    };
  }

  /**
   * 清空地图
   */
  function clearMap(): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.clearMap();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.clearMap();
    }
    return false;
  }

  /**
   * 获取地图边界
   */
  function getMapBoundary(): MapBoundary | null {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getMapBoundary();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getMapBoundary();
    }
    return null;
  }

  /**
   * 地理编码 - 根据坐标获取地址
   */
  function getAddress(lngLat: MapPoint): Promise<string> {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getAddress(lngLat as AMapPoint);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getAddress(lngLat as GMapPoint);
    }
    return Promise.reject(new Error('Map instance not available'));
  }

  /**
   * 创建信息窗口
   */
  function createInfoWindow(options: UnifiedInfoWindowOptions = {}): UnifiedInfoWindow | null {
    let rawInfoWindow: UnifiedInfoWindow | null = null;

    if (provider.value === MapProvider.AMAP && amapInstance) {
      // 处理统一参数并转换为 AMap 格式
      const amapOptions = { ...options.amapOptions };

      if (options.content) {
        amapOptions.content = options.content;
      }

      if (options.offset) {
        amapOptions.offset = convertOffsetToAMap(options.offset);
      }

      if (options.size) {
        amapOptions.size = convertSizeToAMap(options.size);
      }

      if (options.anchor) {
        amapOptions.anchor = options.anchor;
      }

      rawInfoWindow = amapInstance.createInfoWindow(amapOptions);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      // 处理统一参数并转换为 Google Maps 格式
      const gmapOptions = { ...options.gmapOptions };

      if (options.content) {
        gmapOptions.content = options.content;
      }

      if (options.offset) {
        gmapOptions.pixelOffset = convertOffsetToGoogleMaps(options.offset);
      }

      if (options.size) {
        // Google Maps 只支持 maxWidth
        const size = Array.isArray(options.size)
          ? options.size
          : [options.size.width, options.size.height];
        gmapOptions.maxWidth = size[0];
      }

      rawInfoWindow = gmapInstance.createInfoWindow(gmapOptions);
    }

    if (!rawInfoWindow) {
      return null;
    }

    // 创建统一的包装器
    const unifiedInfoWindow: UnifiedInfoWindow = {
      ...rawInfoWindow,
    };

    return unifiedInfoWindow;
  }

  /**
   * 手动触发地图对象的事件
   * @param target 目标对象（地图、marker、polyline 等）
   * @param eventType 事件类型
   * @param eventData 事件数据（可选）
   */
  function triggerEvent(target: UnifiedMapObject, eventType: string, eventData?: any): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.triggerEvent(target, eventType, eventData);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.triggerEvent(target, eventType, eventData);
    }
    return false;
  }

  /**
   * 为地图对象添加事件监听器（通用方法，支持事件参数统一）
   * @param target 目标对象（地图、marker、polyline、polygon、circle 等）
   * @param eventType 事件类型
   * @param handler 统一的事件处理函数
   */
  function addEventListener(
    target: UnifiedMapObject,
    eventType: string,
    handler: UnifiedMapEventHandler,
  ): void {
    // 创建包装函数，将原始事件转换为统一格式
    const wrappedHandler = (originalEvent: any) => {
      let unifiedEvent: UnifiedMapEvent;

      if (provider.value === MapProvider.AMAP) {
        unifiedEvent = convertAMapEvent(originalEvent, target, eventType);
      } else {
        unifiedEvent = convertGoogleMapsEvent(originalEvent, target, eventType);
      }

      handler(unifiedEvent);
    };

    // 保存映射关系
    universalEventHandlerManager.setWrappedHandler(target, eventType, handler, wrappedHandler);

    // 调用原始的 addEventListener
    if (provider.value === MapProvider.AMAP && amapInstance) {
      amapInstance.addEventListener(target, eventType, wrappedHandler);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      gmapInstance.addEventListener(target, eventType, wrappedHandler);
    }
  }

  /**
   * 移除地图对象的事件监听器（通用方法，支持事件参数统一）
   * @param target 目标对象（地图、marker、polyline、polygon、circle 等）
   * @param eventType 事件类型
   * @param handler 统一的事件处理函数（可选，不传则移除该事件类型的所有监听器）
   */
  function removeEventListener(
    target: UnifiedMapObject,
    eventType: string,
    handler?: UnifiedMapEventHandler,
  ): void {
    if (handler) {
      // 获取包装函数
      const wrappedHandler = universalEventHandlerManager.getWrappedHandler(
        target,
        eventType,
        handler,
      );

      if (wrappedHandler) {
        // 调用原始的 removeEventListener
        if (provider.value === MapProvider.AMAP && amapInstance) {
          amapInstance.removeEventListener(target, eventType, wrappedHandler as any);
        } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
          gmapInstance.removeEventListener(target, eventType, wrappedHandler as any);
        }

        // 移除映射关系
        universalEventHandlerManager.removeWrappedHandler(target, eventType, handler);
      }
    } else {
      // 如果没有指定 handler，移除该事件类型的所有监听器
      if (provider.value === MapProvider.AMAP && amapInstance) {
        amapInstance.removeEventListener(target, eventType);
      } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
        gmapInstance.removeEventListener(target, eventType);
      }
    }
  }

  /**
   * 销毁地图
   */
  function destroy(): void {
    if (amapInstance) {
      amapInstance.destroy();
      amapInstance = null;
    }
    if (gmapInstance) {
      gmapInstance.destroy();
      gmapInstance = null;
    }
    provider.value = null;
  }

  /**
   * 手动切换地图提供商
   */
  async function switchProvider(newProvider: MapProvider): Promise<void> {
    if (provider.value === newProvider) {
      return;
    }

    // 销毁当前地图实例
    destroy();

    // 设置新的提供商
    manager.setProvider(newProvider);

    // 重新初始化
    await initializeMap();
  }

  /**
   * 添加统一控件
   * @param controlId 控件唯一标识
   * @param config 统一控件配置
   */
  async function addControl(controlId: string, config: UnifiedControlConfig): Promise<any> {
    if (provider.value === MapProvider.AMAP && amapInstance && config.amapConfig) {
      return amapInstance.addControl(controlId, config.amapConfig);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance && config.gmapConfig) {
      return gmapInstance.addControl(controlId, config.gmapConfig);
    }
    throw new Error(
      `Cannot add control: provider ${provider.value} not available or config missing`,
    );
  }

  /**
   * 移除统一控件
   * @param controlId 控件唯一标识
   */
  function removeControl(controlId: string): boolean {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.removeControl(controlId);
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.removeControl(controlId);
    }
    console.warn(`Cannot remove control: provider ${provider.value} not available`);
    return false;
  }

  /**
   * 添加预定义的统一控件
   * @param controlName 预定义控件名称
   * @param customOptions 自定义选项（可选）
   */
  async function addPredefinedControl(
    controlName: keyof typeof DEFAULT_UNIFIED_CONTROL_CONFIGS,
    customOptions?: Partial<UnifiedControlConfig>,
  ): Promise<any> {
    const defaultConfig = DEFAULT_UNIFIED_CONTROL_CONFIGS[controlName];
    if (!defaultConfig) {
      throw new Error(`Predefined control ${controlName} not found`);
    }

    const config: UnifiedControlConfig = {
      type: defaultConfig.type,
      position: customOptions?.position || defaultConfig.position,
      options: customOptions?.options || defaultConfig.options,
      amapConfig: {
        ...defaultConfig.amapConfig,
        ...customOptions?.amapConfig,
      } as AMapControlConfig,
      gmapConfig: {
        ...defaultConfig.gmapConfig,
        ...customOptions?.gmapConfig,
      } as GMapControlConfig,
    };

    return addControl(controlName, config);
  }

  /**
   * 批量添加统一控件
   * @param controlConfigs 控件配置数组
   */
  async function addMultipleControls(
    controlConfigs: Array<{ id: string; config: UnifiedControlConfig }>,
  ): Promise<Array<{ id: string; control?: any; error?: any; success: boolean }>> {
    const results: Array<{ id: string; control?: any; error?: any; success: boolean }> = [];
    for (const { id, config } of controlConfigs) {
      try {
        const control = await addControl(id, config);
        results.push({ id, control, success: true });
      } catch (error) {
        console.error(`Failed to add unified control ${id}:`, error);
        results.push({ id, error, success: false });
      }
    }
    return results;
  }

  /**
   * 清除所有控件
   */
  function clearAllControls(): void {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      amapInstance.clearAllControls();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      gmapInstance.clearAllControls();
    }
  }

  /**
   * 获取已添加的控件列表
   */
  function getControls(): Map<string, any> {
    if (provider.value === MapProvider.AMAP && amapInstance) {
      return amapInstance.getControls();
    } else if (provider.value === MapProvider.GOOGLE && gmapInstance) {
      return gmapInstance.getControls();
    }
    return new Map();
  }

  /**
   * 添加高德地图特有的工具栏控件
   */
  async function addToolBar(options?: any): Promise<any> {
    if (provider.value !== MapProvider.AMAP || !amapInstance) {
      throw new Error('ToolBar control is only available for AMap');
    }
    return amapInstance.addPredefinedControl('toolbar', { options });
  }

  /**
   * 添加高德地图特有的控制栏控件
   */
  async function addControlBar(options?: any): Promise<any> {
    if (provider.value !== MapProvider.AMAP || !amapInstance) {
      throw new Error('ControlBar control is only available for AMap');
    }
    return amapInstance.addPredefinedControl('controlbar', { options });
  }

  /**
   * 添加高德地图特有的鹰眼控件
   */
  async function addHawkEye(options?: any): Promise<any> {
    if (provider.value !== MapProvider.AMAP || !amapInstance) {
      throw new Error('HawkEye control is only available for AMap');
    }
    return amapInstance.addPredefinedControl('hawkeye', { options });
  }

  /**
   * 添加地图类型控件（通用）
   */
  async function addMapType(options?: any): Promise<any> {
    return addPredefinedControl('maptype', { options });
  }

  // 初始化地图
  onMounted(() => {
    initializeMap();
  });

  return {
    containerRef,
    provider,
    isLoading,
    error,
    mapRef: getMapRef(),
    getMapInstance,
    getAMapInstance,
    getGoogleMapInstance,
    getRawMapInstance,
    executeProviderSpecific,
    initializeMap,
    setMapCenter,
    setMapCenterFitView,
    setFitView,
    setZoom,
    addMarker,
    addPolyline,
    addPolygon,
    addCircle,
    removePolygon,
    removeCircle,
    removeAllPolygons,
    removeAllCircles,
    removeAllPolygonsAndCircles,
    getAllPolygons,
    getAllCircles,
    districtSearch,
    startDrawPolygon,
    startDrawCircle,
    stopDrawing,
    clearDrawingOverlays,
    getDrawingOverlays,
    getDrawingData,
    initializeDrawingTool,
    setCity,
    clearMap,
    getMapBoundary,
    getAddress,
    createInfoWindow,
    destroy,
    switchProvider,
    // 事件管理方法
    addEventListener,
    removeEventListener,
    triggerEvent,
    // 统一控件管理方法
    addControl,
    removeControl,
    addPredefinedControl,
    addMultipleControls,
    clearAllControls,
    getControls,
    // 高德地图特有控件方法
    addToolBar,
    addControlBar,
    addHawkEye,
    // 通用控件方法
    addMapType,
    // 轨迹回放方法
    startMoveAnimation,
    stopMoveAnimation,
    getCarIcon,
  };
}

/**
 * 统一的地理编码Hook
 */
export function useGeoCoder(detectionOptions?: GeoDetectionOptions) {
  const provider = ref<MapProvider | null>(null);
  const isLoading = ref(true);
  const error = ref<string | null>(null);

  let amapGeocoderInstance: ReturnType<typeof useAGeoCoder> | null = null;
  let gmapGeocoderInstance: ReturnType<typeof useGGeoCoder> | null = null;

  const manager = MapProviderManager.getInstance();

  /**
   * 初始化地理编码器
   */
  async function initializeGeocoder(): Promise<void> {
    try {
      isLoading.value = true;
      error.value = null;

      const detectedProvider = await manager.detectProvider(detectionOptions);
      provider.value = detectedProvider;

      if (detectedProvider === MapProvider.AMAP) {
        // 创建 AMap geocoder 实例
        amapGeocoderInstance = useAGeoCoder();
        // 手动调用初始化方法，因为 onMounted 不会在这里触发
        if (amapGeocoderInstance.initGeocoder) {
          const initSuccess = amapGeocoderInstance.initGeocoder();
          if (!initSuccess) {
            throw new Error('Failed to initialize AMap geocoder');
          }
        }
      } else {
        // 创建 Google Maps geocoder 实例
        gmapGeocoderInstance = useGGeoCoder();
        // 手动调用初始化方法，因为 onMounted 不会在这里触发
        if (gmapGeocoderInstance.initGeocoder) {
          const initSuccess = gmapGeocoderInstance.initGeocoder();
          if (!initSuccess) {
            throw new Error('Failed to initialize Google Maps geocoder');
          }
        }
      }

      isLoading.value = false;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to initialize geocoder';
      isLoading.value = false;
      console.error('Geocoder initialization failed:', err);
    }
  }

  // 自动初始化
  initializeGeocoder();

  /**
   * 手动初始化地理编码器（用于自动初始化失败的情况）
   */
  async function manualInitializeGeocoder(): Promise<void> {
    console.log('手动初始化地理编码器...');
    await initializeGeocoder();
  }

  /**
   * 地理编码 - 根据坐标获取地址
   */
  function getAddress(lngLat: [number, number]): Promise<string> {
    console.log(
      'get address instance: ',
      provider.value === MapProvider.GOOGLE,
      gmapGeocoderInstance,
    );

    if (!provider.value) {
      return Promise.reject(
        new Error('Geocoder not initialized. Please call initializeGeocoder() first.'),
      );
    }

    if (provider.value === MapProvider.AMAP && amapGeocoderInstance) {
      return amapGeocoderInstance.getAddress(lngLat);
    } else if (provider.value === MapProvider.GOOGLE && gmapGeocoderInstance) {
      return gmapGeocoderInstance.getAddress(lngLat);
    }
    return Promise.reject(new Error('Geocoder instance not available'));
  }

  /**
   * 检查地理编码器是否已初始化
   */
  function isGeocoderReady(): boolean {
    if (!provider.value) return false;

    if (provider.value === MapProvider.AMAP) {
      return amapGeocoderInstance?.isInitialized?.value === true;
    } else if (provider.value === MapProvider.GOOGLE) {
      return gmapGeocoderInstance?.isInitialized?.value === true;
    }

    return false;
  }

  return {
    provider,
    isLoading,
    error,
    getAddress,
    initializeGeocoder,
    manualInitializeGeocoder,
    isGeocoderReady,
  };
}

// 导出地图提供商管理器，供外部使用
export { MapProviderManager };
