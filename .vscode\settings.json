{"i18n-ally.localesPaths": ["lang", "src/locales", "src/locales/lang", "public/resource/tinymce/langs"], "typescript.tsdk": "./node_modules/typescript/lib", "npm.packageManager": "pnpm", "editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode", "files.eol": "\n", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "never"}, "[vue]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.gotoLocation.multipleDefinitions": "goto", "terminal.integrated.scrollback": 10000}