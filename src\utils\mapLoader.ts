/**
 * 地图API动态加载工具
 */

export interface GoogleMapsConfig {
  apiKey: string;
  libraries?: string[];
  language?: string;
  region?: string;
  version?: string;
}

export interface AMapConfig {
  apiKey: string;
  version?: string;
  plugins?: string[];
  securityJsCode?: string;
}

/**
 * Google Maps API加载状态
 */
enum GoogleMapsLoadState {
  NOT_LOADED = 'not_loaded',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error',
}

/**
 * 高德地图API加载状态
 */
enum AMapLoadState {
  NOT_LOADED = 'not_loaded',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error',
}

class MapAPILoader {
  private static instance: MapAPILoader;
  private googleMapsState: GoogleMapsLoadState = GoogleMapsLoadState.NOT_LOADED;
  private amapState: AMapLoadState = AMapLoadState.NOT_LOADED;
  private googleMapsPromise: Promise<void> | null = null;
  private amapPromise: Promise<void> | null = null;

  static getInstance(): MapAPILoader {
    if (!MapAPILoader.instance) {
      MapAPILoader.instance = new MapAPILoader();
    }
    return MapAPILoader.instance;
  }

  /**
   * 加载Google Maps API
   */
  async loadGoogleMaps(config: GoogleMapsConfig): Promise<void> {
    if (this.googleMapsState === GoogleMapsLoadState.LOADED) {
      return Promise.resolve();
    }

    if (this.googleMapsState === GoogleMapsLoadState.LOADING && this.googleMapsPromise) {
      return this.googleMapsPromise;
    }

    if (this.googleMapsState === GoogleMapsLoadState.ERROR) {
      throw new Error('Google Maps API failed to load previously');
    }

    this.googleMapsState = GoogleMapsLoadState.LOADING;
    this.googleMapsPromise = this.loadGoogleMapsScript(config);

    try {
      await this.googleMapsPromise;
      this.googleMapsState = GoogleMapsLoadState.LOADED;
    } catch (error) {
      this.googleMapsState = GoogleMapsLoadState.ERROR;
      throw error;
    }
  }

  private loadGoogleMapsScript(config: GoogleMapsConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载
      if (window.google && window.google.maps) {
        resolve();
        return;
      }

      // 构建URL参数
      const params = new URLSearchParams({
        key: config.apiKey,
        callback: '__googleMapsCallback',
      });

      if (config.libraries && config.libraries.length > 0) {
        params.append('libraries', config.libraries.join(','));
      }

      if (config.language) {
        params.append('language', config.language);
      }

      if (config.region) {
        params.append('region', config.region);
      }

      if (config.version) {
        params.append('v', config.version);
      }

      // 设置全局回调函数
      (window as any).__googleMapsCallback = () => {
        delete (window as any).__googleMapsCallback;
        resolve();
      };

      // 创建script标签
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?${params.toString()}`;
      script.async = true;
      script.defer = true;

      script.onerror = () => {
        delete (window as any).__googleMapsCallback;
        reject(new Error('Failed to load Google Maps API'));
      };

      // 添加到页面
      document.head.appendChild(script);
    });
  }

  /**
   * 加载高德地图API
   */
  async loadAMap(config: AMapConfig): Promise<void> {
    if (this.amapState === AMapLoadState.LOADED) {
      return Promise.resolve();
    }

    if (this.amapState === AMapLoadState.LOADING && this.amapPromise) {
      return this.amapPromise;
    }

    if (this.amapState === AMapLoadState.ERROR) {
      throw new Error('AMap API failed to load previously');
    }

    this.amapState = AMapLoadState.LOADING;
    this.amapPromise = this.loadAMapScript(config);

    try {
      await this.amapPromise;
      this.amapState = AMapLoadState.LOADED;
    } catch (error) {
      this.amapState = AMapLoadState.ERROR;
      throw error;
    }
  }

  private loadAMapScript(config: AMapConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载
      if (window.AMap) {
        resolve();
        return;
      }

      // 设置安全密钥
      if (config.securityJsCode) {
        (window as any)._AMapSecurityConfig = {
          securityJsCode: config.securityJsCode,
        };
      }

      // 构建URL参数
      const params = new URLSearchParams({
        key: config.apiKey,
        v: config.version || '2.0',
      });

      if (config.plugins && config.plugins.length > 0) {
        params.append('plugin', config.plugins.join('&plugin='));
      }

      // 创建script标签
      const script = document.createElement('script');
      script.src = `https://webapi.amap.com/maps?${params.toString()}`;
      script.async = true;

      script.onload = () => {
        // 高德地图加载完成后检查AMap对象
        if (window.AMap) {
          resolve();
        } else {
          reject(new Error('AMap object not found after script load'));
        }
      };

      script.onerror = () => {
        reject(new Error('Failed to load AMap API'));
      };

      // 添加到页面
      document.head.appendChild(script);
    });
  }

  /**
   * 检查Google Maps是否已加载
   */
  isGoogleMapsLoaded(): boolean {
    return (
      this.googleMapsState === GoogleMapsLoadState.LOADED && !!(window.google && window.google.maps)
    );
  }

  /**
   * 检查高德地图是否已加载
   */
  isAMapLoaded(): boolean {
    return this.amapState === AMapLoadState.LOADED && !!window.AMap;
  }

  /**
   * 重置加载状态
   */
  reset(): void {
    this.googleMapsState = GoogleMapsLoadState.NOT_LOADED;
    this.amapState = AMapLoadState.NOT_LOADED;
    this.googleMapsPromise = null;
    this.amapPromise = null;
  }
}

/**
 * 默认配置
 */
export const DEFAULT_GOOGLE_MAPS_CONFIG: GoogleMapsConfig = {
  apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '', // 从环境变量获取
  libraries: ['geometry', 'places'],
  language: 'zh-CN',
  region: 'CN',
  version: 'weekly',
};

export const DEFAULT_AMAP_CONFIG: AMapConfig = {
  apiKey: import.meta.env.VITE_AMAP_API_KEY || '8daa33fd0168182c44c29076ce995985',
  version: '2.0',
  plugins: [
    'AMap.Geocoder',
    'AMap.MouseTool',
    'AMap.Autocomplete',
    'AMap.PolyEditor',
    'AMap.MarkerClusterer',
  ],
  securityJsCode: import.meta.env.VITE_AMAP_SECURITY_JS_CODE || '0a93e982b376824d33c84f11e8751db6',
};

/**
 * 便捷的加载函数
 */
export async function loadGoogleMaps(config?: Partial<GoogleMapsConfig>): Promise<void> {
  const loader = MapAPILoader.getInstance();
  const finalConfig = { ...DEFAULT_GOOGLE_MAPS_CONFIG, ...config };

  if (!finalConfig.apiKey) {
    throw new Error('Google Maps API key is required');
  }

  return loader.loadGoogleMaps(finalConfig);
}

export async function loadAMap(config?: Partial<AMapConfig>): Promise<void> {
  const loader = MapAPILoader.getInstance();
  const finalConfig = { ...DEFAULT_AMAP_CONFIG, ...config };
  return loader.loadAMap(finalConfig);
}

export function isGoogleMapsLoaded(): boolean {
  return MapAPILoader.getInstance().isGoogleMapsLoaded();
}

export function isAMapLoaded(): boolean {
  return MapAPILoader.getInstance().isAMapLoaded();
}

// 导出加载器实例
export { MapAPILoader };
