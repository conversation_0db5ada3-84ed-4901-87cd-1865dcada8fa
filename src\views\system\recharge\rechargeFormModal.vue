<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-26 10:27:26
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-26 16:16:58
 * @FilePath     : \tzlink-gps-web\src\views\system\recharge\rechargeFormModal.vue
 * Copyright (C) 2025 chen. All rights reserved.
-->
<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { formSchema } from './package.data';
  import { Table, Button, InputNumber, Input } from 'ant-design-vue';
  import { addRecharge, editRecharge } from '@/api/passport/recharge';

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref<number | undefined>(undefined)

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    dataSource.value = [];
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      setFieldsValue({
        ...data.record,
      });
      dataSource.value = data.record.params;
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增套餐' : '编辑套餐'));

  const columns = ref([
    {
      title: '参数名',
      dataIndex: 'priceCode',
    },
    {
      title: '价格',
      dataIndex: 'priceValue',
    },
    {
      title: '操作',
      dataIndex: 'actions',
    },
  ]);
  const dataSource: any = ref([]);
  //添加
  const addPrice = () => {
    dataSource.value.push({
      priceCode: '',
      priceValue: '',
    });
  };
  //删除
  const delPackage = (index: number) => {
    dataSource.value.splice(index, 1);
  };
  async function handleSubmit() {
    try {
      let values = await validate();
      values.params = dataSource.value;
      setModalProps({ confirmLoading: true });
      if (unref(isUpdate)) {
        await editRecharge(rowId.value, values);
      } else {
        await addRecharge(values);
      }
      closeModal()
      emit('success', {
        isUpdate: unref(isUpdate),
        values: { ...values, id: rowId.value }
      });
    } finally {
      setModalProps({ confirmLoading: false })
    }
  }

</script>

<template>
  <BasicModal
    width="800px"
    v-bind="$attrs"
    :title="getTitle"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
    <div class="actions">
      <Button type="primary" size="small" @click="addPrice">添加</Button>
    </div>
    <Table :columns="columns" :data-source="dataSource" :pagination="false">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'priceCode'">
          <Input v-model:value="record.priceCode" placeholder="请输入" />
        </template>
        <template v-if="column.dataIndex === 'priceValue'">
          <InputNumber
            :min="0"
            :step="0.01"
            :formatter="value => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
            v-model:value="record.priceValue"
            placeholder="请输入"
          />
        </template>

        <template v-if="column.dataIndex === 'actions'">
          <Button type="text" danger size="small" @click="delPackage(index)">删除</Button>
        </template>
      </template>
    </Table>
  </BasicModal>
</template>
<style lang="less" scoped>
  .actions {
    text-align: right;
    margin: 10px 0px;
  }
</style>
