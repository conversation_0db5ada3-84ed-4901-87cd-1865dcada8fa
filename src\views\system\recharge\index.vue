<template>
  <div class="h-full">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增套餐 </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              tooltip: '编辑',
              icon: 'clarity:note-edit-line',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              tooltip: '删除',
              color: 'error',
              popConfirm: {
                title: '是否确认删除',
                confirm: handleDelete.bind(null, record),
              },
            },
            {
              tooltip: '设为默认',
              icon: 'icon-park-twotone:setting-web',
              popConfirm: {
                title: record.orgId
                  ? '当前套餐已绑定机构，是否确认设为默认'
                  : '是否确认设为默认套餐',
                confirm: handleSetDefault.bind(null, record),
              },
            },
          ]"
        />
        <!-- onClick: handleSetDefault.bind(null, record), -->
      </template>
    </BasicTable>
    <RechargeFormModal @register="registerFormModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';

  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getRechargeList, removeRecharge, setDefaultRecharge } from '@/api/passport/recharge';
  import { useMessage } from '@/hooks/web/useMessage';

  import { useModal } from '@/components/Modal';
  import RechargeFormModal from './rechargeFormModal.vue';

  import { useDrawer } from '@/components/Drawer';

  import { columns, searchFormSchema } from './package.data';

  export default defineComponent({
    name: 'RoleList',
    components: { BasicTable, TableAction, RechargeFormModal },
    setup() {
      const [registerTable, { reload }] = useTable({
        title: '套餐列表',
        api: getRechargeList,
        columns,
        formConfig: {
          labelAlign: 'left',
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
          slot: 'action',
          fixed: undefined,
        },
      });

      const [registerFormModal, { openModal: openFormModal }] = useModal();

      const [registerDrawer, { openDrawer }] = useDrawer();

      function handleCreate() {
        openFormModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        console.log(record);
        openFormModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        removeRecharge(record?.id).then((res) => {
          reload();
        });
      }
      //设为默认
      function handleSetDefault(record: Recordable) {
        setDefaultRecharge(record?.id).then((res) => {
          reload();
        });
      }

      function handleSuccess() {
        reload();
      }

      return {
        registerTable,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
        handleSetDefault,
        registerFormModal,
        registerDrawer,
      };
    },
  });
</script>