/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:04
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-30 15:14:58
 * @FilePath     : \tzlink-gps-web\src\api\data\simCard.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { defHttp as request } from '@/utils/http/axios';
/**
 * @description: sim卡列表
 */
export function getsimList(data) {
  return request.post({
    url: `/sim/query`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 新建sim卡
 */
export function addSim(data) {
  return request.post({
    url: `/sim`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 编辑sim卡
 */
export function editSim(id, data) {
  return request.put({
    url: `/sim/${id}`,
    data,
    // permission: 'vendor-edit'
  });
}

/**
 * @description: 删除sim卡
 */
export function removeSim(id) {
  return request.delete({
    url: `/sim/${id}`,
    // permission: 'vendor-remove'
  });
}
/**
 * @description: 上传验证
 */
export function importValidate(data) {
  return request.post({
    url: `/sim/import/validate`,
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}