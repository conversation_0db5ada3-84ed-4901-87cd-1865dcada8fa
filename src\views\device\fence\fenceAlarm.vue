<template>
  <div class="flex flex-col">
    <div class="h-10 w-full p-2">
      <Tabs v-model:activeKey="activeName" @change="handleTabChange">
        <TabPane tab="实时报警" key="current" />
        <TabPane tab="历史报警" key="history" />
      </Tabs>
    </div>
    <BasicTable class="flex-1" @register="registerTable">
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>
      <TableAction
        :actions="[
          // {
          //   icon: 'flowbite:link-break-outline',
          //   color: 'error',
          //   popConfirm: {
          //     title: '是否确认解绑该设备',
          //     confirm: handleDelete.bind(null, record),
          //   },
          // },
        ]"
      />
    </BasicTable>

    <AlarmHistory @register="registerModal" />
  </div>
</template>
<script lang="tsx" setup>
  import { ref } from 'vue';
  import { Tabs, TabPane, Tag } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getFenceAlarmList } from '@/api/vehicle/fence';
  import { useModal } from '@/components/Modal';
  import { useAMap } from '@/hooks/web/useAMap';
  import AlarmHistory from './alarmHistory.vue';

  const activeName = ref('current');
  const [registerModal, { openModal }] = useModal();
  const { batchGetAddress } = useAMap({});

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '报警类型',
      dataIndex: 'alertEvent',
      customRender: ({ record }) => {
        return record.alertEvent === 'OUT' ? (
          <Tag color={'orange'}>驶出</Tag>
        ) : (
          <Tag color={'green'}>驶入</Tag>
        );
      },
    },
    {
      title: '报警围栏',
      dataIndex: 'fenceName',
    },
    {
      title: '开始时间',
      dataIndex: 'alertStart',
    },
    {
      title: '结束时间',
      dataIndex: 'alertEnd',
    },
    {
      title: '持续时间(h)',
      dataIndex: 'continueTime',
    },
    {
      title: '位置',
      dataIndex: 'address',
      width: 250,
    },
    {
      title: '越界历史',
      dataIndex: 'historyCount',
      fixed: 'right',
      customRender: ({ record }) => {
        return (
          <a href="javascript:void(0)" onClick={handleHistory.bind(null, record)}>
            {record.historyCount}
          </a>
        );
      },
      ifShow: () => activeName.value === 'current',
    },
  ];

  // tabs切换
  const handleTabChange = (activeKey) => {
    reload();
  };

  const handleHistory = (record) => {
    openModal(true, {
      isActual: 2,
      ...record,
    });
  };

  const [registerTable, { reload, getDataSource }] = useTable({
    api: getFenceAlarmList,
    columns,
    useSearchForm: true,
    formConfig: {
      labelAlign: 'left',
      schemas: [
        {
          field: 'criteria.keyword',
          component: 'Input',
          label: '关键字',
          colProps: { span: 6 },
        },
        {
          field: 'criteria.dataRange',
          component: 'RangePicker',
          label: '时间范围',
          colProps: { span: 6 },
          componentProps: {
            format: 'YYYY-MM-DD HH:mm:ss',
            showTime: true,
          },
        },
      ],
    },
    showTableSetting: true,
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    maxHeight: 500,
    rowKey: 'id',
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      slot: 'action',
      fixed: 'right',
    },
    beforeFetch: ({ criteria }) => {
      if (activeName.value === 'current') {
        criteria.isActual = 1;
      } else {
        criteria.isActual = 2;
      }
      if (criteria.dataRange && criteria.dataRange.length) {
        criteria.alertDateTimeRange = {
          beginTime: criteria.dataRange[0],
          endTime: criteria.dataRange[1],
        };
        delete criteria.dataRange;
      }
    },
    afterFetch: (data) => {
      return Promise.all(
        data.map(async (item) =>
          item.wgs84Lng && item.wgs84Lat
            ? batchGetAddress(item, ['wgs84Lat', 'wgs84Lng']).then((res) => ({ ...item, address: res.address }))
            : item,
        ),
      );
    },
  });
</script>
<style lang="less" scoped>
.ant-tabs{
  padding-left:8px;
}
.ant-form-item .ant-form-item-label{
    width: auto !important
  }
</style>

