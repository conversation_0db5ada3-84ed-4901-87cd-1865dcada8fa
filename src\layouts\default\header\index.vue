<template>
  <Layout.Header :class="getHeaderClass">
    <!-- <div :style="getWrapStyleIn"></div> -->
    <!-- left start -->
    <div :class="`${prefixCls}-left`">
      <!-- logo -->
      <AppLogo
        v-if="getShowHeaderLogo || getIsMobile"
        :class="`${prefixCls}-logo`"
        :theme="getHeaderTheme"
        :style="getLogoWidth"
      />
      <LayoutTrigger
        v-if="
          ((getShowContent && getShowHeaderTrigger && !getSplit && !getIsMixSidebar) ||
            getIsMobile) &&
          userStore.getUserInfo.memberType !== 'User'
        "
        :theme="getHeaderTheme"
        :sider="false"
      />
      <!-- <LayoutBreadcrumb v-if="getShowContent && getShowBread" :theme="getHeaderTheme" /> -->
      <img src="/logo.png" style="height: 32px; margin-left: 7px;"/>
      <h3 class="text-gray-900 my-0 ml-2 text-xl font-bold" style="color: #eee">{{
        projectTitle
      }}</h3>
    </div>
    <!-- left end -->

    <!-- menu start -->
    <div v-if="getShowTopMenu && !getIsMobile" :class="`${prefixCls}-menu`">
      <LayoutMenu
        :isHorizontal="true"
        :theme="getHeaderTheme"
        :splitType="getSplitType"
        :menuMode="getMenuMode"
      />
    </div>
    <!-- menu-end -->

    <!-- action  -->
    <div :class="`${prefixCls}-action justify-end`">
      <AppSearch v-if="getShowSearch" :class="`${prefixCls}-action__item `" />

      <ErrorAction v-if="getUseErrorHandle" :class="`${prefixCls}-action__item error-action`" />

      <!-- <Notify v-if="getShowNotice" :class="`${prefixCls}-action__item notify-item`" /> -->
      <Button type="primary" @click="router.push({ path: '/system/payment' })">充值续费</Button>
      <Select
        v-model:value="langType"
        @change="handleChange"
        style="width: 100px; margin: 0px 10px"
      >
        <SelectOption value="zh-cn">简体中文</SelectOption>
        <SelectOption value="tw">繁体中文</SelectOption>
        <SelectOption value="ar">阿拉伯语</SelectOption>
        <SelectOption value="en">英语</SelectOption>
        <SelectOption value="ja">日语</SelectOption>
        <SelectOption value="ru">俄语</SelectOption>
        <SelectOption value="ko">韩语</SelectOption>
      </Select>

      <FullScreen v-if="getShowFullScreen" :class="`${prefixCls}-action__item fullscreen-item`" />

      <!-- <AppLocalePicker
        v-if="getShowLocalePicker"
        :reload="true"
        :showText="false"
        :class="`${prefixCls}-action__item`"
      /> -->

      <UserDropDown :theme="getHeaderTheme" />

      <SettingDrawer v-if="getShowSetting" :class="`${prefixCls}-action__item`" />
    </div>
  </Layout.Header>
</template>
<script lang="ts" setup>
  import { Layout, Select, SelectOption,on,Button } from 'ant-design-vue';
  import { computed, unref, ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    // AppLocalePicker,
    AppLogo,
    AppSearch,
  } from '@/components/Application';
  import { SettingButtonPositionEnum } from '@/enums/appEnum';
  import { MenuModeEnum, MenuSplitTyeEnum } from '@/enums/menuEnum';
  import { useHeaderSetting } from '@/hooks/setting/useHeaderSetting';
  import { useMenuSetting } from '@/hooks/setting/useMenuSetting';
  import { useRootSetting } from '@/hooks/setting/useRootSetting';
  import { useAppInject } from '@/hooks/web/useAppInject';
  import { useDesign } from '@/hooks/web/useDesign';
  // import { useLocale } from '@/locales/useLocale';
  import { createAsyncComponent } from '@/utils/factory/createAsyncComponent';
  import { propTypes } from '@/utils/propTypes';

  import LayoutMenu from '../menu/index.vue';
  import LayoutTrigger from '../trigger/index.vue';
  import {
    ErrorAction,
    FullScreen,
    //  LayoutBreadcrumb,
    // Notify,
    UserDropDown,
  } from './components';
  import { useUserStore } from '@/store/modules/user';
  
  const route = useRoute();
  const router = useRouter();
  const langType: any = ref('zh-cn');
  onMounted(() => {
    langType.value = window.localStorage.getItem('lang') || 'zh-cn';
  });
  const getWrapStyleIn = computed((): CSSProperties => {
    return `width: ${getWidth.value}px`;
  }); 
  const handleChange = () => {
    window.localStorage.setItem('lang', langType.value);
    window.location.reload();
  };
  const SettingDrawer = createAsyncComponent(() => import('@/layouts/default/setting/index.vue'), {
    loading: true,
  });
  defineOptions({ name: 'LayoutHeader' });
  const projectTitle = ref('');
  projectTitle.value = '星联物联网';
  const props = defineProps({
    fixed: propTypes.bool,
  });
  const { prefixCls } = useDesign('layout-header');
  const {
    getShowTopMenu,
    getShowHeaderTrigger,
    getSplit,
    getIsMixMode,
    getMenuWidth,
    getIsMixSidebar,
    getWidth
  } = useMenuSetting();
  const { getUseErrorHandle, getShowSettingButton, getSettingButtonPosition } = useRootSetting();

  const {
    getHeaderTheme,
    getShowFullScreen,
    // getShowNotice,
    getShowContent,
    // getShowBread,
    getShowHeaderLogo,
    getShowHeader,
    getShowSearch,
  } = useHeaderSetting();

  // const { getShowLocalePicker } = useLocale();

  const { getIsMobile } = useAppInject();

  const getHeaderClass = computed(() => {
    const theme = unref(getHeaderTheme);
    return [
      prefixCls,
      {
        [`${prefixCls}--fixed`]: props.fixed,
        [`${prefixCls}--mobile`]: unref(getIsMobile),
        [`${prefixCls}--${theme}`]: theme,
      },
    ];
  });

  const getShowSetting = computed(() => {
    if (!unref(getShowSettingButton)) {
      return false;
    }
    const settingButtonPosition = unref(getSettingButtonPosition);

    if (settingButtonPosition === SettingButtonPositionEnum.AUTO) {
      return unref(getShowHeader);
    }
    return settingButtonPosition === SettingButtonPositionEnum.HEADER;
  });

  const getLogoWidth = computed(() => {
    if (!unref(getIsMixMode) || unref(getIsMobile)) {
      return {};
    }
    const width = unref(getMenuWidth) < 180 ? 180 : unref(getMenuWidth);
    return { width: `${width}px` };
  });

  const getSplitType = computed(() => {
    return unref(getSplit) ? MenuSplitTyeEnum.TOP : MenuSplitTyeEnum.NONE;
  });

  const getMenuMode = computed(() => {
    return unref(getSplit) ? MenuModeEnum.HORIZONTAL : null;
  });

  const userStore = useUserStore();
</script>
<style lang="less">
  @import url('./index.less');
</style>
