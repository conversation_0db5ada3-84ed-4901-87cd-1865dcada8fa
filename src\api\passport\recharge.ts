/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-19 15:38:04
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-20 15:05:05
 * @FilePath     : \special-front\src\api\passport\Recharge.ts
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { defHttp as request } from '@/utils/http/axios';
/**
 * @description: 套餐列表
 */
export function getRechargeList(data) {
  return request.post({
    url: `/devicePaymentPackage/query`,
    data,
    // permission: 'Recharge-add'
  });
}

/**
 * @description: 新建套餐
 */
export function addRecharge(data) {
  return request.post({
    url: `/devicePaymentPackage`,
    data,
    // permission: 'Recharge-add'
  });
}

/**
 * @description: 编辑套餐
 */
export function editRecharge(id, data) {
  return request.put({
    url: `/devicePaymentPackage/${id}`,
    data,
    // permission: 'Recharge-edit'
  });
}

/**
 * @description: 删除套餐
 */
export function removeRecharge(id) {
  return request.delete({
    url: `/devicePaymentPackage/${id}`,
    // permission: 'Recharge-remove'
  });
}
/**
 * @description: 设置默认套餐
 */
export function setDefaultRecharge(id) {
  return request.put({
    url: `/devicePaymentPackage/set/${id}`,
    // permission: 'Recharge-remove'
  });
}
