<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-29 09:41:52
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-29 16:25:41
 * @FilePath     : \special-front\src\views\vehicle\vehlist\status.vue
 * Copyright (C) 2024 chen. All rights reserved.
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'vin'">
          <a @click="lookInfo(record)">
            <span> {{ record.vin }} </span>
          </a>
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts">
  import { defineComponent, onMounted } from 'vue';
  import { Tag } from 'ant-design-vue';
  import { BasicTable, useTable } from '@/components/Table';
  import { getDeviceList } from '@/api/vehicle/vehlist';
  import { statiusColumns as columns, searchFormSchema } from './vehicle.data';
  import { router } from '@/router';
  import { useGeoCoder } from '@/hooks/web/useMap';

  export default defineComponent({
    name: 'VehicleList',
    components: { BasicTable, Tag },
    setup() {
      const { getAddress, manualInitializeGeocoder } = useGeoCoder({});
      onMounted(async () => {
        await manualInitializeGeocoder();
        reload();
      });
      function getLocations(list, num) {
        if (num >= list.length) {
          return Promise.resolve(list);
        }
        if (list[num].gcj02Lat && list[num].gcj02Lng) {
          return new Promise((resolve) => {
            getAddress([list[num].gcj02Lng, list[num].gcj02Lat]).then((res) => {
              list[num].gpsPosition = res;
              resolve(getLocations(list, num + 1));
            });
          });
        } else {
          return getLocations(list, num + 1);
        }
      }

      const [registerTable, { reload }] = useTable({
        title: '设备列表',
        api: (params) => {
          // eslint-disable-next-line no-async-promise-executor
          return new Promise(async (resolve, reject) => {
            try {
              let res = await getDeviceList(params);
              res.data = await getLocations(res.data, 0);
              resolve(res);
            } catch (error) {
              reject(error);
            }
          });
        },
        immediate: false,
        columns,
        formConfig: {
          labelAlign: 'left',
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
      });
      function lookInfo(record) {
        router.push({ path: '/dashboard', state: { code: record.id } });
      }

      return {
        registerTable,
        lookInfo,
      };
    },
  });
</script>
