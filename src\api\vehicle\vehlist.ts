import { defHttp as request } from '@/utils/http/axios';

/**
 * @description: 设备列表
 */
export function getDeviceList(data) {
  return request.post({
    url: `/device/query`,
    data,
  });
}

/**
 * @description: 新建设备
 */
export function addDevice(data) {
  return request.post({
    url: `/device`,
    data,
    // permission: 'vendor-add'
  });
}
/**
 * @description: 编辑设备
 */
export function editDevice(id, data) {
  return request.put({
    url: `/device/${id}`,
    data,
    // permission: 'vendor-edit'
  });
}
/**
 * @description: 删除设备
 */
export function removeDevice(id) {
  return request.delete({
    url: `/device/${id}`,
    // permission: 'vendor-remove'
  });
}
/**
 * @description: 销售设备
 */
export function saleDevice(data) {
  return request.put({
    url: `/device/sale`,
    data,
  });
}
/**
 * @description:按分组获取车辆树
 */
export function getDeviceGroupTree(data = {}) {
  return request.post({
    url: `/device/group`,
    data,
  });
}
//新建分组
export function createGroup(data = {}) {
  return request.post({
    url: `/deviceGroup`,
    data,
  });
}
//编辑分组
export function editGroup(id, data = {}) {
  return request.put({
    url: `/deviceGroup/${id}`,
    data,
  });
}
//删除分组
export function delGroup(id, data = {}) {
  return request.delete({
    url: `/deviceGroup/${id}`,
    data,
  });
}
//修改分组
export function updateGroup(data = {}) {
  return request.put({
    url: `/device/editGroup`,
    data,
  });
}
//修改设备名称分组
export function changeDeviceName(id: number | undefined, name: string) {
  return request.put({
    url: `/device/${id}/${name}`,
  });
}
/**
 * @description: 上传验证
 */
export function importValidate(data) {
  return request.post({
    url: `/device/import/validate`,
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
/**
 * @description: 获取设备报警列表
 */
export function getDeviceAlarmList(data = {}) {
  return request.post({
    url: `/deviceAlert/query`,
    data,
  });
}



/**
 * @description: 轨迹回放
 */
export function queryTrack(id, data) {
  return request.post({
    url: `/track/trace/${id}`,
    data,
  });
}
/**
 * @description: 获取设备详情
 */
export function getDeviceDetail(id) {
  return request.post({
    url: `/device/detail/${id}`,
  });
}
/**
 * @description: 获取车辆列表在线数据
 */
export function getVehData(data = {}) {
  return request.post({
    url: `/device/statusCount`,
    data,
  });
}
/**
 * @description: 指令下发
 */
export function sendCommand(code, simNo, data) {
  return request.put(
    {
      url: `/device/send/command/${code}/${simNo}`,
      data,
      // permission: 'vendor-add'
    },
    {
      successMessageMode: 'none',
      errorMessageMode: 'none',
    },
  );
}

/**
 * @description: 报警历史
 */
export function getAlarmHistory(data) {
  return request.post({
    url: `/deviceAlert/query`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 获取指令应答结果
 */
export function getlogresult(id) {
  return request.get(
    {
      url: `/terminal/command-log/${id}`,
    },
    {
      successMessageMode: 'none',
      errorMessageMode: 'none',
    },
  );
}

export function getTrip(data) {
  return request.post({
    url: `/device-trip/trip`,
    data,
    // permission: 'vendor-add'
  });
}

/**
 * @description: 行驶轨迹
 */
export function getTrack(data) {
  return request.post({
    url: `/device-trip/track`,
    data,
    // permission: 'vendor-add'
  });
}