<script lang="ts" setup name="role-list-form">
  import { ref } from 'vue';
  import { type TableActionType, BasicTable, useTable } from '@/components/Table';
  import { useModalInner, BasicModal } from '@/components/Modal';
  import { getFenceAlarmList } from '@/api/vehicle/fence';
  import { useGeoCoder } from '@/hooks/web/useMap';

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const emit = defineEmits(['success']);

  const { getAddress, manualInitializeGeocoder, isGeocoderReady } = useGeoCoder({});

  const fenceDetail: any = ref(null);
  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false, showCancelBtn: false, showOkBtn: false });
    fenceDetail.value = data;
    if (!isGeocoderReady()) {
      await manualInitializeGeocoder();
    }
    await reload();
  });

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '报警类型',
      dataIndex: 'alertEvent',
      customRender: ({ record }) => {
        return record.alertEvent === 'OUT' ? '驶出' : '驶入';
      },
    },
    {
      title: '报警围栏',
      dataIndex: 'fenceName',
    },
    // {
    //   title: '报警内容',
    //   dataIndex: 'alertMessage',
    //   width: 200,
    // },
    {
      title: '开始时间',
      dataIndex: 'alertStart',
    },
    {
      title: '结束时间',
      dataIndex: 'alertEnd',
    },
    {
      title: '持续时间(h)',
      dataIndex: 'continueTime',
    },
    {
      title: '位置',
      dataIndex: 'address',
      width: 200,
    },
  ];
  const [registerTable, { reload }] = useTable({
    api: getFenceAlarmList,
    immediate: false,
    columns,
    useSearchForm: false,
    showTableSetting: true,
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    maxHeight: 500,
    rowKey: 'id',
    beforeFetch: ({ criteria }) => {
      criteria.isActual = fenceDetail.value.isActual;
      criteria.fenceId = fenceDetail.value.fenceId;
      criteria.deviceId = fenceDetail.value.deviceId;
    },
    afterFetch: (data) => {
      return Promise.all(
        data.map(async (item) =>
          item.lng && item.lat
            ? getAddress([item.lng, item.lat]).then((res) => ({ ...item, address: res }))
            : item,
        ),
      );
    },
  });

  const tableRef = ref<Nullable<TableActionType>>(null);
</script>

<template>
  <BasicModal width="1200px" v-bind="$attrs" title="报警信息" @register="registerModal">
    <BasicTable @register="registerTable" ref="tableRef">
      <template #aboveOfTable>
        <!-- <div class="mapRef" ref="mapContainerRef"></div> -->
      </template>
    </BasicTable>
  </BasicModal>
</template>
