/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-10-06 08:53:36
 * @LastEditors  : chen
 * @LastEditTime : 2025-07-01 09:21:23
 * @FilePath     : \tzlink-gps-web\src\views\alarm\history\vehicle.data.tsx
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import { Tag } from 'ant-design-vue';
import { getOrgTreeOptions } from '@/api/passport/org';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: '设备名称',
    dataIndex: 'name',
    width: 280,
  },
  {
    title: '报警类型',
    dataIndex: 'typeName',
  },
  {
    title: '报警编码',
    dataIndex: 'typeCode',
  },
  {
    title: '报警位置',
    dataIndex: 'gpsPosition',
  },
  {
    title: '开始时间',
    dataIndex: 'alertStart',
  },
  {
    title: '结束时间',
    dataIndex: 'alertEnd',
  },
  {
    title: '时长',
    dataIndex: 'content',
  },
  {
    title: '报警摘要',
    dataIndex: 'summary',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'criteria.deviceId',
    label: '设备名称',
    component: 'Input',
    colProps: { span: 5 },
  },
  {
    field: 'criteria.orgUid',
    label: '所属机构',
    component: 'ApiTreeSelect',
    colProps: { span: 5 },
    componentProps: {
      api: getOrgTreeOptions,
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'criteria.type',
    label: '报警类型',
    component: 'Select',
    defaultValue: null,
    colProps: { span: 5 },
    componentProps: {
      options: [
        { label: '超速报警', value: 'SPEED' },
        { label: '疲劳驾驶', value: 'TIRED' },
        { label: '安全带报警', value: 'BELT' },
        { label: '其他报警', value: 'OTHER' },
      ],
    },
  },
  {
    field: 'criteria.range',
    label: '日期范围',
    component: 'RangePicker',
    colProps: { span: 5 },
    defaultValue: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    componentProps: {
      style: 'width: 100%;',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];
