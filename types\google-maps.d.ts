/**
 * Google Maps API 全局类型定义
 * 扩展 @types/google.maps 的类型定义，确保全局可用
 */

/// <reference types="@types/google.maps" />

declare global {
  interface Window {
    google: typeof google;
    __googleMapsCallback?: () => void;
  }

  namespace google {
    namespace maps {
      // 确保基础类型可用
      class Map {
        constructor(mapDiv: Element | null, opts?: google.maps.MapOptions);
        setCenter(latlng: google.maps.LatLng | google.maps.LatLngLiteral): void;
        getCenter(): google.maps.LatLng | undefined;
        setZoom(zoom: number): void;
        getZoom(): number | undefined;
        getBounds(): google.maps.LatLngBounds | undefined;
        fitBounds(bounds: google.maps.LatLngBounds | google.maps.LatLngBoundsLiteral): void;
        panTo(latLng: google.maps.LatLng | google.maps.LatLngLiteral): void;
        panBy(x: number, y: number): void;
        setMapTypeId(mapTypeId: google.maps.MapTypeId | string): void;
        setOptions(options: google.maps.MapOptions): void;
      }

      class LatLng {
        constructor(lat: number, lng: number, noWrap?: boolean);
        lat(): number;
        lng(): number;
        equals(other: google.maps.LatLng): boolean;
        toString(): string;
        toUrlValue(precision?: number): string;
        toJSON(): google.maps.LatLngLiteral;
      }

      class LatLngBounds {
        constructor(
          sw?: google.maps.LatLng | google.maps.LatLngLiteral,
          ne?: google.maps.LatLng | google.maps.LatLngLiteral,
        );
        contains(latLng: google.maps.LatLng | google.maps.LatLngLiteral): boolean;
        equals(other: google.maps.LatLngBounds): boolean;
        extend(point: google.maps.LatLng | google.maps.LatLngLiteral): google.maps.LatLngBounds;
        getCenter(): google.maps.LatLng;
        getNorthEast(): google.maps.LatLng;
        getSouthWest(): google.maps.LatLng;
        intersects(other: google.maps.LatLngBounds): boolean;
        isEmpty(): boolean;
        toJSON(): google.maps.LatLngBoundsLiteral;
        toString(): string;
        toUrlValue(precision?: number): string;
        union(other: google.maps.LatLngBounds): google.maps.LatLngBounds;
      }

      class Marker {
        constructor(opts?: google.maps.MarkerOptions);
        getPosition(): google.maps.LatLng | undefined;
        setPosition(latlng: google.maps.LatLng | google.maps.LatLngLiteral | null): void;
        getMap(): google.maps.Map | google.maps.StreetViewPanorama | null;
        setMap(map: google.maps.Map | google.maps.StreetViewPanorama | null): void;
        getTitle(): string | undefined;
        setTitle(title: string): void;
        getIcon(): string | google.maps.Icon | google.maps.Symbol | undefined;
        setIcon(icon: string | google.maps.Icon | google.maps.Symbol | null): void;
        getVisible(): boolean;
        setVisible(visible: boolean): void;
        setOptions(options: google.maps.MarkerOptions): void;
      }

      class Polyline {
        constructor(opts?: google.maps.PolylineOptions);
        getPath(): google.maps.MVCArray<google.maps.LatLng>;
        setPath(
          path:
            | google.maps.MVCArray<google.maps.LatLng>
            | google.maps.LatLng[]
            | google.maps.LatLngLiteral[],
        ): void;
        getMap(): google.maps.Map | null;
        setMap(map: google.maps.Map | null): void;
        getVisible(): boolean;
        setVisible(visible: boolean): void;
        setOptions(options: google.maps.PolylineOptions): void;
      }

      class InfoWindow {
        constructor(opts?: google.maps.InfoWindowOptions);
        close(): void;
        getContent(): string | Element | Text | null;
        setContent(content: string | Element | Text | null): void;
        getPosition(): google.maps.LatLng | undefined;
        setPosition(position: google.maps.LatLng | google.maps.LatLngLiteral | null): void;
        open(
          map?: google.maps.Map | google.maps.StreetViewPanorama,
          anchor?: google.maps.MVCObject,
        ): void;
        setOptions(options: google.maps.InfoWindowOptions): void;
      }

      class Geocoder {
        constructor();
        geocode(
          request: google.maps.GeocoderRequest,
          callback: (
            results: google.maps.GeocoderResult[] | null,
            status: google.maps.GeocoderStatus,
          ) => void,
        ): void;
      }

      // 枚举类型
      enum GeocoderStatus {
        ERROR = 'ERROR',
        INVALID_REQUEST = 'INVALID_REQUEST',
        OK = 'OK',
        OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
        REQUEST_DENIED = 'REQUEST_DENIED',
        UNKNOWN_ERROR = 'UNKNOWN_ERROR',
        ZERO_RESULTS = 'ZERO_RESULTS',
      }

      enum MapTypeId {
        HYBRID = 'hybrid',
        ROADMAP = 'roadmap',
        SATELLITE = 'satellite',
        TERRAIN = 'terrain',
      }

      // 接口类型
      interface MapOptions {
        backgroundColor?: string;
        center?: google.maps.LatLng | google.maps.LatLngLiteral;
        clickableIcons?: boolean;
        controlSize?: number;
        disableDefaultUI?: boolean;
        disableDoubleClickZoom?: boolean;
        draggable?: boolean;
        draggableCursor?: string;
        draggingCursor?: string;
        fullscreenControl?: boolean;
        fullscreenControlOptions?: google.maps.FullscreenControlOptions;
        gestureHandling?: string;
        heading?: number;
        keyboardShortcuts?: boolean;
        mapTypeControl?: boolean;
        mapTypeControlOptions?: google.maps.MapTypeControlOptions;
        mapTypeId?: google.maps.MapTypeId | string;
        maxZoom?: number;
        minZoom?: number;
        noClear?: boolean;
        panControl?: boolean;
        panControlOptions?: google.maps.PanControlOptions;
        restriction?: google.maps.MapRestriction;
        rotateControl?: boolean;
        rotateControlOptions?: google.maps.RotateControlOptions;
        scaleControl?: boolean;
        scaleControlOptions?: google.maps.ScaleControlOptions;
        scrollwheel?: boolean;
        streetView?: google.maps.StreetViewPanorama;
        streetViewControl?: boolean;
        streetViewControlOptions?: google.maps.StreetViewControlOptions;
        styles?: google.maps.MapTypeStyle[];
        tilt?: number;
        zoom?: number;
        zoomControl?: boolean;
        zoomControlOptions?: google.maps.ZoomControlOptions;
      }

      interface LatLngLiteral {
        lat: number;
        lng: number;
      }

      interface LatLngBoundsLiteral {
        east: number;
        north: number;
        south: number;
        west: number;
      }

      interface MarkerOptions {
        anchorPoint?: google.maps.Point;
        animation?: google.maps.Animation;
        clickable?: boolean;
        crossOnDrag?: boolean;
        cursor?: string;
        draggable?: boolean;
        icon?: string | google.maps.Icon | google.maps.Symbol;
        label?: string | google.maps.MarkerLabel;
        map?: google.maps.Map | google.maps.StreetViewPanorama;
        opacity?: number;
        optimized?: boolean;
        position?: google.maps.LatLng | google.maps.LatLngLiteral;
        shape?: google.maps.MarkerShape;
        title?: string;
        visible?: boolean;
        zIndex?: number;
      }

      interface PolylineOptions {
        clickable?: boolean;
        draggable?: boolean;
        editable?: boolean;
        geodesic?: boolean;
        icons?: google.maps.IconSequence[];
        map?: google.maps.Map;
        path?:
          | google.maps.MVCArray<google.maps.LatLng>
          | google.maps.LatLng[]
          | google.maps.LatLngLiteral[];
        strokeColor?: string;
        strokeOpacity?: number;
        strokeWeight?: number;
        visible?: boolean;
        zIndex?: number;
      }

      interface InfoWindowOptions {
        content?: string | Element | Text;
        disableAutoPan?: boolean;
        maxWidth?: number;
        pixelOffset?: google.maps.Size;
        position?: google.maps.LatLng | google.maps.LatLngLiteral;
        zIndex?: number;
      }

      interface GeocoderRequest {
        address?: string;
        bounds?: google.maps.LatLngBounds | google.maps.LatLngBoundsLiteral;
        componentRestrictions?: google.maps.GeocoderComponentRestrictions;
        location?: google.maps.LatLng | google.maps.LatLngLiteral;
        placeId?: string;
        region?: string;
      }

      interface GeocoderResult {
        address_components: google.maps.GeocoderAddressComponent[];
        formatted_address: string;
        geometry: google.maps.GeocoderGeometry;
        partial_match?: boolean;
        place_id: string;
        postcode_localities?: string[];
        types: string[];
      }

      interface GeocoderAddressComponent {
        long_name: string;
        short_name: string;
        types: string[];
      }

      interface GeocoderGeometry {
        bounds?: google.maps.LatLngBounds;
        location: google.maps.LatLng;
        location_type: google.maps.GeocoderLocationType;
        viewport: google.maps.LatLngBounds;
      }

      interface GeocoderComponentRestrictions {
        administrativeArea?: string;
        country?: string | string[];
        locality?: string;
        postalCode?: string;
        route?: string;
      }

      // 其他必要的类型
      class MVCArray<T> {
        constructor(array?: T[]);
        clear(): void;
        forEach(callback: (elem: T, i: number) => void): void;
        getArray(): T[];
        getAt(i: number): T;
        getLength(): number;
        insertAt(i: number, elem: T): void;
        pop(): T;
        push(elem: T): number;
        removeAt(i: number): T;
        setAt(i: number, elem: T): void;
      }

      class Point {
        constructor(x: number, y: number);
        x: number;
        y: number;
        equals(other: google.maps.Point): boolean;
        toString(): string;
      }

      class Size {
        constructor(width: number, height: number, widthUnit?: string, heightUnit?: string);
        width: number;
        height: number;
        equals(other: google.maps.Size): boolean;
        toString(): string;
      }
    }
  }
}

export {};
