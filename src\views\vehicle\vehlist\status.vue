<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-09-29 09:41:52
 * @LastEditors  : chen
 * @LastEditTime : 2024-09-29 16:25:41
 * @FilePath     : \special-front\src\views\vehicle\vehlist\status.vue
 * Copyright (C) 2024 chen. All rights reserved.
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #settingCount="{ record }">
        <Tag color="blue" class="cursor-pointer">{{ record.settingCount }}</Tag>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'vin'">
          <a @click="lookInfo(record)">
            <span> {{ record.vin }} </span>
          </a>
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { Tag } from 'ant-design-vue';

  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getvehicleList } from '@/api/vehicle/vehlist';

  import { statiusColumns as columns, searchFormSchema } from './vehicle.data';
  import { router } from '@/router';

  export default defineComponent({
    name: 'VehicleList',
    components: { BasicTable, TableAction, Tag },
    setup() {
      const geocoder = new AMap.Geocoder({
        city: '010', // 城市设为北京，默认：“全国”
        radius: 1000, // 范围，默认：500
      });
      function getLocations(list, num) {
        if (num >= list.length) {
          return Promise.resolve(list);
        }
        if (list[num].gcj02Lat && list[num].gcj02Lng) {
          return new Promise((resolve) => {
            geocoder.getAddress(
              new AMap.LngLat(list[num].gcj02Lng, list[num].gcj02Lat),
              (status, result) => {
                if (status === 'complete' && result.regeocode) {
                  list[num].gpsPosition = result.regeocode.formattedAddress;
                } else {
                  list[num].gpsPosition = '';
                }
                resolve(getLocations(list, num + 1));
              },
            );
          });
        } else {
          return getLocations(list, num + 1);
        }
      }

      const [registerTable, { reload }] = useTable({
        title: '设备列表',
        api: (params) => {
          // eslint-disable-next-line no-async-promise-executor
          return new Promise(async (resolve, reject) => {
            try {
              let res = await getvehicleList(params);
              res.data = await getLocations(res.data, 0);
              resolve(res);
            } catch (error) {
              reject(error);
            }
          });
        },
        columns,
        formConfig: {
          labelAlign: 'left',
          schemas: searchFormSchema,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
      });
      function lookInfo(record) {
        router.push({ path: '/dashboard', state: { code: record.id } });
      }

      return {
        registerTable,
        lookInfo,
      };
    },
  });
</script>
