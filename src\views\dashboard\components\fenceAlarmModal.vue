<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-30 16:51:28
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-30 17:26:04
 * @FilePath     : \tzlink-gps-web\src\views\dashboard\components\fenceAlarmModal.vue
 * Copyright (C) 2025 chen. All rights reserved.
-->
<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, h } from 'vue';
  import { BasicTable, useTable } from '@/components/Table';
  import { useModalInner, BasicModal } from '@/components/Modal';
  import { getFenceAlarmList } from '@/api/vehicle/fence';
  import { useAMap } from '@/hooks/web/useAMap';
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const emit = defineEmits(['success']);
  const { batchGetAddress } = useAMap({});

  const [registerModal] = useModalInner(async (data) => {
  });

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '报警类型',
      dataIndex: 'alertEvent',
      customRender: ({ record }) => {
        return record.alertEvent === 'OUT' ? '驶出' : '驶入';
      },
    },
    {
      title: '报警围栏',
      dataIndex: 'fenceName',
    },
    // {
    //   title: '报警内容',
    //   dataIndex: 'alertMessage',
    //   width: 200,
    // },
    {
      title: '开始时间',
      dataIndex: 'alertStart',
    },
    {
      title: '结束时间',
      dataIndex: 'alertEnd',
    },
    {
      title: '持续时间(h)',
      dataIndex: 'continueTime',
    },
    {
      title: '位置',
      dataIndex: 'address',
      width: 200,
    },
  ];
  const [registerTable] = useTable({
    api: getFenceAlarmList,
    immediate: true,
    columns,
    useSearchForm: false,
    showTableSetting: false,
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    maxHeight: 500,
    rowKey: 'id',
    afterFetch: (data) => {
      return Promise.all(
        data.map(async (item) =>
          item.wgs84Lng && item.wgs84Lat
            ? batchGetAddress(item, ['wgs84Lat', 'wgs84Lng']).then((res: any) => ({
                ...item,
                address: res.address,
              }))
            : item,
        ),
      );
    },
  });
</script>

<template>
  <BasicModal width="800px" v-bind="$attrs" title="围栏报警" @register="registerModal">
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>