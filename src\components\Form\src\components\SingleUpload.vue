<script lang="ts" setup>
  import { Upload, message } from 'ant-design-vue';
  import { onMounted, ref, watch } from 'vue';
  import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue';
  import type { UploadChangeParam, UploadProps } from 'ant-design-vue';

  const props = defineProps({
    value: {
      type: String,
      default: null,
    },
  });
  onMounted(() => {
    if (props.value) imageUrl.value = `/file/${props.value}`;
  });
  watch(
    () => props.value,
    () => {
      if (props.value) imageUrl.value = `/file/${props.value}`;
    },
  );

  const emits = defineEmits(['upload-success', 'update:value', 'change']);

  // function getBase64(img: Blob, callback: (base64Url: string) => void) {
  //   const reader = new FileReader();
  //   reader.addEventListener('load', () => callback(reader.result as string));
  //   reader.readAsDataURL(img);
  // }

  const fileList = ref([]);
  const loading = ref<boolean>(false);
  const imageUrl = ref<string>('');

  const handleChange = (info: UploadChangeParam) => {
    console.log('handle upload change info : ', info);
    if (info.file.status === 'uploading') {
      loading.value = true;
      return;
    }
    if (info.file.status === 'done') {
      // Get this url from response in real world.
      // getBase64(info.file.originFileObj as Blob, (base64Url: string) => {
      //   imageUrl.value = base64Url;
      //   loading.value = false;
      // });
      imageUrl.value = `/file/${info.file.response?.object}`;
      emits('upload-success', info.file.response?.object);
      emits('update:value', info.file.response?.object);
      emits('change', info.file.response?.object);
    }
    if (info.file.status === 'error') {
      loading.value = false;
      message.error('upload error');
    }
  };

  const beforeUpload = (file: UploadProps['fileList'][number]) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传jpg/png格式的图片!');
    }
    // const isLt2M = file.size / 1024 / 1024 < 2;
    // if (!isLt2M) {
    //   message.error('Image must smaller than 2MB!');
    // }
    return isJpgOrPng;
  };
</script>
<template>
  <Upload
    v-model:file-list="fileList"
    name="file"
    list-type="picture-card"
    :show-upload-list="false"
    action="/file/upload"
    :before-upload="beforeUpload"
    @change="handleChange"
  >
    <div v-if="imageUrl" class="w-30 h-30">
      <img :src="imageUrl" alt="avatar" class="w-full h-full" />
    </div>
    <div v-else class="w-30 h-30 flex justify-center items-center">
      <LoadingOutlined v-if="loading" />
      <PlusOutlined v-else />
    </div>
  </Upload>
</template>
<style scoped>
  :deep(.ant-upload.ant-upload-select) {
    width: 120px !important;
    height: 120px !important;
  }
</style>
