<script lang="ts" setup>
  import { ref, watch, onMounted, toRefs } from 'vue';
  import { RadioGroup, Radio } from 'ant-design-vue';
  import ECharts from '@/components/ECharts/src/ECharts.vue';
  import { getDeviceGrowthActive } from '@/api/statis/deviceovw';
  import dayjs from 'dayjs';

  const props = defineProps({
    criteria: Object,
  });

  const { criteria }: any = toRefs(props);
  const type = ref(0);
  const isMonth = ref(false);

  let options = ref({
    title: {
      text: '设备增长激活曲线图',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params) {
        let result = params[0].axisValue + '<br>';
        params.forEach((param) => {
          result += `${param.seriesName}: ${param.value}<br>`;
        });
        return result;
      },
    },
    legend: {
      data: ['新增设备', '活跃设备'],
      top: 30,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
      // axisLabel: {
      //   rotate: 45,
      // },
    },
    yAxis: {
      type: 'value',
      name: '设备数量',
    },
    series: [
      {
        name: '新增设备',
        type: 'line',
        data: [],
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#5470C6',
        },
        lineStyle: {
          width: 3,
        },
      },
      {
        name: '活跃设备',
        type: 'line',
        data: [],
        symbol: 'diamond',
        symbolSize: 8,
        itemStyle: {
          color: '#91CC75',
        },
        lineStyle: {
          width: 3,
        },
      },
    ],
  });

  watch(
    criteria,
    (newValue, oldValue) => {
      const params = {
        orgId: newValue.orgId,
      };
      handleSearch(params);
    },
    {
      deep: true,
    },
  );

  async function handleSearch(params) {
    const res = await getDeviceGrowthActive(params);
    const months = res.map((item) => item.month);
    const addCounts = res.map((item) => item.add);
    const activeCounts = res.map((item) => item.active);
    options.value.xAxis.data = months;
    options.value.series[0].data = addCounts;
    options.value.series[1].data = activeCounts;
  }

  const handleTypeChange = async (e) => {
    let v = e.target.value;
    if (v === 1) {
      isMonth.value = false;
      const params = {
        orgId: criteria.value.orgId,
        startTime: dayjs().subtract(1, 'year').startOf('year').format('YYYY-MM-DD'),
        endTime: dayjs().endOf('year').format('YYYY-MM-DD'),
      };
      await handleSearch(params);
    } else {
      isMonth.value = true;
      const params = {
        orgId: criteria.value.orgId,
        startTime: dayjs().subtract(1, 'year').startOf('month').format('YYYY-MM-DD'),
        endTime: dayjs().endOf('month').format('YYYY-MM-DD'),
      };
      await handleSearch(params);
    }
  };

  onMounted(() => {
    handleSearch({});
  });
</script>

<template>
  <div class="w-full h-80 flex flex-col">
    <div class="ml-auto mr-20">
      <RadioGroup v-model:value="type" @change="handleTypeChange">
        <Radio :value="1">年</Radio>
        <Radio :value="2">月</Radio>
      </RadioGroup>
    </div>
    <ECharts :options="options" />
  </div>
</template>

<style scoped></style>
