import { BasicColumn, FormSchema } from '@/components/Table';
import { getOrgTreeOptions } from '@/api/passport/org';
import { getModelTree } from '@/api/data/deviceModel';

export const columns: BasicColumn[] = [
  {
    title: '型号名称',
    dataIndex: 'name',
  },
  {
    title: '所属机构',
    dataIndex: 'orgName',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '型号名称',
    component: 'Input',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'parentId',
    label: '上级型号',
    component: 'ApiTreeSelect',
    defaultValue: null,
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请选择',
      api: getModelTree,
      labelField: 'name',
      valueField: 'id',
      getPopupContainer: () => document.body,
    },
    required: false,
  },
  {
    field: 'orgId',
    label: '所属机构',
    component: 'ApiTreeSelect',
    colProps: { span: 24 },
    ifShow: (record: any) => !record.values.parentId,
    componentProps: {
      placeholder: '请选择',
      api: getOrgTreeOptions,
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
    required: true,
  },
];
