<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-10 15:39:41
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-25 14:11:29
 * @FilePath     : \tzlink-gps-web\src\views\system\org\index.vue
 * Copyright (C) 2025 chen. All rights reserved.
-->
<template>
  <div class="p-4 h-full">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate"> 新增 </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: '是否确认删除',
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <OrgModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';

  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getOrgTreeOptions, removeOrg } from '@/api/passport/org';

  import { useModal } from '@/components/Modal';
  import OrgModal from './OrgModal.vue';
  import { useMessage } from '@/hooks/web/useMessage';

  import { columns, searchFormSchema } from './org.data';

  export default defineComponent({
    name: 'OrgManagement',
    components: { BasicTable, OrgModal, TableAction },
    setup() {
      const [registerModal, { openModal }] = useModal();
      const [registerTable, { reload }] = useTable({
        title: '机构列表',
        api: getOrgTreeOptions,
        columns,
        // formConfig: {
        //   labelAlign: 'left',
        //   schemas: searchFormSchema,
        // },
        defSort: {
          order: {
            property: 'uid',
            direction: 'ASC',
          },
        },
        rowKey: 'uid',
        // pagination: false,
        striped: false,
        useSearchForm: false,
        showTableSetting: true,
        bordered: true,
        showIndexColumn: false,
        canResize: false,
        actionColumn: {
          width: 80,
          title: '操作',
          dataIndex: 'action',
          slots: { customRender: 'action' },
          fixed: undefined,
        },
      });

      function handleCreate() {
        openModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        openModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleDelete(record: Recordable) {
        console.log(record);
        const { createMessage } = useMessage();
        removeOrg(record?.uid).then((res) => {
          createMessage.success('删除成功');
          reload();
        });
      }

      function handleSuccess() {
        reload();
      }

      return {
        registerTable,
        registerModal,
        handleCreate,
        handleEdit,
        handleDelete,
        handleSuccess,
      };
    },
  });
</script>
