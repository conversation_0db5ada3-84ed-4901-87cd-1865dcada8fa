<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-30 14:23:48
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-30 14:39:16
 * @FilePath     : \tzlink-gps-web\src\views\device\list\uploadErrorForm.vue
 * Copyright (C) 2025 chen. All rights reserved.
-->
<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, h } from 'vue';
  import { BasicTable, useTable } from '@/components/Table';
  import { useModalInner, BasicModal } from '@/components/Modal';

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const emit = defineEmits(['success']);

  const dataSource = ref([]);
  const [registerModal] = useModalInner(async (data) => {
    dataSource.value = data.record.map((v) => {
      let msg = Object.values(v.errorMessages).join(',');
      return { ...v.target, errorData: msg };
    });
  });

  const columns = [
    {
      title: '设备名称',
      dataIndex: 'name',
    },
    {
      title: 'IMEI',
      dataIndex: 'deviceSn',
    },
    {
      title: '错误信息',
      dataIndex: 'errorData',
    },
  ];
  const [registerTable] = useTable({
    dataSource,
    immediate: false,
    columns,
    useSearchForm: false,
    showTableSetting: false,
    bordered: true,
    canResize: false,
    showIndexColumn: true,
    maxHeight: 500,
    rowKey: 'id',
  });

</script>

<template>
  <BasicModal width="800px" v-bind="$attrs" title="错误信息" @register="registerModal">
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>