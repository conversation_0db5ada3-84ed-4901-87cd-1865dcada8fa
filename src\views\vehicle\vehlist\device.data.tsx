/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-20 17:30:22
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-25 17:26:32
 * @FilePath     : \tzlink-gps-web\src\views\vehicle\vehlist\device.data.tsx
 * Copyright (C) 2025 chen. All rights reserved.
 */
import { ref } from 'vue';
import { FormSchema } from '@/components/Table';
import { getOrgTreeOptions } from '@/api/passport/org';
import { getsimList } from '@/api/data/simCard';
import { getModelTree } from '@/api/data/deviceModel';

const simCardList = ref([]);
const getCardList = async (simNo: any = '') => {
  const params = {
    pageIndex: 1,
    pageSize: 10,
    order: { property: 'id', direction: 'ASC' },
    criteria: {
      simNo,
    },
  };
  const res = await getsimList(params);
  console.log(res);
  simCardList.value = (res.data || []).map((v) => ({ label: v.simNo, value: v.id }));
};
getCardList();
export const basicFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '设备名称',
    colProps: { span: 12 },
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'deviceModelId',
    label: '设备类型',
    colProps: { span: 12 },
    component: 'ApiTreeSelect',
    required: true,
    componentProps: {
      api: getModelTree,
      disabled: true,
      labelField: 'name',
      valueField: 'id',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'deviceSn',
    label: 'IMEI',
    required: true,
    colProps: { span: 12 },
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'orgId',
    label: '所属机构',
    component: 'ApiTreeSelect',
    colProps: { span: 12 },
    required: true,
    componentProps: {
      api: getOrgTreeOptions,
      labelField: 'name',
      disabled: true,
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'simId',
    label: 'sim卡',
    component: 'Select',
    colProps: { span: 12 },
    required: true,
    componentProps: {
      // api: getsimList,
      options: simCardList,
      disabled: true,
      filterOption: false,
      showSearch: true,
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'protocol',
    label: '协议类型',
    component: 'Select',
    required: true,
    defaultValue: null,
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
      options: [
        { label: 'JT808', value: 'JT808' },
        { label: 'TIZA-V3', value: 'TIZAV3' },
      ],
    },
  },
  {
    field: 'activateTime',
    label: '激活时间',
    colProps: { span: 12 },
    component: 'DatePicker',
    componentProps: {
      style: 'width: 100%',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      disabled: true,
      showTime: true,
    },
  },
  {
    field: 'saleTime',
    label: '销售时间',
    colProps: { span: 12 },
    component: 'DatePicker',
    componentProps: {
      disabled: true,
      style: 'width: 100%',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
  },
  {
    field: 'expireTime',
    label: '平台到期时间',
    colProps: { span: 12 },
    component: 'DatePicker',
    componentProps: {
      disabled: true,
      style: 'width: 100%',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
  },
  {
    field: 'useWay',
    label: '使用范围',
    colProps: { span: 12 },
    component: 'DatePicker',
    slot: 'useWay',
  },
  {
    field: 'comment',
    label: '备注',
    colProps: { span: 24 },
    component: 'InputTextArea',
    componentProps: {
      disabled: true,
    },
  },
];
export const simFormSchema: FormSchema[] = [
  {
    field: 'simNo',
    label: 'sim卡号',
    required: true,
    colProps: { span: 12 },
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'iccid',
    label: 'ICCID号',
    required: true,
    colProps: { span: 12 },
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'imsi',
    label: 'IMSI号',
    required: true,
    colProps: { span: 12 },
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'network',
    label: '网络类型',
    component: 'Select',
    defaultValue: null,
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
      options: [
        { label: '2g', value: 0 },
        { label: '4g', value: 1 },
        { label: '5g', value: 2 },
      ],
    },
  },
  {
    field: 'status',
    label: '卡状态',
    component: 'Select',
    defaultValue: null,
    colProps: { span: 12 },
    componentProps: {
      disabled: true,
      options: [
        { label: '正常', value: 'NORMAL' },
        { label: '停机', value: 'SHUTDOWN' },
        { label: '注销', value: 'LOGOFF' },
      ],
    },
  },
  {
    field: 'activateTime',
    label: '卡激活时间',
    colProps: { span: 12 },
    component: 'DatePicker',
    componentProps: {
      style: 'width: 100%',
      disabled: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
  },
  {
    field: 'expireTime',
    label: '卡到期时间',
    colProps: { span: 12 },
    component: 'DatePicker',
    componentProps: {
      disabled: true,
      style: 'width: 100%',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
  },
];

export const customerFormSchema: FormSchema[] = [
  {
    field: 'driverName',
    label: '司机名称',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'mobile',
    label: '联系方式',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'license',
    label: '车牌号',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'cardNo',
    label: '身份证号',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'sn',
    label: '唯一识别码',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'vin',
    label: '车架号/vin码',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'engineNo',
    label: '发动机号',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'businessName',
    label: '业务员',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'equipColor',
    label: '车辆颜色',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
];
export const installFormSchema: FormSchema[] = [
  {
    field: 'createTime',
    label: '安装时间',
    colProps: { span: 12 },
    component: 'DatePicker',
    componentProps: {
      style: 'width: 100%',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
  },
  {
    field: 'address',
    label: '安装地址',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'company',
    label: '安装公司',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'location',
    label: '安装位置',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'createMember',
    label: '安装人',
    required: false,
    colProps: { span: 12 },
    component: 'Input',
  },
  {
    field: 'jpg',
    label: '安装图片',
    required: false,
    colProps: { span: 12 },
    component: 'SingleUpload',
  },
];
