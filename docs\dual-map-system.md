# 双地图系统使用指南

本项目实现了一个智能的双地图系统，能够根据用户的网络环境自动选择使用高德地图（中国大陆）或Google Maps（国际版本）。

## 功能特性

- 🌍 **智能检测**: 自动检测用户网络环境，判断是否在中国大陆
- 🔄 **自动切换**: 根据检测结果自动选择合适的地图提供商
- 🛡️ **容错机制**: 当一个地图API加载失败时，自动回退到另一个
- 🎯 **统一接口**: 提供统一的API接口，无需关心底层使用的是哪个地图
- ⚡ **动态加载**: 按需加载地图API，减少初始加载时间
- 🔧 **手动切换**: 支持手动切换地图提供商

## 快速开始

### 1. 配置API密钥

在 `.env` 文件中配置Google Maps API密钥：

```env
# Google Maps API Key
VITE_GOOGLE_MAPS_API_KEY = your_google_maps_api_key_here

# 高德地图配置 (已预配置)
VITE_AMAP_API_KEY = 8daa33fd0168182c44c29076ce995985
VITE_AMAP_SECURITY_JS_CODE = 0a93e982b376824d33c84f11e8751db6
```

### 2. 基本使用

```vue
<template>
  <div>
    <!-- 地图容器 -->
    <div ref="mapContainer" style="width: 100%; height: 400px;"></div>
    
    <!-- 状态显示 -->
    <div v-if="isLoading">正在加载地图...</div>
    <div v-if="error">{{ error }}</div>
    <div v-if="provider">当前使用: {{ provider === 'amap' ? '高德地图' : 'Google Maps' }}</div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useMap, MapProvider } from '@/hooks/web/useMap';

// 地图配置
const mapOptions = {
  center: [116.397428, 39.90923], // 北京天安门
  zoom: 13,
};

// 初始化地图
const {
  provider,
  isLoading,
  error,
  mapRef,
  getMapInstance,
  setMapCenter,
  addMarker,
  addPolyline,
  clearMap,
  getMapBoundary,
  getAddress,
  createInfoWindow,
  switchProvider,
} = useMap(mapOptions);

onMounted(() => {
  // 将mapRef绑定到DOM元素
  mapRef.value = mapContainer.value;
});

// 添加标记点
function addSampleMarker() {
  addMarker([116.397428, 39.90923], {
    title: '天安门',
    icon: 'https://example.com/marker-icon.png',
  });
}

// 获取地址信息
async function getSampleAddress() {
  try {
    const address = await getAddress([116.397428, 39.90923]);
    console.log('地址:', address);
  } catch (err) {
    console.error('获取地址失败:', err);
  }
}

// 手动切换地图提供商
async function switchToGoogle() {
  try {
    await switchProvider(MapProvider.GOOGLE);
  } catch (err) {
    console.error('切换到Google Maps失败:', err);
  }
}

async function switchToAMap() {
  try {
    await switchProvider(MapProvider.AMAP);
  } catch (err) {
    console.error('切换到高德地图失败:', err);
  }
}
</script>
```

### 3. 地理编码使用

```vue
<script setup lang="ts">
import { useGeoCoder } from '@/hooks/web/useMap';

const {
  provider,
  isLoading,
  error,
  getAddress,
} = useGeoCoder();

// 根据坐标获取地址
async function reverseGeocode() {
  try {
    const address = await getAddress([116.397428, 39.90923]);
    console.log('地址:', address);
  } catch (err) {
    console.error('地理编码失败:', err);
  }
}
</script>
```

## API 参考

### useMap(options, callback?, detectionOptions?)

主要的地图Hook，提供完整的地图功能。

#### 参数

- `options`: 统一的地图选项
  - `center`: 地图中心点 `[lng, lat]`
  - `zoom`: 缩放级别
  - `mapStyle`: 地图样式
  - `amapOptions`: 高德地图特有选项
  - `gmapOptions`: Google Maps特有选项

- `callback`: 地图初始化完成后的回调函数
- `detectionOptions`: 网络环境检测选项

#### 返回值

- `provider`: 当前使用的地图提供商
- `isLoading`: 是否正在加载
- `error`: 错误信息
- `mapRef`: 地图容器引用
- `getMapInstance()`: 获取地图实例
- `setMapCenter(lngLat)`: 设置地图中心点
- `addMarker(position, options)`: 添加标记点
- `addPolyline(points, options)`: 添加折线
- `clearMap()`: 清空地图
- `getMapBoundary()`: 获取地图边界
- `getAddress(lngLat)`: 地理编码
- `createInfoWindow(options)`: 创建信息窗口
- `switchProvider(provider)`: 切换地图提供商

### useGeoCoder(detectionOptions?)

地理编码Hook，用于地址解析。

#### 返回值

- `provider`: 当前使用的地图提供商
- `isLoading`: 是否正在加载
- `error`: 错误信息
- `getAddress(lngLat)`: 根据坐标获取地址

## 网络环境检测

系统使用多种方法检测用户网络环境：

1. **IP地理位置检测** (最准确)
   - 使用多个IP地理位置服务
   - 置信度: 0.9

2. **时区检测** (中等准确度)
   - 检查系统时区设置
   - 置信度: 0.7

3. **语言检测** (较低准确度)
   - 检查浏览器语言设置
   - 置信度: 0.5

系统会综合多种检测结果，计算加权平均值来做出最终决策。

## 配置选项

### 网络检测配置

```typescript
const detectionOptions = {
  timeout: 5000, // 检测超时时间
  fallbackToTimezone: true, // 是否回退到时区检测
  fallbackToLanguage: true, // 是否回退到语言检测
  useMultipleMethods: true, // 是否使用多种方法综合判断
};
```

### 地图选项配置

```typescript
const mapOptions = {
  center: [116.397428, 39.90923],
  zoom: 13,
  // 高德地图特有选项
  amapOptions: {
    mapStyle: 'amap://styles/grey',
    viewMode: '3D',
  },
  // Google Maps特有选项
  gmapOptions: {
    mapTypeId: 'roadmap',
    disableDefaultUI: true,
  },
};
```

## 故障排除

### 1. Google Maps API密钥未配置

**错误**: `Google Maps API key is required`

**解决**: 在 `.env` 文件中配置 `VITE_GOOGLE_MAPS_API_KEY`

### 2. 地图加载失败

**错误**: `Failed to load any map provider`

**解决**: 
- 检查网络连接
- 确认API密钥有效
- 检查控制台错误信息

### 3. 地理编码失败

**错误**: `Geocoder instance not available`

**解决**: 
- 确保地图已完全加载
- 检查API配额限制

## 最佳实践

1. **错误处理**: 始终包装地图操作在try-catch块中
2. **加载状态**: 显示加载状态给用户
3. **API配额**: 监控API使用量，避免超出配额
4. **缓存**: 利用地理编码缓存减少API调用
5. **性能**: 使用防抖处理频繁的地图操作

## 更新日志

- v1.0.0: 初始版本，支持高德地图和Google Maps自动切换
- 支持网络环境智能检测
- 提供统一的API接口
- 实现动态API加载
