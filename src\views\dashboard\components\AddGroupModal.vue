<!--
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2025-06-25 11:11:39
 * @LastEditors  : chen
 * @LastEditTime : 2025-06-25 15:18:34
 * @FilePath     : \tzlink-gps-web\src\views\dashboard\components\AddGroupModal.vue
 * Copyright (C) 2025 chen. All rights reserved.
-->
<script lang="ts" setup name="role-list-form">
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { FormSchema } from '@/components/Table';
  import { editGroup, createGroup } from '@/api/vehicle/vehlist';

  const formSchema: FormSchema[] = [
    {
      field: 'name',
      label: '分组名称',
      required: true,
      colProps: { span: 24 },
      component: 'Input',
    },
  ];

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const rowId = ref<number | undefined>(undefined);

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.record.groupId;
      setFieldsValue({
        name: data.record.groupName,
      });
    }
  });
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      if (unref(isUpdate)) {
        await editGroup(rowId.value, values);
      } else {
        await createGroup(values);
      }
      closeModal();
      emit('success', {
        isUpdate: unref(isUpdate),
        values: { ...values, id: rowId.value },
      });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  };
</script>

<template>
  <BasicModal v-bind="$attrs" title="新建分组" @register="registerModal" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
