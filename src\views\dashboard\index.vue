<template>
  <div class="home_div">
    <div class="left">
      <div class="top">
        <div class="org">
          <a-tree-select
            placeholder="请选择机构"
            allowClear
            treeDefaultExpandAll
            style="width: 100%"
            :treeData="orgOptions"
            @change="selectorg"
            :field-names="orgfieldNames"
          />
          <PlusCircleOutlined title="新建分组" @click="addGroup" />
        </div>
        <!-- <div class="search">
          <a-input-search
            v-model:value="vin"
            placeholder="设备名称"
            enter-button
            @search="onSearch"
          />
        </div> -->
        <Tabs v-model:activeKey="activeKey" @change="stateChange">
          <TabPane key="1" :tab="'全部(' + (VehData.all || 0) + ')'">
            <div class="scrollbox">
              <a-tree
                v-if="List.length > 0"
                :defaultExpandAll="true"
                :fieldNames="fieldNames"
                :treeData="List"
                @select="select"
                @check="select"
                v-model:checkedKeys="carList"
                checkable
              >
                <template #title="{ dataRef }">
                  <div class="tree_box" v-if="dataRef.isGroup">
                    <div class="tree_box_name">{{ dataRef.name }}</div>
                    <div class="actions" v-if="dataRef.name !== '默认分组'">
                      <Icon icon="clarity:note-edit-line" @click="editGroup(dataRef)" />

                      <Popconfirm
                        title="确认删除当前分组"
                        ok-text="确认"
                        cancel-text="取消"
                        @confirm="delGroupData(dataRef)"
                      >
                        <Icon icon="ant-design:delete-outlined" />
                      </Popconfirm>
                    </div>
                  </div>
                  <div class="tree_box" v-else>
                    <Icon
                      :icon="iconMap[dataRef.useWay]"
                      v-if="dataRef.useWay"
                      :title="dataRef.useWay"
                      style="margin-left: 5px; color: #0421bc"
                    />
                    <div class="tree_box_name">{{ dataRef.name }}</div>

                    <div class="speed" :class="{ online: dataRef.online == '在线' }">
                      {{ dataRef.gpsSpeed || '0.00' }} km/h
                      <EnvironmentOutlined />
                      <Tooltip placement="right" color="#FFF">
                        <template #title>
                          <div class="menu_list">
                            <div class="item">轨迹回放</div>
                            <div class="item">下发指令</div>
                            <div class="item">围栏设置</div>
                            <div class="item" @click="lookDetail(dataRef)">查看详情</div>
                            <div class="item" @click="moveGroup(dataRef)">移动到</div>
                          </div>
                        </template>
                        <MoreOutlined style="color: #000" />
                      </Tooltip>
                    </div>
                  </div>
                </template>
              </a-tree>
            </div>
          </TabPane>
          <TabPane key="2" :tab="'在线(' + (VehData.online || 0) + ')'">
            <div class="scrollbox">
              <a-tree
                style="width: 250px"
                v-if="List.length > 0"
                :defaultExpandAll="true"
                :fieldNames="fieldNames"
                :treeData="List"
                v-model:checkedKeys="carList"
                @select="select"
                checkable
              >
                <template #title="{ dataRef }">
                  <div class="tree_box" v-if="dataRef.isGroup">
                    <div class="tree_box_name">{{ dataRef.name }}</div>
                    <div class="actions" v-if="dataRef.name !== '默认分组'">
                      <Icon icon="clarity:note-edit-line" @click="editGroup(dataRef)" />

                      <Popconfirm
                        title="确认删除当前分组"
                        ok-text="确认"
                        cancel-text="取消"
                        @confirm="delGroupData(dataRef)"
                      >
                        <Icon icon="ant-design:delete-outlined" />
                      </Popconfirm>
                    </div>
                  </div>
                  <div class="tree_box" v-else>
                    <Icon
                      :icon="iconMap[dataRef.useWay]"
                      v-if="dataRef.useWay"
                      :title="dataRef.useWay"
                      style="margin-left: 5px; color: #0421bc"
                    />
                    <div class="tree_box_name">{{ dataRef.name }}</div>

                    <div class="speed" :class="{ online: dataRef.online == '在线' }">
                      {{ dataRef.gpsSpeed || '0.00' }} km/h
                      <EnvironmentOutlined />
                    </div>
                  </div>
                </template>
              </a-tree>
            </div>
          </TabPane>
          <TabPane key="3" :tab="'离线(' + (VehData.offline || 0) + ')'">
            <div class="scrollbox">
              <a-tree
                style="width: 250px"
                v-if="List.length > 0"
                :defaultExpandAll="true"
                :fieldNames="fieldNames"
                :treeData="List"
                v-model:checkedKeys="carList"
                @select="select"
                checkable
              >
                <template #title="{ dataRef }">
                  <div class="tree_box" v-if="dataRef.isGroup">
                    <div class="tree_box_name">{{ dataRef.name }}</div>
                    <div class="actions" v-if="dataRef.name !== '默认分组'">
                      <Icon icon="clarity:note-edit-line" @click="editGroup(dataRef)" />

                      <Popconfirm
                        title="确认删除当前分组"
                        ok-text="确认"
                        cancel-text="取消"
                        @confirm="delGroupData(dataRef)"
                      >
                        <Icon icon="ant-design:delete-outlined" />
                      </Popconfirm>
                    </div>
                  </div>
                  <div class="tree_box" v-else>
                    <Icon
                      :icon="iconMap[dataRef.useWay]"
                      v-if="dataRef.useWay"
                      :title="dataRef.useWay"
                      style="margin-left: 5px; color: #0421bc"
                    />
                    <div class="tree_box_name">{{ dataRef.name }}</div>
                    <div class="speed" :class="{ online: dataRef.online == '在线' }">
                      {{ dataRef.gpsSpeed || '0.00' }} km/h
                      <EnvironmentOutlined />
                    </div>
                  </div>
                </template>
              </a-tree>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
    <div class="right">
      <div class="topbox">
        <div class="mapcard">
          <div ref="containerRef" id="container" class="width_new">
            <div class="legend">
              <div class="col">
                <img class="icon" src="../../assets/images/online.png" />
                在线
              </div>
              <div class="col">
                <img class="icon" src="../../assets/images/offline.png" />
                离线
              </div>
              <div class="col">
                <img class="icon" src="../../assets/images/alarm.png" />
                报警
              </div>
              <div class="col" style="margin-right: 0">
                <img class="icon" src="../../assets/images/nosig.png" />
                无效定位
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <DicFormModal @register="registerDicFormModal" @success="handleCarSuccess" />
    <DeviceDetail @register="registerDetailModal" @success="handleCarSuccess" />
    <AddGroup @register="registerGroupModal" @success="handleCarSuccess" />
    <MoveModal @register="registerMoveModal" @success="handleCarSuccess" />
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, watch } from 'vue';
  import DicFormModal from './dicFormModal.vue';
  import AddGroup from './components/AddGroupModal.vue';
  import { Tree, TreeSelect, Popconfirm, Tabs, Tooltip } from 'ant-design-vue';
  import MoveModal from './components/moveModal.vue';
  import DeviceDetail from './components/deviceDetail.vue';
  import gisOnlinePng from '@/assets/images/online.png';
  import gisOffPng from '@/assets/images/offline.png';
  import nosigPng from '@/assets/images/nosig.png';
  import alarmPng from '@/assets/images/alarm.png';
  import {
    getDeviceGroupTree,
    delGroup,
    getlogresult,
    sendCommand,
    getVehData,
  } from '@/api/vehicle/vehlist';
  import { getOrgTreeOptions } from '@/api/passport/org';
  import { EnvironmentOutlined, PlusCircleOutlined, MoreOutlined } from '@ant-design/icons-vue';
  import { useMap } from '@/hooks/web/useMap';
  import Icon from '@/components/Icon/Icon.vue';
  import { useModal } from '@/components/Modal';
  import { useRoute, useRouter } from 'vue-router';

  let distributionMap;

  export default defineComponent({
    name: 'Dashboard',
    components: {
      Popconfirm,
      DicFormModal,
      DeviceDetail,
      PlusCircleOutlined,
      EnvironmentOutlined,
      Icon,
      ATree: Tree,
      ATreeSelect: TreeSelect,
      Tabs,
      MoveModal,
      TabPane: Tabs.TabPane,
      MoreOutlined,
      Tooltip,
      AddGroup,
    },

    setup() {
      const route = useRoute();
      const router = useRouter();
      const iconMap = ref({
        轿车: 'emojione-monotone:oncoming-automobile',
        货车: 'fa:truck',
        客车: 'fa:bus',
        出租车: 'fa:taxi',
        摩托车: 'fa:motorcycle',
        人: 'fa:user',
        挖掘机: 'mdi:excavator',
        其他: 'material-symbols-light:devices-other-outline',
      });
      watch(
        () => route,
        () => {
          if (history.state?.code) {
            try {
              carList.value = [Number(history.state?.code)];
              setTimeout(() => {
                // 获取所有的图层
                var allOverlays = distributionMap.getAllOverlays();
                // 遍历所有图层，查找Marker并触发点击事件
                allOverlays.forEach((overlay) => {
                  if (overlay instanceof AMap.Marker) {
                    // 触发点击事件
                    overlay.emit('click', { target: overlay });
                  }
                });
              }, 3000);
            } catch (error) {
              console.log(error);
            }
          }
        },
        { deep: true },
      );

      const {
        containerRef,
        getMapInstance,
        addMarker,
        getAddress,
        createInfoWindow,
        setFitView,
        clearMap,
        addToolBar,
        addControlBar,
        addHawkEye,
        addMapType,
        setMapCenter,
        setZoom,
        triggerEvent,
        addEventListener,
      } = useMap(
        {
          center: [116.33, 39.9],
          zoom: 5,
        },
        initMap,
      );

      let playObj = ref({} as any);

      let timer: any;
      let chartOptions = ref({} as any);

      let carList = ref([] as any);

      let allDevice = ref([]);

      const staticMap = ref({} as any);

      chartOptions.value = {
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            smooth: true,
            areaStyle: {},
          },
        ],
      };

      let total = ref('15');
      let online = ref('2');
      let onlinestate = ref(null as any);

      let activeKey = ref('1');

      let VehData = ref({
        all: 0,
        total: 0,
        online: 0,
        offline: 0,
      });

      let isMap = ref(true);

      const fieldNames = {
        key: 'id',
        title: 'name',
        children: 'devices',
        icon: renderIcon(),
      };
      const orgfieldNames = {
        label: 'name',
        value: 'uid',
      };

      let orgUid = ref('');
      let vin = ref('');
      let modelKey = ref('map');

      let List = ref([] as any);

      let orgOptions = ref([] as any);

      let selectedVeh = ref(null as any);

      function renderIcon() {
        return '哈哈';
      }

      function handleClick(e) {
        console.log(e.target.value);
        if (e.target.value == 'map') {
          isMap.value = true;
        } else {
          isMap.value = false;
        }
      }

      function getorg() {
        getOrgTreeOptions().then((res) => {
          orgOptions.value = res;
        });
      }

      function refresh() {
        getTree();
        getvehdata();
      }

      function getvehdata() {
        getVehData().then((res) => {
          VehData.value = res;
        });
      }

      function getTree() {
        getDeviceGroupTree({
          online: onlinestate.value,
          orgUid: orgUid.value,
        }).then((res) => {
          console.log('TreeData:', res);
          // if (!res.length) return;
          // let defaultMap = res[res.length - 1];
          // let list = res.slice(0, -1);
          // list.unshift(defaultMap);
          List.value = (res || []).map((v) => ({
            ...v,
            name: v.groupName,
            isGroup: true,
          }));
          allDevice.value = List.value.reduce((arr, item) => {
            return arr.concat(item.devices);
          }, []);
          getDistribution();
        });
      }
      function select(veh, e) {
        if (e.checked === false) return;
        console.log('选中', e.node);
        const data = e.node;
        selectedVeh.value = data.id;
        if (modelKey.value == 'map') {
          //如果是地图模式
          if (data.lng && data.lat) {
            setMapCenter([data.lng, data.lat]);
            setZoom(14);
            triggerEvent(markMap[data.id], 'click', data);
          }
        }
      }

      function stopChannel(channel) {
        sendCommand('realtime-video-upload-control', playObj.value.deviceSn, {
          channelNo: channel,
          commandType: 0,
        })
          .then((res) => {
            console.log('查询下发结果');
            if (res.success == true) {
              console.log('查询下发结果');
              getResult(res.object);
            }
          })
          .catch(() => {
            stopChannel(channel);
          });
      }
      //查询指令结果
      function getResult(id) {
        getlogresult(id).then((result) => {
          if (result.replyStatus == '等待应答') {
            timer = setTimeout(() => {
              getResult(id);
            }, 2000);
          } else if (result.replyStatus == '应答结束' && result.resultStatus == '成功') {
            timer = null;
            clearTimeout(timer);
          }
        });
      }

      function handleChange(e) {
        console.log('e~~~~~~~~~~~~~~~', e);
      }

      function initTrackMap() {
        const map = getMapInstance();
        console.log('map~~~~~~~', map);
      }

      function initMap() {
        addToolBar();
        addControlBar();
        addHawkEye();
        addMapType();

        getTree();
      }
      const selectElem = ref();
      const markMap = {};
      const [registerDetailModal, { openModal: openDetailModal }] = useModal();
      function getDistribution() {
        // 清除地图上所有添加的覆盖物
        clearMap();
        allDevice.value.forEach(async (elem: any) => {
          console.log('all device : ', elem);
          if (elem.lng != null && elem.lat != null) {
            elem.gpsPosition = await getAddress([elem.lng, elem.lat]);
            let icon;
            if (elem.online == '在线') {
              icon = gisOnlinePng;
            } else {
              if (elem.Effective !== 'Effective') {
                icon = nosigPng;
              } else {
                icon = gisOffPng;
              }
            }
            if (elem.alarmCount > 0) {
              icon = alarmPng;
            }
            const marker = addMarker([elem.lng, elem.lat], {
              icon: {
                imageSize: [50, 50],
                url: icon,
                size: [50, 50],
              },
              offset: [0, -5],
              extData: elem,
            });
            markMap[elem.id] = marker;
            addEventListener(marker, 'click', function (e) {
              console.log('点击maker~~~', e);
              const extData = e.target ? e.target.getExtData() : e;
              selectElem.value = extData;
              // 实例化信息窗体
              var content = `<div style="padding:6px;padding-left:10px;line-height:1.5;width:400px;font-size:12px">
              <div style="font-weight:bold;font-size:14px">${extData.deviceCustomer.license || extData.name || ''}</div>
              <div style="font-weight:bold;font-size:12px;display:flex;align-items:center;justify-content: space-between;">
                <span>IMEI：${extData.deviceSn}</span>
              </div>
              <ul style="display:flex;flex-wrap:wrap;">
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/user.png" width="17" title="车主">
                  <span style="margin-left:5px;">${extData.deviceCustomer.driverName}</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/wifi.png" width="17" title="GPS时间">
                  <span style="margin-left:5px;">${extData.gpsTime}</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/location.png" width="17" title="定位">
                  <span style="margin-left:5px;">${extData.location}</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/modal.png" width="17" title="类型">
                  <span style="margin-left:5px;">${extData.deviceModelName}</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/speed.png" width="17" title="速度">
                  <span style="margin-left:5px;">${extData.gpsSpeed || 0}km/h</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/online.png" width="17" title="在线状态">
                  <span style="margin-left:5px;">${extData.online}</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/lnglat.png" width="17" title="经纬度">
                  <span style="margin-left:5px;">${extData.lat + ',' + extData.lng}</span>
                </li>
                <li style="width:100%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/location_gps.png" width="17" title="当前位置">
                  <span style="margin-left:5px;">${extData.gpsPosition}</span>
                </li>
              </ul>
              </div>
              <div style='width: 100%;height:40px;display:flex;align-items:center;gap:30px;padding:6px;cursor:pointer;background:#F6F7F9'>
                <img class="goLocation" src="/public/images/icon/hf.png" width="20" title="轨迹">
                <img class="openDic" src="/public/images/icon/zl.png" width="20" title="指令">
                <img class="openDic" src="/public/images/icon/wl.png" width="20" title="围栏">
                <img class="openDetail" src="/public/images/icon/xq.png" width="20" title="详情">
                <img class="map_env" src="/public/images/icon/map.png" width="20" title="位置">
                <img class="alarmBtn" src="/public/images/icon/alarm.png" width="20" title="报警">
              </div>
              `;

              const infoWindow = createInfoWindow({
                content,
                offset: [25, -5],
              });

              infoWindow?.open(marker?.getPosition());
              addEventListener(infoWindow?.getRawInstance(), 'click', function () {
                const list = document.querySelectorAll('.openDic');
                list.forEach((el: any) => {
                  el.onclick = () => {
                    openDic();
                  };
                });
                //轨迹回放
                const locationArr = document.querySelectorAll('.goLocation');
                locationArr.forEach((el: any) => {
                  el.onclick = () => {
                    router.push({
                      path: '/location/track',
                      query: { code: selectElem.value.id },
                    });
                  };
                });
                //报警历史
                const alarmBtnArr = document.querySelectorAll('.alarmBtn');
                alarmBtnArr.forEach((el: any) => {
                  el.onclick = () => {};
                });
                //街景地图
                const mapEnvArr = document.querySelectorAll('.map_env');
                mapEnvArr.forEach((el: any) => {
                  el.onclick = () => {
                    window.open(
                      `https://uri.amap.com/marker?position=${selectElem.value.lng},${selectElem.value.lat}`,
                    );
                  };
                });
              });
            });
            setFitView();
          }
        });
      }
      const [registerDicFormModal, { openModal: openDicFormModal }] = useModal();

      function openDic() {
        openDicFormModal(true, {
          record: selectElem.value,
        });
      }
      //新建分组
      const [registerGroupModal, { openModal: openGroupModal }] = useModal();
      function addGroup() {
        openGroupModal(true);
      }
      function editGroup(record: Recordable) {
        openGroupModal(true, {
          record,
          isUpdate: true,
        });
      }
      function delGroupData(record: Recordable) {
        delGroup(record?.groupId).then(() => {
          getTree();
        });
      }
      function selectorg(e) {
        orgUid.value = e;
        getTree();
      }

      function stateChange(key) {
        if (key == '3') {
          onlinestate.value = 'OFFLINE';
        } else if (key == '2') {
          onlinestate.value = 'ONLINE';
        } else {
          onlinestate.value = null;
        }
        getTree();
      }

      //搜车
      function onSearch(keyword) {
        console.log(keyword);
        getTree();
      }

      function handleCarSuccess() {
        getTree();
      }
      function lookDetail(record: Recordable) {
        openDetailModal(true, {
          record,
        });
      }
      //移动分组
      const [registerMoveModal, { openModal: openMoveModal }] = useModal();
      function moveGroup(record: Recordable) {
        openMoveModal(true, {
          record,
        });
      }
      onMounted(() => {
        // getTree();
        getorg();
        getvehdata();
      });
      return {
        moveGroup,
        lookDetail,
        iconMap,
        addGroup,
        editGroup,
        delGroupData,
        registerGroupModal,
        registerMoveModal,
        registerDicFormModal,
        handleCarSuccess,
        select,
        carList,
        staticMap,
        containerRef,
        total,
        online,
        activeKey,
        isMap,
        modelKey,
        VehData,
        playObj,
        orgOptions,
        fieldNames,
        orgfieldNames,
        orgUid,
        onlinestate,
        selectedVeh,
        List,
        vin,
        chartOptions,
        initMap,
        getvehdata,
        initTrackMap,
        handleChange,
        handleClick,
        refresh,
        stateChange,
        onSearch,
        getorg,
        getDistribution,
        selectorg,
        getTree,
        stopChannel,
        registerDetailModal,
      };
    },
  });
</script>

<style scoped lang="less">
  .home_div {
    position: relative;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: #fff;
  }

  .toggle_arrow {
    color: #fff;
    font-size: 16px;
    cursor: pointer;
  }

  .layout {
    display: flex;
    position: absolute;
    top: -36px;
    right: 10px;
    width: 120px;
  }

  .mapcard {
    display: flex;
    width: 100%;

    .width_new {
      position: relative;
      flex: 1;
      height: 100%;

      .legend {
        position: absolute;
        z-index: 999;
        top: 10px;
        left: 370px;
        padding: 0 0 0 5px;
        border: solid 1px #ddd;
        border-radius: 2px;
        background-color: #fcfcfc;
        color: #000;

        .col {
          display: flex;
          margin: 5px 0;
          margin-right: 15px;
          float: left;
          font-size: 13px;
          line-height: 19px;
          text-align: center;

          .icon {
            width: 19px;
            height: 19px;
            margin-right: 5px;
          }
        }
      }
    }

    .board {
      width: 420px;
      height: calc(100vh - 150px);
      margin-left: 10px;
      padding-bottom: 40px;
      overflow-y: auto;
      border-left: solid 1px #eee;
      background-color: #f8f8f8;

      .subtitle {
        color: #666;
      }

      .total {
        margin-bottom: 20px;
        padding: 10px 20px;
        background-color: #fff;

        .all {
          border-bottom: solid 1px #eee;

          .data {
            color: #1c96fa;
            font-size: 48px;
            font-weight: 500;
          }
        }

        .flex {
          text-align: center;

          .leftcard {
            flex: 1;
            height: 190px;
            margin-top: 10px;
            padding-top: 55px;

            .number {
              font-size: 36px;
              font-weight: 600;
            }
          }

          .righttcard {
            flex: 1;

            .col {
              margin: 5px 10px;
              border-bottom: solid 1px #eee;

              .number {
                padding-bottom: 5px;
                font-size: 30px;
                line-height: 1.1;
              }
            }
          }
        }
      }

      .miliege {
        margin-bottom: 20px;
        padding: 10px 20px;
        background-color: #fff;

        .m_top {
          display: flex;

          .btngroup {
            margin-left: 50px;
          }
        }

        .data {
          margin-top: 12px;
          font-size: 34px;

          .subtitle {
            font-size: 24px;
          }
        }

        .chart {
          width: 100%;
          height: 300px;
        }

        .m_bottom {
          padding-top: 10px;
          border-top: solid 1px #eee;
          color: #666;
        }
      }

      .top10 {
        margin-bottom: 20px;
        padding: 10px 20px;
        background-color: #fff;

        .m_top {
          display: flex;

          .btngroup {
            margin-left: 50px;
          }
        }

        .t_table {
          background-color: #fff;
        }
      }
    }
  }

  .middle {
    position: relative;
    height: 700px;
    overflow: hidden;

    .btnbox {
      position: absolute;
      bottom: 50px;
      width: 100%;
      height: 52px;
      border-radius: 8px;
      background-color: #eee;
    }

    .content {
      overflow: hidden;

      .v_list {
        width: 300px;
        float: left;
      }

      .video {
        width: 100%;
        height: calc(100vh - 350px);
      }

      .video_300 {
        width: calc(100% - 320px);
      }
    }

    .btn {
      margin: 10px;
    }
  }

  .track {
    width: 100%;
    height: calc(100vh - 150px);
    padding: 0;
    overflow: hidden;
  }

  .playbox {
    position: relative;
    height: 700px;
    margin: 0;
    overflow: hidden;

    .videobox {
      height: 650px;
      overflow: hidden;
    }

    .btnbox {
      position: absolute;
      bottom: 50px;
      width: 100%;
      height: 52px;
      border-radius: 8px;
      background-color: #eee;
    }

    .btn {
      margin: 10px;
    }
  }

  .video_1 {
    width: 100%;
    height: calc(100vh - 350px);
    margin: 0;
  }

  .video_2 {
    width: 48%;
    height: 280px;
    margin: 5px 1%;
    float: left;
  }

  .video_3 {
    width: 31%;
    height: 185px;
    margin: 5px 1%;
    float: left;
  }

  .left {
    position: absolute;
    z-index: 999;
    top: 10px;
    left: 10px;
    width: 350px;
    height: calc(100% - 20px);
    border-right: none !important;
    background: #fff;
    box-shadow: 0 2px 5px #ccc;
  }

  .tit {
    height: 18px;
    margin: 5px 0 0 5px;
    padding-left: 5px;
    border-left: solid 3px #0421bc;
    color: #0421bc;
    font-weight: bold;
    line-height: 18px;
  }

  .videolist {
    box-sizing: border-box;
    width: 300px;
    height: 36px;
    margin: 10px;
    padding: 0 5px;
    border-radius: 6px;
    background-color: #f2faff;
    box-shadow: 0 0 5px #ccc;
    line-height: 36px;
    cursor: pointer;
  }

  .scrollbox {
    width: 100%;
    max-height: calc(100vh - 280px);
    overflow: hidden auto;
  }

  .top {
    padding: 10px;

    .title {
      display: flex;
      height: 30px;
      color: #666;
      font-size: 15px;
      font-weight: 600;
      line-height: 30px;

      .tit {
        flex: 1;
      }
    }

    .org {
      display: flex;
      align-items: center;
      margin: 10px 0;
      gap: 10px;

      .anticon {
        font-size: 14px;
        cursor: pointer;
      }
    }
  }

  .vehbox {
    display: flex;
    padding: 15px;
    border-bottom: solid 1px #eee;
    cursor: pointer;
  }

  .vehbox:hover {
    background-color: #f2faff;
    color: #273352;
    font-weight: bold;
  }

  .selected {
    background-color: #f2faff;
    color: #273352;
    font-weight: bold;
  }

  .right {
    width: 100%;
    height: 100%;
    overflow: hidden;

    .topbox {
      display: flex;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
    }

    .table {
      display: flex;
      position: absolute;
      z-index: 999;
      bottom: -10px;
      left: 0;
      width: calc(100% - 20px);
      margin-right: 10px;
      margin-left: 10px;
      border-top-left-radius: 2px;
      border-top-right-radius: 2px;
      background-color: rgb(0 0 0 / 30%);

      .tabs {
        position: relative;
        flex: 1;
        width: 100px;
        padding: 0 10px;
        border-top-left-radius: 2px;
        border-top-right-radius: 2px;
        background-color: #fff;

        .ant-tabs {
          margin-bottom: 10px;
        }

        .arrow {
          display: flex;
          position: absolute;
          top: 10px;
          right: 10px;
          align-items: center;
          justify-content: center;
          width: 35px;
          height: 25px;
          border-radius: 2px;
          background-color: #0421bc;
        }
      }
    }

    .up {
      bottom: 0;
    }

    .down {
      bottom: 0;
    }
  }

  .ant-picker-range {
    margin: 10px 0 0 10px;
  }

  .tree_box {
    display: flex;
    align-items: center;
    overflow: hidden;

    .app-iconify {
      margin-right: 5px;
    }

    &_name {
      flex: 1;
      width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .speed {
      flex-shrink: 0;
      margin-left: 5px;
      color: #666;
    }

    .speed.online {
      color: #12ed80;
    }
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .menu_list {
    .item {
      padding: 5px 10px;
      color: #000;
      cursor: pointer;
    }

    .item:hover {
      background: #1c96fa;
    }
  }

  .ant-tabs > .ant-tabs-nav .ant-tabs-nav-wrap {
    padding-left: 35px !important;
  }

  .amap-info-content {
    padding: 0 !important;
  }
</style>
