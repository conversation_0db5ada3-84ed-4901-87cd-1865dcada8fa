<template>
  <div class="home_div">
    <div class="left">
      <div class="top">
        <div class="org">
          <a-tree-select
            placeholder="请选择机构"
            allowClear
            treeDefaultExpandAll
            style="width: 100%"
            :treeData="orgOptions"
            @change="selectorg"
            :field-names="orgfieldNames"
          />
          <PlusCircleOutlined title="新建分组" @click="addGroup" />
        </div>
        <!-- <div class="search">
          <a-input-search
            v-model:value="vin"
            placeholder="设备名称"
            enter-button
            @search="onSearch"
          />
        </div> -->
        <Tabs v-model:activeKey="activeKey" @change="stateChange">
          <TabPane key="1" :tab="'全部(' + (VehData.all || 0) + ')'" />
          <TabPane key="2" :tab="'在线(' + (VehData.online || 0) + ')'" />
          <TabPane key="3" :tab="'离线(' + (VehData.offline || 0) + ')'" />
        </Tabs>
        <div class="scrollbox">
          <a-tree
            v-if="List.length > 0"
            :defaultExpandAll="true"
            :fieldNames="fieldNames"
            :treeData="List"
            @select="select"
            @check="select"
            v-model:checkedKeys="carList"
            checkable
          >
            <template #title="{ dataRef }">
              <div class="tree_box" v-if="dataRef.isGroup">
                <div class="tree_box_name">{{ dataRef.name }}</div>
                <div class="actions" v-if="dataRef.name !== '默认分组'">
                  <Icon icon="clarity:note-edit-line" @click.native="editGroup(dataRef)" />

                  <Popconfirm
                    title="确认删除当前分组"
                    ok-text="确认"
                    cancel-text="取消"
                    @confirm="delGroupData(dataRef)"
                  >
                    <Icon icon="ant-design:delete-outlined" />
                  </Popconfirm>
                </div>
              </div>
              <div class="tree_box" v-else>
                <Icon
                  :icon="iconMap[dataRef.useWay]"
                  v-if="dataRef.useWay"
                  :title="dataRef.useWay"
                  style="margin-left: 5px; color: #0421bc"
                />
                <div class="tree_box_name">{{ dataRef.name }}</div>

                <div class="speed" :class="{ online: dataRef.online == '在线' }">
                  {{ dataRef.gpsSpeed || '0.00' }} km/h
                  <EnvironmentOutlined />
                  <Tooltip placement="right" color="#FFF">
                    <template #title>
                      <div class="menu_list">
                        <div class="item" @click="changeName(dataRef)">修改名称</div>
                        <div class="item" @click="lookHistory(dataRef)">轨迹回放</div>
                        <div class="item">下发指令</div>
                        <div class="item" @click="openFence(dataRef)">围栏设置</div>
                        <div class="item" @click="showFence(dataRef)">查看围栏</div>
                        <div class="item" @click="lookDetail(dataRef)">查看详情</div>
                        <div class="item" @click="moveGroup(dataRef)">移动到</div>
                      </div>
                    </template>
                    <MoreOutlined style="color: #000" />
                  </Tooltip>
                </div>
              </div>
            </template>
          </a-tree>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="topbox">
        <div class="mapcard">
          <div ref="containerRef" id="container" class="width_new">
            <div class="legend">
              <div class="col">
                <img class="icon" src="../../assets/images/online.png" />
                在线
              </div>
              <div class="col">
                <img class="icon" src="../../assets/images/offline.png" />
                离线
              </div>
              <div class="col">
                <img class="icon" src="../../assets/images/alarm.png" />
                报警
              </div>
              <div class="col" style="margin-right: 0">
                <img class="icon" src="../../assets/images/nosig.png" />
                无效定位
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <DicFormModal @register="registerDicFormModal" @success="handleCarSuccess" />
    <DeviceDetail @register="registerDetailModal" @success="handleCarSuccess" />
    <AddGroup @register="registerGroupModal" @success="handleCarSuccess" />
    <MoveModal @register="registerMoveModal" @success="handleCarSuccess" />
    <AddFenceFormModal @register="registerFenceFormModal" @success="handleCarSuccess" />
    <ChangeModal @register="registerChangeModal" @success="handleCarSuccess" />
    <AlarmModal @register="registerAlarmModal" />
    <FenceAlarmModal @register="registerFenceAlarmModal" />
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    ref,
    onMounted,
    h,
    onDeactivated,
    watch,
    computed,
    nextTick,
  } from 'vue';
  import DicFormModal from './dicFormModal.vue';
  import AddGroup from './components/AddGroupModal.vue';
  import { Tree, TreeSelect, Popconfirm, Tabs, Tooltip } from 'ant-design-vue';
  import MoveModal from './components/moveModal.vue';
  import AddFenceFormModal from './components/AddFenceFormModal.vue';
  import DeviceDetail from './components/deviceDetail.vue';
  import ChangeModal from './components/changeModal.vue';
  import AlarmModal from './components/alarmModal.vue';
  import FenceAlarmModal from './components/fenceAlarmModal.vue';
  import gisOnlinePng from '@/assets/images/online.png';
  import gisOffPng from '@/assets/images/offline.png';
  import nosigPng from '@/assets/images/nosig.png';
  import alarmPng from '@/assets/images/alarm.png';
  import {
    getDeviceGroupTree,
    delGroup,
    getlogresult,
    sendCommand,
    getVehData,
  } from '@/api/vehicle/vehlist';
  import { getOrgTreeOptions } from '@/api/passport/org';
  import { EnvironmentOutlined, PlusCircleOutlined, MoreOutlined } from '@ant-design/icons-vue';
  import { type UnifiedMarker, useMap, MapProvider } from '@/hooks/web/useMap';
  import Icon from '@/components/Icon/Icon.vue';
  import { useModal } from '@/components/Modal';
  import { useRoute, useRouter } from 'vue-router';
  import { getFenceList } from '@/api/vehicle/fence';

  export default defineComponent({
    name: 'Dashboard',
    components: {
      Popconfirm,
      DicFormModal,
      DeviceDetail,
      PlusCircleOutlined,
      EnvironmentOutlined,
      Icon,
      ATree: Tree,
      ATreeSelect: TreeSelect,
      Tabs,
      MoveModal,
      TabPane: Tabs.TabPane,
      MoreOutlined,
      Tooltip,
      AddGroup,
      AddFenceFormModal,
      ChangeModal,
      AlarmModal,
      FenceAlarmModal,
    },

    setup() {
      const route = useRoute();
      const router = useRouter();
      const iconMap = ref({
        轿车: 'emojione-monotone:oncoming-automobile',
        货车: 'fa:truck',
        客车: 'fa:bus',
        出租车: 'fa:taxi',
        摩托车: 'fa:motorcycle',
        人: 'fa:user',
        挖掘机: 'mdi:excavator',
        其他: 'material-symbols-light:devices-other-outline',
      });
      let markMap: Recordable<UnifiedMarker> = {};
      watch(
        () => route,
        () => {
          if (history.state?.code) {
            try {
              carList.value = [Number(history.state?.code)];
              setTimeout(() => {
                // 获取所有的marker
                const markers = Object.values(markMap);
                if (markers.length) {
                  // 遍历所有marker
                  for (const marker of markers) {
                    // 触发点击事件
                    triggerEvent(marker, 'click');
                  }
                }
              }, 3000);
            } catch (error) {
              console.log(error);
            }
          }
        },
        { deep: true },
      );

      const {
        provider,
        containerRef,
        getMapInstance,
        addMarker,
        getAddress,
        createInfoWindow,
        setFitView,
        clearMap,
        addToolBar,
        addControlBar,
        addHawkEye,
        addMapType,
        setMapCenter,
        setZoom,
        triggerEvent,
        addEventListener,
        addPolygon,
        addCircle,
        districtSearch,
        removeAllPolygons,
        removeAllCircles,
      } = useMap(
        {
          center: [116.33, 39.9],
          zoom: 5,
        },
        initMap,
      );

      let playObj = ref({} as any);

      let timer: any;
      let chartOptions = ref({} as any);

      let carList = ref([] as any);

      let allDevice: any = ref([]);

      const staticMap = ref({} as any);

      chartOptions.value = {
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            smooth: true,
            areaStyle: {},
          },
        ],
      };

      let total = ref('15');
      let online = ref('2');
      let onlinestate = ref(null as any);

      let activeKey = ref('1');

      let VehData = ref({
        all: 0,
        total: 0,
        online: 0,
        offline: 0,
      });

      let isMap = ref(true);

      const fieldNames = {
        key: 'id',
        title: 'name',
        children: 'devices',
        icon: renderIcon(),
      };
      const orgfieldNames = {
        label: 'name',
        value: 'uid',
      };

      let orgUid = ref('');
      let vin = ref('');

      let List = ref([] as any);

      let orgOptions = ref([] as any);

      function renderIcon() {
        return '哈哈';
      }

      function handleClick(e) {
        console.log(e.target.value);
        if (e.target.value == 'map') {
          isMap.value = true;
        } else {
          isMap.value = false;
        }
      }

      function getorg() {
        getOrgTreeOptions().then((res) => {
          orgOptions.value = res;
        });
      }

      function refresh() {
        getTree();
        getvehdata();
      }

      function getvehdata() {
        getVehData().then((res) => {
          VehData.value = res;
        });
      }

      function getTree() {
        getDeviceGroupTree({
          online: onlinestate.value,
          orgUid: orgUid.value,
        }).then((res) => {
          console.log('TreeData:', res);
          // if (!res.length) return;
          // let defaultMap = res[res.length - 1];
          // let list = res.slice(0, -1);
          // list.unshift(defaultMap);
          List.value = (res || []).map((v) => ({
            ...v,
            name: v.groupName,
            isGroup: true,
          }));
          allDevice.value = List.value.reduce((arr, item) => {
            return arr.concat(item.devices);
          }, []);
          getDistribution();
        });
      }
      function select(veh, e) {
        let data: any = {};
        if (e.node.isGroup) {
          carList.value = e.checked ? e.node.devices.map((v) => v.id) : [];
          data = e.node.devices[0];
        } else {
          data = e.node;
        }
        if (e.checked === false) return;
        console.log('选中', e.node);
        //如果是地图模式
        if (data.lng && data.lat) {
          setMapCenter([data.lng, data.lat]);
          setZoom(14);
          triggerEvent(markMap[data.id], 'click', data);
        }
      }

      function stopChannel(channel) {
        sendCommand('realtime-video-upload-control', playObj.value.deviceSn, {
          channelNo: channel,
          commandType: 0,
        })
          .then((res) => {
            console.log('查询下发结果');
            if (res.success == true) {
              console.log('查询下发结果');
              getResult(res.object);
            }
          })
          .catch(() => {
            stopChannel(channel);
          });
      }
      //查询指令结果
      function getResult(id) {
        getlogresult(id).then((result) => {
          if (result.replyStatus == '等待应答') {
            timer = setTimeout(() => {
              getResult(id);
            }, 2000);
          } else if (result.replyStatus == '应答结束' && result.resultStatus == '成功') {
            timer = null;
            clearTimeout(timer);
          }
        });
      }

      function handleChange(e) {
        console.log('e~~~~~~~~~~~~~~~', e);
      }

      function initMap() {
        addToolBar();
        addControlBar();
        addHawkEye();
        addMapType();

        getTree();

        // infoWindow事件绑定
        document.body.addEventListener('click', (e) => {
          const el = e.target as HTMLImageElement;
          console.log('click~~~', e, el);
          // 轨迹
          if (el && el.className === 'openDic') {
            openDic();
          }

          // 围栏
          if (el && el.className === 'openFence') {
            const record: any = {
              id: el.dataset.id,
              lng: el.dataset.lng,
              lat: el.dataset.lat,
            };
            openFence(record);
          }
          // 查看围栏
          if (el && el.className === 'showFence') {
            const record: any = {
              id: el.dataset.id,
            };
            showFence(record);
          }
          // 详情
          if (el && el.className === 'openDetail') {
            const record: any = el.dataset.record ? JSON.parse(el.dataset.record) : {};
            lookDetail(record);
          }

          // 轨迹回放
          if (el && el.className === 'goLocation') {
            lookHistory(el.dataset.id!);
          }

          // 报警历史
          if (el && el.className === 'alarmBtn') {
            openAlarm();
          }

          // 围栏报警历史
          if (el && el.className === 'fenceAlarmBtn') {
            openFenceAlarm();
          }

          //街景地图
          if (el && el.className === 'map_env') {
            if (provider.value === MapProvider.AMAP) {
              window.open(
                `https://uri.amap.com/marker?position=${el.dataset.lng},${el.dataset.lat}`,
              );
            } else {
              window.open(
                `https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=${el.dataset.lat},${el.dataset.lng}&heading=210&pitch=10&fov=80`,
              );
            }
          }
        });
      }
      const selectElem = ref();
      const [registerDetailModal, { openModal: openDetailModal }] = useModal();
      function getDistribution() {
        // 清除地图上所有添加的覆盖物
        clearMap();
        markMap = {};
        allDevice.value.forEach(async (elem: any) => {
          console.log('all device : ', elem);
          if (elem.lng != null && elem.lat != null) {
            elem.gpsPosition = await getAddress([elem.lng, elem.lat]);
            let icon;
            if (elem.online == '在线') {
              icon = gisOnlinePng;
            } else {
              if (elem.Effective !== 'Effective') {
                icon = nosigPng;
              } else {
                icon = gisOffPng;
              }
            }
            if (elem.alarmCount > 0) {
              icon = alarmPng;
            }
            const marker = addMarker([elem.lng, elem.lat], {
              icon: {
                imageSize: [50, 50],
                url: icon,
                size: [50, 50],
              },
              offset: [0, -5],
              extData: elem,
            });
            if (marker) {
              markMap[elem.id] = marker;

              addEventListener(marker, 'click', function (e) {
                console.log('点击maker~~~', e);
                const extData = e.target ? e.target.getExtData() : e;
                selectElem.value = extData;
                // 实例化信息窗体
                var content = `<div style="padding:6px;padding-left:10px;line-height:1.5;width:400px;font-size:12px">
              <div style="font-weight:bold;font-size:14px">${extData.deviceCustomer.license || extData.name || ''}</div>
              <div style="font-weight:bold;font-size:12px;display:flex;align-items:center;justify-content: space-between;">
                <span>IMEI：${extData.deviceSn}</span>
              </div>
              <ul style="display:flex;flex-wrap:wrap;">
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/user.png" width="17" title="车主">
                  <span style="margin-left:5px;">${extData.deviceCustomer.driverName}</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/wifi.png" width="17" title="GPS时间">
                  <span style="margin-left:5px;">${extData.gpsTime}</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/${extData.location == '有效定位' ? 'location_on' : 'location'}.png" width="17" title="定位">
                  <span style="margin-left:5px;color:${extData.location == '有效定位' ? '#418F02' : '#000'}">${extData.location}</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/modal.png" width="17" title="类型">
                  <span style="margin-left:5px;">${extData.deviceModelName}</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/${extData.gpsSpeed ? 'speed_on' : 'speed'}.png" width="17" title="速度">
                  <span style="margin-left:5px;color:${extData.gpsSpeed ? '#418F02' : '#000'}">${extData.gpsSpeed || 0}km/h</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/${extData.online == '在线' ? 'online_on' : 'online'}.png" width="17" title="在线状态">
                  <span style="margin-left:5px;color:${extData.online == '在线' ? '#418F02' : '#000'}">${extData.online}</span>
                </li>
                <li style="width:50%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/lnglat.png" width="17" title="经纬度">
                  <span style="margin-left:5px;">${extData.lat + ',' + extData.lng}</span>
                </li>
                <li style="width:100%;height:32px;line-height:32px;display:flex;align-items:center;">
                  <img src="/public/images/icon/location_gps.png" width="17" title="当前位置">
                  <span style="margin-left:5px;">${extData.gpsPosition || '-'}</span>
                </li>
              </ul>
              </div>
              <div id="map-action-group" style='width: 100%;height:40px;display:flex;align-items:center;gap:30px;padding:6px;cursor:pointer;background:#F6F7F9'>
                <img class="goLocation" src="/public/images/icon/hf.png" width="20" title="轨迹" data-id="${extData.id}">
                <img class="openDic" src="/public/images/icon/zl.png" width="20" title="指令">
                <img class="openDetail" src="/public/images/icon/xq.png" width="20" title="详情" data-record="${JSON.stringify(extData || {})}">
                <img class="alarmBtn" src="/public/images/icon/alarm.png" width="20" title="报警" data-id="${extData.id}">
                <img class="fenceAlarmBtn" src="/public/images/icon/fence_alarm.png" width="19" title="围栏报警">
                <img class="openFence" src="/public/images/icon/wl.png" width="19" title="围栏设置" data-id="${extData.id}" data-lng="${extData.lng}" data-lat="${extData.lat}">
                <img class="map_env" src="/public/images/icon/map.png" width="20" title="位置" data-lng="${extData.lng}" data-lat="${extData.lat}">
                <img class="showFence" src="/public/images/icon/wl.png" width="20" title="查看围栏" data-id="${extData.id}">
              </div>
              `;

                const infoWindow = createInfoWindow({
                  content,
                  offset: [25, -5],
                });

                console.log('marker position : ', marker?.getPosition());
                infoWindow?.open(getMapInstance()!, marker?.getPosition());
              });
            }
            setFitView();
          }
        });
      }
      //指令
      const [registerDicFormModal, { openModal: openDicFormModal }] = useModal();

      function openDic() {
        openDicFormModal(true, {
          record: selectElem.value,
        });
      }
      //围栏
      const [registerFenceFormModal, { openModal: openFenceModal }] = useModal();

      function openFence(record: Recordable) {
        openFenceModal(true, {
          record,
        });
      }
      //查看围栏
      let overlays: any = [];
      async function showFence(record: Recordable) {
        if (overlays.length) {
          removeAllPolygons();
          removeAllCircles();
          overlays = [];
          return;
        }
        console.log(record);
        let params = {
          pageIndex: 1,
          pageSize: 10000,
          order: { property: 'id', direction: 'ASC' },
          criteria: {
            deviceId: record.id,
            status: 'YES',
          },
        };
        const res = await getFenceList(params);
        console.log('围栏', res);
        (res.data || []).forEach((e) => {
          let fenceJson = JSON.parse(e.fenceJson || '[]');
          if (e.fenceType === 'AREA' && e.districtCodes) {
            drawDistricts(e.districtCodes);
          } else {
            if (fenceJson.length) drawFence(fenceJson, e.fenceType);
          }
        });
      }
      //绘制围栏
      function drawFence(fenceJson: any, type: string) {
        // 根据数据显示已保存的围栏
        if (type === 'POLYGON') {
          fenceJson = fenceJson.filter((v) => Array.isArray(v));
          console.log('fenceJson', fenceJson);
          const polygon = addPolygon(fenceJson, {
            fillColor: '#00b0ff',
            strokeColor: '#80d8ff',
            strokeOpacity: 1,
            fillOpacity: 0.5,
            strokeWeight: 1,
            amapOptions: {
              strokeDasharray: [5, 5],
            },
          });

          // distributionMap.setFitView([polygon]);
          setFitView([polygon]);
          overlays.push(polygon);
          fenceJson = [];
        } else if (fenceJson.length && type === 'CIRCULAR') {
          let circleRadius = fenceJson.filter((v) => typeof v === 'number');
          const circle = addCircle(fenceJson[0], circleRadius[0] || 0, {
            fillColor: '#00b0ff',
            strokeColor: '#80d8ff',
          });
          setFitView([circle]);
          overlays.push(circle);
        }
      }
      // 绘制行政区
      function drawDistricts(districtCodes: any) {
        if (!districtCodes) return;
        let codesArray = Array.isArray(districtCodes) ? districtCodes : districtCodes.split(',');

        if (codesArray.length) {
          districtSearch(codesArray[0], {
            level: 'district',
            subdistrict: 0,
            extensions: 'all',
          }).then((result) => {
            if (result.status === 'complete' && result.info === 'OK') {
              let bounds = result.districtList[0].boundaries;
              if (bounds) {
                let polygons: any[] = [];
                for (let i = 0; i < bounds.length; i++) {
                  polygons.push(
                    addPolygon(bounds[i], {
                      fillColor: '#00b0ff',
                      strokeColor: '#80d8ff',
                      strokeOpacity: 1,
                      fillOpacity: 0.5,
                      strokeWeight: 1,
                      amapOptions: {
                        strokeDasharray: [5, 5],
                      },
                    }),
                  );
                }
                overlays = polygons;
                setFitView(polygons);
              }
            }
          });
        }
      }
      //新建分组
      const [registerGroupModal, { openModal: openGroupModal }] = useModal();
      function addGroup() {
        openGroupModal(true);
      }
      function editGroup(record: Recordable) {
        openGroupModal(true, {
          record,
          isUpdate: true,
        });
      }
      function delGroupData(record: Recordable) {
        delGroup(record?.groupId).then(() => {
          getTree();
        });
      }
      function selectorg(e) {
        orgUid.value = e;
        getTree();
      }

      function stateChange(key) {
        if (key == '3') {
          onlinestate.value = 'OFFLINE';
        } else if (key == '2') {
          onlinestate.value = 'ONLINE';
        } else {
          onlinestate.value = null;
        }
        getTree();
      }

      function handleCarSuccess() {
        getTree();
      }
      function lookDetail(record: Recordable) {
        openDetailModal(true, {
          record,
        });
      }
      //移动分组
      const [registerMoveModal, { openModal: openMoveModal }] = useModal();
      function moveGroup(record: Recordable) {
        openMoveModal(true, {
          record,
        });
      }
      //轨迹回放
      function lookHistory(id: string | number) {
        router.push({
          path: '/location/track',
          query: { code: id },
        });
      }
      //修改名称
      const [registerChangeModal, { openModal: openChangeModal }] = useModal();
      const changeName = (record: Recordable) => {
        openChangeModal(true, {
          record,
        });
      };
      //设备报警
      const [registerAlarmModal, { openModal: openAlarmModal }] = useModal();
      const openAlarm = () => {
        openAlarmModal(true);
      };
      //围栏报警
      const [registerFenceAlarmModal, { openModal: openFenceAlarmModal }] = useModal();
      const openFenceAlarm = () => {
        openFenceAlarmModal(true);
      };
      onMounted(() => {
        // getTree();
        getorg();
        getvehdata();
      });
      return {
        moveGroup,
        lookDetail,
        iconMap,
        addGroup,
        editGroup,
        delGroupData,
        registerGroupModal,
        registerMoveModal,
        registerDicFormModal,
        handleCarSuccess,
        select,
        carList,
        staticMap,
        containerRef,
        total,
        online,
        activeKey,
        isMap,
        VehData,
        playObj,
        orgOptions,
        fieldNames,
        orgfieldNames,
        orgUid,
        onlinestate,
        List,
        vin,
        chartOptions,
        initMap,
        getvehdata,
        handleChange,
        handleClick,
        refresh,
        stateChange,
        getorg,
        getDistribution,
        selectorg,
        getTree,
        stopChannel,
        registerDetailModal,
        registerFenceFormModal,
        openFence,
        showFence,
        lookHistory,
        registerChangeModal,
        changeName,
        registerAlarmModal,
        registerFenceAlarmModal,
      };
    },
  });
</script>

<style scoped lang="less">
  .home_div {
    position: relative;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: #fff;
  }

  .toggle_arrow {
    color: #fff;
    font-size: 16px;
    cursor: pointer;
  }

  .layout {
    display: flex;
    position: absolute;
    top: -36px;
    right: 10px;
    width: 120px;
  }

  .mapcard {
    display: flex;
    width: 100%;

    .width_new {
      position: relative;
      flex: 1;
      height: 100%;

      .legend {
        position: absolute;
        z-index: 999;
        top: 10px;
        left: 370px;
        padding: 0 0 0 5px;
        border: solid 1px #ddd;
        border-radius: 2px;
        background-color: #fcfcfc;
        color: #000;

        .col {
          display: flex;
          margin: 5px 0;
          margin-right: 15px;
          float: left;
          font-size: 13px;
          line-height: 19px;
          text-align: center;

          .icon {
            width: 19px;
            height: 19px;
            margin-right: 5px;
          }
        }
      }
    }

    .board {
      width: 420px;
      height: calc(100vh - 150px);
      margin-left: 10px;
      padding-bottom: 40px;
      overflow-y: auto;
      border-left: solid 1px #eee;
      background-color: #f8f8f8;

      .subtitle {
        color: #666;
      }

      .total {
        margin-bottom: 20px;
        padding: 10px 20px;
        background-color: #fff;

        .all {
          border-bottom: solid 1px #eee;

          .data {
            color: #1c96fa;
            font-size: 48px;
            font-weight: 500;
          }
        }

        .flex {
          text-align: center;

          .leftcard {
            flex: 1;
            height: 190px;
            margin-top: 10px;
            padding-top: 55px;

            .number {
              font-size: 36px;
              font-weight: 600;
            }
          }

          .righttcard {
            flex: 1;

            .col {
              margin: 5px 10px;
              border-bottom: solid 1px #eee;

              .number {
                padding-bottom: 5px;
                font-size: 30px;
                line-height: 1.1;
              }
            }
          }
        }
      }

      .miliege {
        margin-bottom: 20px;
        padding: 10px 20px;
        background-color: #fff;

        .m_top {
          display: flex;

          .btngroup {
            margin-left: 50px;
          }
        }

        .data {
          margin-top: 12px;
          font-size: 34px;

          .subtitle {
            font-size: 24px;
          }
        }

        .chart {
          width: 100%;
          height: 300px;
        }

        .m_bottom {
          padding-top: 10px;
          border-top: solid 1px #eee;
          color: #666;
        }
      }

      .top10 {
        margin-bottom: 20px;
        padding: 10px 20px;
        background-color: #fff;

        .m_top {
          display: flex;

          .btngroup {
            margin-left: 50px;
          }
        }

        .t_table {
          background-color: #fff;
        }
      }
    }
  }

  .middle {
    position: relative;
    height: 700px;
    overflow: hidden;

    .btnbox {
      position: absolute;
      bottom: 50px;
      width: 100%;
      height: 52px;
      border-radius: 8px;
      background-color: #eee;
    }

    .content {
      overflow: hidden;

      .v_list {
        width: 300px;
        float: left;
      }

      .video {
        width: 100%;
        height: calc(100vh - 350px);
      }

      .video_300 {
        width: calc(100% - 320px);
      }
    }

    .btn {
      margin: 10px;
    }
  }

  .track {
    width: 100%;
    height: calc(100vh - 150px);
    padding: 0;
    overflow: hidden;
  }

  .playbox {
    position: relative;
    height: 700px;
    margin: 0;
    overflow: hidden;

    .videobox {
      height: 650px;
      overflow: hidden;
    }

    .btnbox {
      position: absolute;
      bottom: 50px;
      width: 100%;
      height: 52px;
      border-radius: 8px;
      background-color: #eee;
    }

    .btn {
      margin: 10px;
    }
  }

  .video_1 {
    width: 100%;
    height: calc(100vh - 350px);
    margin: 0;
  }

  .video_2 {
    width: 48%;
    height: 280px;
    margin: 5px 1%;
    float: left;
  }

  .video_3 {
    width: 31%;
    height: 185px;
    margin: 5px 1%;
    float: left;
  }

  .left {
    position: absolute;
    z-index: 999;
    top: 10px;
    left: 10px;
    width: 350px;
    height: calc(100% - 20px);
    border-right: none !important;
    background: #fff;
    box-shadow: 0 2px 5px #ccc;
  }

  .tit {
    height: 18px;
    margin: 5px 0 0 5px;
    padding-left: 5px;
    border-left: solid 3px #0421bc;
    color: #0421bc;
    font-weight: bold;
    line-height: 18px;
  }

  .videolist {
    box-sizing: border-box;
    width: 300px;
    height: 36px;
    margin: 10px;
    padding: 0 5px;
    border-radius: 6px;
    background-color: #f2faff;
    box-shadow: 0 0 5px #ccc;
    line-height: 36px;
    cursor: pointer;
  }

  .scrollbox {
    width: 100%;
    max-height: calc(100vh - 120px);
    margin-top: 12px;
    overflow: hidden auto;
  }

  .top {
    padding: 10px;

    .title {
      display: flex;
      height: 30px;
      color: #666;
      font-size: 15px;
      font-weight: 600;
      line-height: 30px;

      .tit {
        flex: 1;
      }
    }

    .org {
      display: flex;
      align-items: center;
      margin: 10px 0;
      gap: 10px;

      .anticon {
        font-size: 14px;
        cursor: pointer;
      }
    }
  }

  .vehbox {
    display: flex;
    padding: 15px;
    border-bottom: solid 1px #eee;
    cursor: pointer;
  }

  .vehbox:hover {
    background-color: #f2faff;
    color: #273352;
    font-weight: bold;
  }

  .selected {
    background-color: #f2faff;
    color: #273352;
    font-weight: bold;
  }

  .right {
    width: 100%;
    height: 100%;
    overflow: hidden;

    .topbox {
      display: flex;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
    }

    .table {
      display: flex;
      position: absolute;
      z-index: 999;
      bottom: -10px;
      left: 0;
      width: calc(100% - 20px);
      margin-right: 10px;
      margin-left: 10px;
      border-top-left-radius: 2px;
      border-top-right-radius: 2px;
      background-color: rgb(0 0 0 / 30%);

      .tabs {
        position: relative;
        flex: 1;
        width: 100px;
        padding: 0 10px;
        border-top-left-radius: 2px;
        border-top-right-radius: 2px;
        background-color: #fff;

        .ant-tabs {
          margin-bottom: 10px;
        }

        .arrow {
          display: flex;
          position: absolute;
          top: 10px;
          right: 10px;
          align-items: center;
          justify-content: center;
          width: 35px;
          height: 25px;
          border-radius: 2px;
          background-color: #0421bc;
        }
      }
    }

    .up {
      bottom: 0;
    }

    .down {
      bottom: 0;
    }
  }

  .ant-picker-range {
    margin: 10px 0 0 10px;
  }

  .tree_box {
    display: flex;
    align-items: center;
    overflow: hidden;

    .app-iconify {
      margin-right: 5px;
    }

    &_name {
      flex: 1;
      width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .speed {
      flex-shrink: 0;
      margin-left: 5px;
      color: #666;
    }

    .speed.online {
      color: #418f03;
    }
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .menu_list {
    .item {
      padding: 5px 10px;
      color: #000;
      cursor: pointer;
    }

    .item:hover {
      background: #1c96fa;
    }
  }

  :deep(.amap-info-content) {
    padding: 0 !important;
  }

  :deep(.bottom-center .amap-info-sharp) {
    border-top: 8px solid #f6f7f9 !important;
  }
</style>
