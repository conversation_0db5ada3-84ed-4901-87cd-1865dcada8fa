import { BasicColumn, FormSchema } from '@/components/Table';
import { Tag } from 'ant-design-vue';
import { h, ref } from 'vue';
import { getRoleOptions } from '@/api/passport/role';
import { func } from 'vue-types';
import { getOrgTreeOptions } from '@/api/passport/org';

type SelectOption = {
  label: string;
  value: string;
};

const memberTypeOptions = ref<SelectOption[]>([
  { value: 'ADMIN', label: '管理员' },
  { value: 'COMMON', label: '普通用户' },
]);
const ruleOptions = ref<SelectOption[]>([]);

// getRoleOptions().then((res) => {
//   ruleOptions.value = res.map((item) => ({
//     label: item.text,
//     value: item.value,
//   }));
// });

export const columns: BasicColumn[] = [
  {
    title: '登录名',
    dataIndex: 'loginName',
  },
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    title: '用户类型',
    dataIndex: 'memberType',
    width: 160,
    customRender: ({ text }) => {
      return h(
        Tag,
        {
          color: text === 'ADMIN' ? 'red' : 'blue',
        },
        {
          default: () => (text === 'ADMIN' ? '管理员' : '普通用户'),
        },
      );
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
  },
  {
    title: '手机号',
    dataIndex: 'mobile',
    width: 160,
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: 160,
  },
  {
    title: '过期时间',
    dataIndex: 'expire',
  },
  {
    title: '登录次数',
    dataIndex: 'signinCount',
    slot: 'signinCount',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'key',
    label: '关键词',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      allowClear: true,
    },
  },
  {
    field: 'roleId',
    label: '角色',
    component: 'ApiSelect',
    colProps: { span: 6 },
    defaultValue: null,
    componentProps: {
      api: getRoleOptions,
      allowClear: true,
      fieldNames: {
        label: 'text',
        key: 'value',
        value: 'value',
      },
    },
  },
];

export const accountFormSchema: FormSchema[] = [
  {
    field: 'loginName',
    label: '登录名',
    component: 'Input',
    colProps: { span: 24 },
    // helpMessage: ['本字段演示异步验证', '不能输入带有admin的用户名'],
    rules: [
      {
        required: true,
        message: '请输入登录名',
      },
    ],
  },
  {
    field: 'name',
    label: '姓名',
    component: 'Input',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'memberType',
    label: '用户类型',
    component: 'Select',
    colProps: { span: 24 },
    required: true,
    defaultValue: null,
    componentProps: {
      options: memberTypeOptions,
    },
  },
  {
    field: 'orgUid',
    label: '所属机构',
    component: 'ApiTreeSelect',
    colProps: { span: 24 },
    componentProps: {
      api: getOrgTreeOptions,
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'password',
    label: '登录密码',
    component: 'StrengthMeter',
    colProps: { span: 24 },
    required: true,
    show: false,
  },
  {
    field: 'cpwd',
    label: '确认密码',
    component: 'InputPassword',
    colProps: { span: 24 },
    required: true,
    show: false,
  },
  {
    field: 'expire',
    label: '过期时间',
    component: 'DatePicker',
    colProps: { span: 24 },
    componentProps: {
      style: 'width: 100%',
      valueFormat: 'YYYY-MM-DD',
    },
    required: true,
  },
  {
    field: 'roles',
    label: '角色类型',
    component: 'ApiSelect',
    colProps: { span: 24 },
    defaultValue: null,
    componentProps: {
      api: getRoleOptions,
      allowClear: true,
      fieldNames: {
        label: 'text',
        key: 'value',
        value: 'value',
      },
      mode: 'multiple',
    },
    required: true,
  },
  {
    field: 'email',
    label: '邮箱',
    component: 'Input',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'mobile',
    label: '手机号',
    component: 'Input',
    colProps: { span: 24 },
    required: true,
  },
];

export const resetFormSchema: FormSchema[] = [
  {
    field: 'loginName',
    label: '登录名',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      allowClear: true,
      disabled: true,
    },
  },
  {
    field: 'newPassword',
    label: '新登录密码',
    component: 'StrengthMeter',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'cpwd',
    label: '确认新登录密码',
    component: 'InputPassword',
    colProps: { span: 24 },
    required: true,
    dynamicRules: ({ values }) => {
      return [
        {
          required: true,
          validator: (_, value) => {
            if (!value) {
              return Promise.reject('不能为空');
            }
            if (value !== values.newPassword) {
              return Promise.reject('两次输入的密码不一致!');
            }
            return Promise.resolve();
          },
        },
      ];
    },
  },
];
