import { BasicColumn, FormSchema } from '@/components/Table';
import { Tag } from 'ant-design-vue';
import { getOrgTreeOptions } from '@/api/passport/org';
import { h } from 'vue';

export const columns: BasicColumn[] = [
  {
    title: '套餐名称',
    dataIndex: 'name',
    customRender: ({ record }) => {
      if (record.isCommon && record.isCommon == 'YES') {
        return h('div', [
          h(
            'span',
            {
              style: 'margin-right:10px',
            },
            {
              default: () => record.name,
            },
          ),
          h(
            Tag,
            {
              color: 'blue',
            },
            {
              default: () => '默认',
            },
          ),
        ]);
      } else {
        return record.name;
      }
    },
  },
  {
    title: '所属机构',
    dataIndex: 'orgName',
  },
  {
    title: '套餐描述',
    dataIndex: 'summary',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '套餐名称',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '套餐名称',
    required: true,
    colProps: { span: 24 },
    component: 'Input',
  },
  {
    field: 'orgId',
    label: '所属机构',
    component: 'ApiTreeSelect',
    colProps: { span: 24 },
    required: false,
    componentProps: {
      api: getOrgTreeOptions,
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'summary',
    label: '套餐描述',
    required: false,
    colProps: { span: 24 },
    component: 'Input',
  },
  // {
  //   field: 'isCommon',
  //   label: '设为默认',
  //   required: true,
  //   colProps: { span: 24 },
  //   component: 'Switch',
  //   defaultValue: 'NO',
  //   componentProps: {
  //     checkedValue: 'YES',
  //     unCheckedValue: 'NO',
  //   },
  // },
];
