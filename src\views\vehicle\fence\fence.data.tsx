/*
 * @Description  : 只是一个描述
 * @Version      : 1.0
 * <AUTHOR> <EMAIL>
 * @Date         : 2024-10-09 11:34:00
 * @LastEditors  : chen
 * @LastEditTime : 2024-10-10 15:35:39
 * @FilePath     : \special-front\src\views\vehicle\fence\fence.data.tsx
 * Copyright (C) 2024 chen. All rights reserved.
 */
import { FormSchema } from '@/components/Table';
import { getOrgTreeOptions } from '@/api/passport/org';
import dayjs from 'dayjs';

export const columns: BasicColumn[] = [
  {
    title: '围栏名称',
    dataIndex: 'name',
    width: 280,
  },
  {
    title: '围栏类型',
    dataIndex: 'fenceType',
  },
  {
    title: '关联车辆',
    dataIndex: 'vehicles',
  },
  {
    title: '关联报警',
    dataIndex: 'fenceAlert',
  },
  {
    title: '所属机构',
    dataIndex: 'orgName',
  },
  {
    title: '触发方式',
    dataIndex: 'alertEvent',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
];
export const alarmColumns: BasicColumn[] = [
  {
    title: '车架号',
    dataIndex: 'vehicleId',
    width: 200,
  },
  {
    title: '司机姓名',
    dataIndex: 'driverName',
  },
  {
    title: '司机编号',
    dataIndex: 'driverNo',
  },
  {
    title: '位置',
    dataIndex: 'gpsPosition',
  },
  {
    title: '触发方式',
    dataIndex: 'alertEvent',
  },
  {
    title: '报警信息',
    dataIndex: 'alertMessage',
  },
  {
    title: '开始时间',
    dataIndex: 'alertStart',
  },
  {
    title: '结束时间',
    dataIndex: 'alertEnd',
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    field: 'criteria.keyword',
    label: '关键字',
    component: 'Input',
    colProps: { span: 5 },
  },
  {
    field: 'criteria.name',
    label: '围栏名称',
    component: 'Input',
    colProps: { span: 5 },
  },
  {
    field: 'criteria.orgId',
    label: '所属机构',
    component: 'ApiTreeSelect',
    colProps: { span: 5 },
    componentProps: {
      api: getOrgTreeOptions,
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'criteria.status',
    label: '启用状态',
    colProps: { span: 5 },
    component: 'Select',
    componentProps: {
      options: [
        { label: '禁用', value: 'NO' },
        { label: '启用', value: 'YES' },
      ],
    },
  },
];
export const alarmFormSchema: FormSchema[] = [
  {
    field: 'criteria.vehicleId',
    label: '车架号',
    labelWidth: 80,
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'criteria.orgBranch',
    label: '所属机构',
    labelWidth: 80,
    component: 'ApiTreeSelect',
    colProps: { span: 6 },
    componentProps: {
      api: () => {},
      labelField: 'name',
      valueField: 'uid',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'criteria.range',
    label: '日期范围',
    component: 'RangePicker',
    labelWidth: 80,
    colProps: { span: 6 },
    defaultValue: [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    componentProps: {
      style: 'width: 100%;',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];
