<template>
  <div class="h-full">
    <BasicTable :search-info="searchInfo" @register="registerTable">
      <template #signinCount="{ record }">
        <Tag color="blue" class="cursor-pointer" @click="handleSignInLog(record)">{{
          record.signinCount
        }}</Tag>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">新增账号</a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              tooltip: '编辑',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'clarity:trash-line',
              color: 'error',
              tooltip: '删除',
              popConfirm: {
                title: '是否确认删除用户',
                confirm: handleRemove.bind(null, record),
              },
            },
            {
              icon: 'clarity:refresh-line',
              color: 'error',
              tooltip: '重置密码',
              popConfirm: {
                title: '是否确认重置密码',
                confirm: handleReset.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
    <AccountModal @register="registerModal" @success="handleSuccess" />
    <ResetPwdModal @register="registerResetModal" @success="resetSuccess" />
    <SignInCountModal :width="800" @register="registerSignInModal" />
  </div>
</template>
<script lang="ts">
  import { defineComponent, reactive } from 'vue';
  import { Tag } from 'ant-design-vue';

  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { getMemberList, removeMember } from '@/api/passport/member';
  import { useMessage } from '@/hooks/web/useMessage';

  import { useModal } from '@/components/Modal';
  import AccountModal from './AccountModal.vue';
  import ResetPwdModal from './ResetPwdModal.vue';
  import SignInCountModal from './SigninCountModal.vue';

  import { columns, searchFormSchema } from './account.data';

  export default defineComponent({
    name: 'MemberList',
    components: {
      BasicTable,
      AccountModal,
      TableAction,
      ResetPwdModal,
      SignInCountModal,
      Tag,
    },
    setup() {
      const [registerModal, { openModal }] = useModal();
      const [registerResetModal, { openModal: openResetModal }] = useModal();
      const [registerSignInModal, { openModal: openSignInModal }] = useModal();
      const searchInfo = reactive<Recordable>({});
      const [registerTable, { reload }] = useTable({
        title: '账号列表',
        api: getMemberList,
        rowKey: 'id',
        columns,
        formConfig: {
          labelAlign: 'left',
          schemas: searchFormSchema,
          autoSubmitOnEnter: true,
        },
        useSearchForm: true,
        showTableSetting: true,
        bordered: true,
        handleSearchInfoFn(info) {
          console.log('handleSearchInfoFn', info);
          return info;
        },
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
          slot: 'action',
        },
      });

      function handleCreate() {
        openModal(true, {
          isUpdate: false,
        });
      }

      function handleEdit(record: Recordable) {
        console.log(record);
        openModal(true, {
          record,
          isUpdate: true,
        });
      }

      function handleRemove(record: Recordable) {
        console.log(record);
        const { createMessage } = useMessage();
        removeMember(record?.uid).then((res) => {
          createMessage.success('删除成功');
          reload();
        });
      }

      function handleReset(record: Recordable) {
        openResetModal(true, {
          record,
        });
      }

      function handleSuccess() {
        reload();
      }

      function resetSuccess() {
        const { createMessage } = useMessage();
        createMessage.success('重置成功');
        reload();
      }

      function handleSignInLog(record: Recordable) {
        openSignInModal(true, {
          record,
        });
      }

      return {
        registerTable,
        registerModal,
        handleCreate,
        handleEdit,
        handleReset,
        handleSuccess,
        searchInfo,
        registerResetModal,
        openResetModal,
        resetSuccess,
        registerSignInModal,
        openSignInModal,
        handleSignInLog,
        handleRemove,
      };
    },
  });
</script>
