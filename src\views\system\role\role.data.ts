import { BasicColumn, FormSchema } from '@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '角色名称',
    dataIndex: 'name',
    width: 280,
  },
  {
    title: '角色描述',
    dataIndex: 'intro',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'key',
    label: '角色名称',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '角色名称',
    required: true,
    colProps: { span: 24 },
    component: 'Input',
  },
  {
    field: 'intro',
    label: '角色描述',
    required: true,
    colProps: { span: 24 },
    component: 'Input',
  },
];
